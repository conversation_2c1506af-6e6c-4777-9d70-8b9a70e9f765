<cfoutput>
<div id="paySchedData" data-numpaymentstouse='#local.numPaymentsToUse#' data-firstpaymentminimum='#local.firstPaymentMinimum#' data-amounttocharge='#local.amountToCharge#' data-invprofilescount='#local.subAmts.qryInvProfiles.recordcount#'></div>
<script id="paySchedData-arrPaySchedule" type="application/json">
	#serializeJSON(local.arrPaySchedule)#
</script>
<script id="paySchedData-sPayOrder" type="application/json">
	#serializeJSON(local.subAmts.payOrderArray)#
</script>
<div class="alert alert-info mb-2">
	NOTE: Saving this subscription as Billed will not retain a modified Payment Schedule. Only modify the Payment Schedule if you intend to Accept this subscription.
</div>
<input type="hidden" id="ps_upfront_amt" name="ps_upfront_amt" value="#local.firstPaymentMinimum#">
<input type="hidden" id="schedFormLoaded" name="schedFormLoaded" value="1">
<input type="hidden" id="hInvoiceProfileIDs" name="hInvoiceProfileIDs" value="#valueList(local.subAmts.qryInvProfiles.invoiceProfileID)#">
<table id="paymentScheduleTable" class="table table-sm table-borderless">
	<tr>
		<td width="5"></td>
		<td class="font-weight-bold">Invoice Due Date</td>
		<td width="5"></td>
		<td class="font-weight-bold">Amount</td>
		<td width="5"></td>
	</tr>
	<tbody id="tblPaySchRows">
	</tbody>
	<tr>
		<td width="5"></td>
		<td colspan="4" class="py-3"><a href="javascript:addMoreDates(null,null,null,true);" id="addPaymentBtnLink" class="btn btn-sm btn-secondary btn-pill">Add Scheduled Payment</a></td>
	</tr>
	<tr id="paymentTotalsRow">
		<td width="5"></td>
		<td colspan="2" class="align-left font-weight-bold">Total:</td>
		<td colspan="2" class="text-right font-weight-bold" id="paymentTotalAmt">#dollarformat(local.amountToCharge)#</td>
	</tr>
	<tr id="paymentScheduledRow">
		<td width="5"></td>
		<td colspan="2" class="align-left font-weight-bold">Scheduled:</td>
		<td colspan="2" class="text-right font-weight-bold" id="amtScheduled">#dollarformat(local.amountToCharge)#</td>
	</tr>
</table>

<script id="mc_subPaymentScheduleRowHTML" type="text/x-handlebars-template">
	<tr id="payScheduleRow{{rowid}}" class="payScheduleRow" data-rowid="{{rowid}}">
		<td width="5" class="text-right schedRowNumDisplay"></td>
		<td>
			<div class="input-group input-group-sm">
				<input type="text" name="ps_{{rowid}}_date" id="ps_{{rowid}}_date" class="form-control form-control-sm dateControl paymentScheduleDateInput" value="{{date}}" autocomplete="off">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="ps_{{rowid}}_date"><i class="fa-solid fa-calendar"></i></span>
				</div>
			</div>
		</td>
		<td width="5"></td>
		<td>
			<div class="input-group input-group-sm">
				<div class="input-group-prepend">
					<span class="input-group-text">$</span>
				</div>
				<input class="form-control form-control-sm payScheduleAmt" type="text" name="ps_{{rowid}}_amt" id="ps_{{rowid}}_amt" autocomplete="off" value="{{amt}}" onchange="checkSpread();">
			</div>
		</td>
		<td>
			{{##compare rowid '>' 1}}
				<a href="javascript:removePayScheduleDateRow({{rowid}});" title="remove"><i class="fa-solid fa-circle-xmark text-danger"></i></a>
			{{/compare}}
		</td>
	</tr>
	<cfoutput query="local.subAmts.qryInvProfiles">
		<tr id="payScheduleInvProfRow{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#" class="payScheduleInvProfRow payScheduleInvProfRow{{rowid}}">
			<td width="5"></td>
			<td colspan="2" class="pt-0 align-left" title="#local.subAmts.qryInvProfiles.invoiceProfileName#">
				<span class="font-italic small">#Left(local.subAmts.qryInvProfiles.invoiceProfileName, 20)#</span>
			</td>
			<td class="pt-0 text-right">
				<input type="hidden" id="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt" name="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt">
				<input type="hidden" id="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_appearOnThisInvoice" name="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_appearOnThisInvoice" value="false">
				<span id="span_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt" class="small"></span>
			</td>
			<td></td>
		</tr>
	</cfoutput>
</script>
</cfoutput>