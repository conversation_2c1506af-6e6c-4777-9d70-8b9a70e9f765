<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = "controller">
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event --------------------------------------------------------------------
			local.tmpRights = buildRightAssignments(
				siteResourceID=this.siteResourceID, 
				memberID=session.cfcuser.memberdata.memberID, 
				siteID=arguments.event.getValue('mc_siteInfo.siteID'));
				
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			
			local.controlPanelSettings = super.getInstanceSettings(this.appInstanceID);
			
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			this.link.copySWProgramPrompt = buildCurrentLink(arguments.event,"copySWProgramPrompt") & "&mode=direct";
			this.link.viewCertificate = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=direct";
			this.link.viewCertificatePDF = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=stream";
			
			this.link.manageSettings = buildCurrentLink(arguments.event,"manageSettings");
			this.link.message = buildCurrentLink(arguments.event,"message");
				
			// Run Assigned Method
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it.
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="listSWL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.seminarsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLList&mode=stream';
			local.objSWLSeminar = createObject("component","model.admin.seminarweb.seminarwebSWL");
			local.seminarsExportLink = buildCurrentLink(arguments.event,"exportSWLSeminars") & "&mode=stream";
			local.editSWLProgram = buildCurrentLink(arguments.event,"editSWLProgram");
			local.deleteSWProgramLink =  buildCurrentLink(arguments.event,"deleteSWProgram") & "&mode=direct";
			local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1);
			if (local.hasManageSWLRegistrantsRights) {
				local.SWLRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLRegistrants&gridmode=regsearchgrid&mode=stream";
				local.exportRegPromptLink = buildCurrentLink(arguments.event,"exportSWProgramRegPrompt") & "&gridmode=exportregsearch&swType=SWL&mode=direct";
				local.manageCreditLink = buildCurrentLink(arguments.event,"manageCredit") & "&mode=direct";
				local.viewSWLProgressLink = buildCurrentLink(arguments.event,"viewSWLProgress") & "&mode=direct";
				local.getCommunicationLink = buildCurrentLink(arguments.event,"getCommunication") & "&mode=direct";
				local.sendMaterialsLink = buildCurrentLink(arguments.event,"sendMaterials") & "&mode=direct";
				local.resendInstructionsLink = buildCurrentLink(arguments.event,"resendInstructions") & "&mode=direct";
				local.sendSWLReplayLink =  buildCurrentLink(arguments.event,"sendSWLReplayLink") & "&mode=direct";
				local.changeRegistrantPriceLink =  buildCurrentLink(arguments.event,"changeRegistrantPrice") & "&mode=direct";
				local.removeEnrollmentLink =  buildCurrentLink(arguments.event,"removeEnrollment") & "&mode=direct";

				local.transactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
				local.myRightsTransactionsAdmin = buildRightAssignments(local.transactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
				local.addSWLPaymentLink	= "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
				if (local.myRightsTransactionsAdmin.transAllocatePayment is 1)
					local.allocateSWLPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
			}

			local.pageHeading = arguments.event.getValue('mc_siteInfo.swlBrand');

			local.pDateFrom = dateFormat(dateAdd("d",-180,now()), "m/d/yyyy");
			local.pDateTo = dateFormat(dateAdd("d",30,now()), "m/d/yyyy");
			local.rDateFrom = "#month(now())#/1/#year(now())#";
			local.rDateTo = "#dateformat(now(),'m/d/yyyy')#";

			local.selectedTab = arguments.event.getTrimValue("tab","programs");
			local.lockTab = "";
			if (arguments.event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}
			local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation;
			local.participantID = local.participantData.participantID;
			local.scheduledTaskDetails = local.objSWLSeminar.getScheduledTaskDetails(local.participantID);
		</cfscript>
		
		<cfif (application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.participantData.handlesOwnPayment is 0 and local.objSWLSeminar.isNATLEParticipant(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')))> 
			<cfset local.qrySeminarsOptedIn = local.objSWLSeminar.getSeminarsOptedInByOrgcode(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.qrySeminarsOptedOut = local.objSWLSeminar.getSeminarsOptedOutByOrgcode(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfquery name="local.qrySeminars" dbtype="query">
				select seminarID, seminarName, seminarSubTitle, isPublished, dateStart, 1 as optedIn
				from [local].qrySeminarsOptedIn AS soi
				WHERE soi.isNatle = 1
					UNION
				select seminarID, seminarName, seminarSubTitle, isPublished, dateStart, 0 as optedIn
				from [local].qrySeminarsOptedOut AS soo
				WHERE soo.isNatle = 1
				order by dateStart
			</cfquery>
		</cfif>
		<cfset local.SWLFilter = CreateObject("component","model.admin.seminarweb.seminarwebSWL").getSWLFilter()>
		<cfset local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).defaultTimeZoneID>
		<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.timeZoneAbbr = local.objTZ.getTZAllFromTZID(timeZoneID=local.defaultTimeZoneID).timeZoneAbbr>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWL.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportSWLSeminars" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.keyword = arguments.event.getValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType',0);
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.dateFrom = arguments.event.getValue('fDateFrom','');
			local.dateTo = arguments.event.getValue('fDateTo','');
			local.syndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);
			
			if(len(local.dateFrom) gt 0) {
				local.dateFrom = DateFormat(local.dateFrom, "mm/dd/yyyy");
			} else {
				local.dateFrom = DateFormat(dateAdd("d",-365,now()), "m/d/yyyy");
			}
			
			if(len(local.dateTo) gt 0) {
				local.dateTo = DateFormat(local.dateTo, "mm/dd/yyyy") & " 23:59:59.997";
			} else {
				local.dateTo =  DateFormat(now(), "mm/dd/yyyy") & " 23:59:59.997";
			}
			
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "Programs#DateFormat(local.dateFrom,'yyyymmdd')#-#DateFormat(local.dateTo,'yyyymmdd')#.csv";
			
			CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getPrograms(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), 
				mode='export', dateFrom=local.dateFrom, dateTo=local.dateTo, keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType, 
				hideInactive=local.hideInactive, syndicatedOnly=local.syndicatedOnly, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWLPrograms('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editSWLProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = StructNew();
			local.seminarId = int(val(arguments.event.getTrimValue('pid',0)));
			local.swType = 'SWL';
			local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode');
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.programAdded = arguments.event.getValue('programAdded',false);
			local.lastIncompleteSectionIndex = arguments.event.getValue('lastIncompleteSectionIndex',0);

			if (local.seminarId eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");

			local.manageCreditLink = buildCurrentLink(arguments.event,"manageCredit") & "&mode=direct";
			local.viewSWLProgressLink = buildCurrentLink(arguments.event,"viewSWLProgress") & "&mode=direct";
			local.getCommunicationLink = buildCurrentLink(arguments.event,"getCommunication") & "&mode=direct";
			local.sendMaterialsLink = buildCurrentLink(arguments.event,"sendMaterials") & "&mode=direct";
			local.resendInstructionsLink = buildCurrentLink(arguments.event,"resendInstructions") & "&mode=direct";
			local.sendSWLReplayLink =   buildCurrentLink(arguments.event,"sendSWLReplayLink") & "&mode=direct";
			local.editSWLProgram = buildCurrentLink(arguments.event,"editSWLProgram") & "&pid=#local.seminarId#";
			local.editSWODProgram = buildCurrentLink(arguments.event,"editSWODProgram");
			local.uploadSWLMaterialDocsLink = buildCurrentLink(arguments.event,"uploadSWLMaterialDocument") & "&pid=#local.seminarId#&mode=stream";
			local.SWLMaterialDocsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLMaterialDocs&pid=#local.seminarId#&mode=stream";
			local.removeAllDocsConfirmationLink = buildCurrentLink(arguments.event,"removeAllSWLDocsConfirmation") & '&mode=direct&pid=#local.seminarId#';
			local.editSWBProgramLink = buildCurrentLink(arguments.event,"editSWBProgram");
			
			local.selectedTab = event.getTrimValue("tab","details");
			local.lockTab = "";
			if(event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}

			local.objAdminSWParticipants = createObject("component","seminarWebParticipants");
			local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon");
			local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors");
			local.objSWTL = CreateObject("component","model.seminarweb.SWTLTitles");
			local.objCredit = CreateObject("component","model.seminarweb.SWCredits");
			local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
			local.objSWLAdmin = CreateObject("component","seminarwebSWL");
			local.objSWForms =  createObject("component","seminarWebForm");
			local.objAdminSWCommon = createObject("component","seminarWebSWCommon");
			local.objAdminSWCategory = createObject("component","model.admin.seminarWeb.seminarWebCategories");		
			local.seminarwebSWCreditsObj = createObject("component","seminarWebSWCredits");	
			local.objSWBundles = CreateObject("component","model.seminarweb.SWBundles");
			
			local.qrySeminar = local.objSWL.getSeminarBySeminarID(seminarID=local.seminarID);
			
			if (val(local.qrySeminar.seminarID) is 0) {
				application.objCommon.redirect("#this.link.message#&message=1");
			} else {
				/* Add timezone specific display to query */
				local.qrySeminar = local.objSWL.addParsedTimeZoneToSeminars(qrySeminars=local.qrySeminar, catalogOrgCode=local.siteCode);
				local.speakerBio = local.objAuthor.getSpeakersInfoByProgramIDForCatalog(programID=local.seminarID, programType="SWL", mode="bs4");
				local.qryOutline = local.objSWTL.getTitlesAndFilesWithinSeminar(local.seminarId,local.siteCode);
				local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.seminarID, siteCode=local.siteCode);
				local.JSStruct = local.objCredit.getCreditInfoForCatalog(qryCredits=local.strCredit.qryCredit, isControlPanel=1);
				local.isFeaturedProgram = local.objSWCommon.isFeaturedProgram(catalogOrgCode=local.siteCode, programID=local.seminarID, programType="SWL");
				
				local.hasEditSWLProgramAllRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWLProgramAll') is 1;
				local.hasEditSWLProgramPublishRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWLProgramPublish') is 1;
				local.isSWProgramLocked = local.qrySeminar.lockSettings is 1;
				local.isPublisher = local.qrySeminar.publisherOrgCode EQ local.siteCode;
				
				local.hasEditRights = (local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights) AND local.isPublisher;
				local.hasManageCreditRights = local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights;
				local.hasLockSWProgramRights = local.hasEditRights AND arguments.event.getValue('mc_adminToolInfo.myRights.lockSWProgram') is 1;
				local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.siteCode);
				local.qryAssociation = local.strAssociation.qryAssociation;
				local.qrySeminarSyndicateObj = local.objAdminSWCommon.getSeminarSyndicateData(local.seminarID,local.siteCode);
				local.listProgramsLink = buildCurrentLink(arguments.event,"listSWL");
				/* inactive syndicated program */
				if (NOT local.isPublisher AND NOT local.objAdminSWCommon.isActiveOptedIntoProgram(participantID=local.qryAssociation.participantID, programID=local.seminarID, programType='SWL'))
					application.objCommon.redirect("#this.link.message#&message=1");

				if(local.programAdded AND local.lastIncompleteSectionIndex EQ 0) {
					local.qryAllSponsorsAndAuthorities = local.objCredit.getSponsorsAndAuthorities();
					var participantOrgCode = local.siteCode;
					local.qrySponsorsAndAuthorities = QueryFilter(local.qryAllSponsorsAndAuthorities, 
														function(thisRow) {
															return arguments.thisRow.creditSponsorOrgCode EQ participantOrgCode;
														});
														
					for (i = 1; i <= local.qrySponsorsAndAuthorities.recordCount; i++) {
						// Fetch each row
						local.CSALinkID = local.qrySponsorsAndAuthorities.CSALinkID[i];
						// Call the addSeminarCredit method
						local.seminarwebSWCreditsObj.addSeminarAllPublisherCredit(
							local.siteCode, 
							local.seminarID,   
							local.CSALinkID
						);
					}
				}

				if (local.hasEditRights) {
					/* Details Tab */
					local.strLearningObjectives = getLearningObjectives(programType="SWL",programID=local.seminarID, isReadOnly=local.isSWProgramLocked, orgcode=local.siteCode, adminHomeResource=arguments.event.getValue('mc_adminNav.adminHomeResource'));
					local.arrZoomWebinarLicenses = createObject("component","model.seminarWeb.SWZoomWebinar").getWebinarLicenses();
					local.uploadSWLReplayVideoLink = buildCurrentLink(arguments.event,"uploadSWLReplayVideo") & "&publisherOrgCode=#local.qrySeminar.publisherOrgCode#&mode=direct";
					local.strSponsors = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsorsSelector(resourceType="Program",
						referenceType="swlProgram", referenceID=local.seminarID, selectorID='swlProgramSponsors', readOnly=local.isSWProgramLocked);

					if (val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0) {
						local.arrSWLConfigs = [ { "ftdExt":"#this.siteResourceID#_swlprogram", "controllingReferenceID":arguments.event.getValue('mc_siteInfo.siteID'), 
							"controllingReferenceType":"swProgram", "referenceID":local.seminarID, "referenceType":"swlProgram", "resourceType":"SeminarWebAdmin", 
							"resourceTypeTitle":local.qrySeminar.seminarName, "onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWEditProgram", 
							"header":'', "ftdImgClassList":"", "readOnly":local.isSWProgramLocked
						} ];
						local.strSWLFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=local.siteCode, arrConfigs=local.arrSWLConfigs);
					}

					/* Fields Tab */
					local.arrSWLEnrollmentFields = [{ "title"="", "intro"="", "detailID"=local.seminarID, "gridext"="#this.siteResourceID#_SWLEnrollmentFields", "initGridOnLoad"=false, "controllingSRID"=this.siteResourceID, "resourceType"="SemWebCatalog", "areaName"="SWLEnrollment", "readOnly":local.isSWProgramLocked ? 1 : 0 }];
					local.strSWLEnrollmentFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrSWLEnrollmentFields);

					/* Speaker Tab */
					local.authorLabel = "Speaker";
					local.speakerListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=getSWProgramAuthors&pid=#local.seminarID#&ft=SWL';
					local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&pid=#local.seminarID#&swtype=SWL&mode=direct";
					local.speakerSendInstructionsLink = buildCurrentLink(arguments.event,"showSpeakerSendInstructions") & "&pid=#local.seminarID#&mode=direct";

					/* Subjects Tab */
					local.categoriesListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=listSWCategories&pid=#local.seminarID#&ft=SWL';
					local.addSWCategoryLink = buildCurrentLink(arguments.event,"addSWCategory") & "&pid=#local.seminarID#&swtype=SWL&mode=direct";

					/* Recordings Tab */
					if(local.qrySeminar.offerReplay and local.qrySeminar.isUploadedReplay)
						local.replayVideoLink = local.objSWL.getSWLReplayVideoLinkFromSeminarID(seminarID=local.seminarID).replayVideoLink;
					else
						local.replayVideoLink = "";
						
					if (local.qrySeminar.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and NOT local.qrySeminar.isOpen) {
						local.loadSWLZoomWebinarsRecordingsLink = buildCurrentLink(arguments.event,"loadSWLZoomWebinarsRecordings") & "&pid=#local.seminarID#&mode=stream";
					}
					local.updateSWLProgramRecordingLink = buildCurrentLink(arguments.event,"updateSWLProgramRecording");
				}
				if (!local.isPublisher) {
					/* Speaker Tab */
					local.speakerListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=getSWProgramAuthors&pid=#local.seminarID#&ft=SWL';
					local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&pid=#local.seminarID#&swtype=SWL&mode=direct&isPublisher=false";
					local.speakerSendInstructionsLink = buildCurrentLink(arguments.event,"showSpeakerSendInstructions") & "&pid=#local.seminarID#&mode=direct";
					local.authorLabel = "Speaker";
					local.enrollmentCount = local.objAdminSWCommon.getEnrollmentCount(seminarID=local.seminarID, participantID=local.qryAssociation.participantID).count;
				}

				/* Rates Tab */
				local.hasManageSWLRatesRights = local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights;
				if (local.hasManageSWLRatesRights) {
					if (NOT local.isPublisher)
						local.qryOptInSeminarRateSettings = local.objAdminSWCommon.getSWProgramRateSettings(participantID=local.qryAssociation.participantID, programID=local.seminarID, programType='SWL');
					
					local.hasSWLRateChangeRights = local.isPublisher OR local.qrySeminar.allowOptInRateChange IS 1;
					if (local.hasSWLRateChangeRights) {
						local.manageCopyRatesLink = buildCurrentLink(arguments.event,"manageCopyRates") & "&mode=direct";
						local.editSWRate = buildCurrentLink(arguments.event,"editSWRate") & "&mode=direct";
						local.copySWRate = buildCurrentLink(arguments.event,"copySWRate") & "&mode=direct";
					}
					
					local.permissionsGridAddLink = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
					local.ratesListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWProgramRates&programID=#local.seminarID#&ft=SWL&mode=stream";
					if (local.qrySeminar.handlesOwnPayment is 1) {
						if (val(local.qrySeminar.revenueGLAccountID)) {
							local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qrySeminar.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
							local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
						} else {
							local.GLAccountPath = "";
						}

						local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountIDCatalog",
							"idFldValue": val(local.qrySeminar.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses accounting settings designated GL Account.)",
							"clearBtnTxt": "Remove Override" };
						local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
					}
				}

				/* Opt-In Tab */
				local.hasManageOptInRights = local.qrySeminar.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and local.isPublisher;
				local.canOptOutOfSyndicateSite = local.qrySeminar.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and !local.isPublisher;
				local.showSWLOptInTab = local.hasManageOptInRights AND val(local.qrySeminar.allowSyndication) is 1;
				if (local.showSWLOptInTab) {
					local.qryNationalPrograms = local.objCredit.getNationalPrograms();
				}

				local.hasManageSWFormRights = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWForms') and local.isPublisher;
				if (local.hasManageSWFormRights) {
					/* Forms Tab */
					local.SWFormsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSeminarForms&sid=#local.seminarID#&mode=stream";
					local.editFormLink = buildCurrentLink(arguments.event,"editSeminarForm") & "&pid=#local.seminarID#&swtype=SWL&mode=direct";
					local.qryOrgForms = local.objSWForms.getFormsByType(siteID=local.siteID, format='E');
					local.qryOrgSurveyForms = local.objSWForms.getFormsByType(siteID=local.siteID, format='S');
				}

				local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp') is 1 or arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll') is 1);
				if (local.hasManageSWLRegistrantsRights) {
					/* Registrants Tab */
					local.SWLRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLRegistrants&mode=stream&pID=#local.seminarId#";
					local.exportRegPromptLink = buildCurrentLink(arguments.event,"exportSWProgramRegPrompt") & "&pID=#local.seminarID#&swType=SWL&mode=direct";
					local.exportRegistrantsFormResponsesLink = buildCurrentLink(arguments.event,"exportSWRegistrantsFormResponses") & "&mode=stream&swType=SWL&pID=#local.seminarID#";
					local.changeRegistrantPriceLink =  buildCurrentLink(arguments.event,"changeRegistrantPrice") & "&mode=direct";
					local.removeEnrollmentLink =  buildCurrentLink(arguments.event,"removeEnrollment") & "&mode=direct";
					local.addSWRegLink = buildCurrentLink(arguments.event,"addSWReg") & "&pID=#local.seminarId#&ft=SWL&mode=direct";

					local.qrySeminarForms = local.objSWLAdmin.getSeminarForms(seminarID=local.seminarID);

					local.transactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
					local.myRightsTransactionsAdmin = buildRightAssignments(local.transactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
					local.addSWLPaymentLink	= "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
					if (local.myRightsTransactionsAdmin.transAllocatePayment is 1)
						local.allocateSWLPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";

					local.hasRegisterForSWODRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.isConvertedToSWOD is 1;
					if (not local.qrySeminar.isOpen)
						local.regSWLRegForSWODLink = buildCurrentLink(arguments.event,"registerSWLRegistrantsForOnDemand") & "&pID=#local.seminarID#&mode=direct";
				}

				local.allowRegistrants = 0;
				local.hasToggleSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.toggleSWLRegistrantsPublish') is 1 AND local.isPublisher) OR arguments.event.getValue('mc_adminToolInfo.myRights.toggleSWLRegistrantsAll') is 1;
				if (local.hasToggleSWLRegistrantsRights)
					local.allowRegistrants = local.qrySeminar.AllowRegistrants;

				local.hasAddSWLRegistrantRights = (arguments.event.getValue('mc_adminToolInfo.myRights.addSWLRegistrantSignUp') is 1);

				local.hasManageSWLRegAttendanceRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegAttendanceSignUp') is 1 or arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegAttendanceAll') is 1) AND NOT local.qrySeminar.isOpen;
				if (local.hasManageSWLRegAttendanceRights)
					local.manageAttendanceLink = buildCurrentLink(arguments.event,"manageRegAttendance") & "&pID=#local.seminarId#&ft=SWL&mode=direct";

				local.hasMassEmailSWLRegistrantsRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher;
				if (local.hasMassEmailSWLRegistrantsRights) {
					local.massEmailInstructionsLink = buildCurrentLink(arguments.event,"massEmailInstructions") & "&pID=#local.seminarId#&ft=SWL&mode=direct";
					local.massEmailRegistrantsLink = buildCurrentLink(arguments.event,"massEmailSWRegistrants") & "&pID=#local.seminarId#&ft=SWL&mode=direct";
				}

				local.hasUploadAttendanceRights = arguments.event.getValue('mc_adminToolInfo.myRights.uploadAttendance') and local.isPublisher AND NOT local.qrySeminar.isOpen AND listFind("2,3",local.qrySeminar.providerID);
				if (local.hasUploadAttendanceRights)
					local.uploadAttendanceLink = buildCurrentLink(arguments.event,"uploadAttendance") & "&pID=#local.seminarId#&ft=SWL&mode=direct";

				local.hasMassEmailSWLCertificatesRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher and NOT local.qrySeminar.isOpen and local.qrySeminar.offerCertificate is 1;
				if (local.hasMassEmailSWLCertificatesRights)
					local.massEmailCertificatesLink = buildCurrentLink(arguments.event,"MassEmailCertificatesSWL") & "&pID=#local.seminarId#&mode=direct";

				local.hasMassEmailSWLMaterialsRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher;
				if (local.hasMassEmailSWLMaterialsRights)
					local.massEmailMaterialsLink = buildCurrentLink(arguments.event,"massEmailMaterials") & "&pID=#local.seminarId#&mode=direct";

				local.hasMassEmailSWLReplayLinkRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND local.qrySeminar.offerReplay EQ 1 AND local.qrySeminar.isUploadedReplay EQ 1 AND DateCompare(local.qrySeminar.replayExpirationDate,now()) NEQ -1;
				local.massEmailReplayLink = buildCurrentLink(arguments.event,"massEmailSWLReplayLink") & "&pID=#local.seminarId#&mode=direct";

				local.showBillingTab = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher;
				if (local.showBillingTab) {
					/* Billing Tab */
					local.cancelSWProgramLink = buildCurrentLink(arguments.event,"cancelSWProgram") & "&pID=#local.seminarID#&ft=SWL&mode=direct";
					local.swBillingLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBillingLogs&pID=#local.seminarId#&ft=SWL&mode=stream";
					local.qryMCBilling = CreateObject("component","seminarWebSWCommon").getMCBillingDetails(orgcode=local.siteCode);
				}
				local.showDownloadAllRegistrantsForIsNATLE = application.objUser.isSuperUser(cfcuser=session.cfcuser) and val(local.qrySeminar.isNATLE);
				local.showDownloadAllRegistrantsForSyndicated = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.allowSyndication AND local.isPublisher;
			}

			local.showSyndicationWarning = false;
			if (local.showSWLOptInTab AND local.hasEditRights) {
				var thesiteCode = local.qrySeminar.publisherOrgCode;
				local.qryOptInParticipants = local.objAdminSWParticipants.getParticipantsOptedIntoSeminar(seminarID=local.seminarID).filter(function(row) { return arguments.row.orgcode neq thesiteCode; });
				if (NOT local.qryOptInParticipants.recordcount)
					local.showSyndicationWarning = true;
			}

			// Initialize the array to hold issues
			local.arrSeminarSetupIssues = [];
			local.arrSeminarSetupSuccess = [];
			local.validDates = false;
			local.validRates = false;
			local.bundleIsActive = false;
			local.bundleLinksArray  = [];
			local.bundleLinks = "";
			local.sellDatesInFutureIssue = "The webinar has future sell dates in the Catalog Details section.";
			local.notSoldInCatalogIssue = "The webinar is marked to not be sold in the Catalog Details section.";
			local.sellInCatalogWithActiveBundleSuccess = "The webinar is marked to sell in the Catalog Details section, the sale dates are not expired, rates are defined, and it's included in an Active Bundle for sale.";
			local.notSellInCatalogInactiveBundleStatement = 'The webinar is marked to not sell in the Catalog Details section, but is included in an Active Bundle for sale.';
			local.sellingSuccessStatement = 'The webinar is marked to sell in the Catalog Details section, the sale dates are not expired and rates are defined.';
			local.expiredDatesIssue = 'The webinar has expired sale dates in the Catalog Details section.';
			local.ratesIssue = 'Rates have not been defined in the Catalog Details section.';
			local.publishedIssues = 'The webinar''s "Program Status" is marked "Inactive" in the Basics section.';
			local.publishedSuccessStatement = 'The "Program Status" is marked "Active" in the Basics section.';
			local.qryBundleDetails = local.objSWBundles.getBundleDetailsFromProgramID(programID = local.seminarID); 
			if (local.qryBundleDetails.recordCount > 0 ) {
				local.activeBundles = queryFilter(local.qryBundleDetails, function(row) {
									return len(row.dateCatalogStart) AND row.dateCatalogEnd GT now() AND row.status EQ 'A';
								})
				if(local.activeBundles.recordCount) {
					local.bundleIsActive = true;
					for (var i = 1; i <= local.activeBundles.recordCount; i++) {
						arrayAppend(local.bundleLinksArray, '<a href="##" onclick="editSWBProgram(' & local.activeBundles.bundleID[i] & ')">SWB-' & local.activeBundles.bundleID[i] & '</a>');
					}
					local.bundleLinks = " (" & arrayToList(local.bundleLinksArray, ", ") & ")";
				}
			}
			local.qryRates = local.objAdminSWCommon.getSeminarRatesBySeminarID(participantID=local.qryAssociation.participantID, seminarID=local.seminarID);
			// Check if the seminar is marked Inactive
			if (local.qrySeminar.isPublished eq 0) 
				arrayAppend(local.arrSeminarSetupIssues, local.publishedIssues);
			else
				arrayAppend(local.arrSeminarSetupSuccess, local.publishedSuccessStatement);
			
			//If sell is ON
			if ((local.isPublisher AND LEN(local.qrySeminar.dateCatalogStart)) OR 
				(!local.isPublisher AND local.qrySeminarSyndicateObj.sellCatalog)) {
				//Check for Dates and Rates
				if(LEN(local.qrySeminar.dateCatalogEnd) AND local.qrySeminar.dateCatalogEnd LTE CreateDateTime(Year(Now()), Month(Now()), Day(Now()), 0, 0, 0))  
					arrayAppend(local.arrSeminarSetupIssues, local.expiredDatesIssue);
				else if(LEN(local.qrySeminar.dateCatalogStart) AND local.qrySeminar.dateCatalogStart GT now())
					arrayAppend(local.arrSeminarSetupIssues, local.sellDatesInFutureIssue);
				else local.validDates = true;
				if(local.qryRates.recordCount EQ 1 AND !LEN(local.qryRates.rateID)) 
					arrayAppend(local.arrSeminarSetupIssues, local.ratesIssue);
				else local.validRates = true;
			}
			else 
			{ 	//If it is part of Bundle then check for any active Bundle. If not found then go inside
				if (local.qryBundleDetails.recordCount > 0) {
					if(local.activeBundles.recordCount EQ 0) 
						arrayAppend(local.arrSeminarSetupIssues, local.notSoldInCatalogIssue);
				}//If it is not part of Bundle then it is standalone seminar and sell is OFF
				else 
					arrayAppend(local.arrSeminarSetupIssues, local.notSoldInCatalogIssue);
			}
			if(!local.qrySeminar.AllowRegistrants)
				arrayAppend(local.arrSeminarSetupIssues, "New registrations are disallowed on the Registrants tab.");
			else
				arrayAppend(local.arrSeminarSetupSuccess, "New registrations are allowed on the Registrants tab.");	

			if (local.qryBundleDetails.recordCount > 0) { 
				if(local.activeBundles.recordCount > 0) {
					if(local.validDates AND local.validRates)
						//If Active and Selling
						arrayAppend(local.arrSeminarSetupSuccess, local.sellInCatalogWithActiveBundleSuccess & local.bundleLinks);
					else //Active bundle and not selling	
						arrayAppend(local.arrSeminarSetupSuccess, local.notSellInCatalogInactiveBundleStatement & local.bundleLinks);
				}//Inactive bundle and selling
				else if(local.validDates AND local.validRates)
					arrayAppend(local.arrSeminarSetupSuccess, local.sellingSuccessStatement);
			}//Standalone seminar  
			else if(local.validDates AND local.validRates)
				arrayAppend(local.arrSeminarSetupSuccess, local.sellingSuccessStatement);

			appendBreadCrumbs(arguments.event,{ link='', text=left(encodeForHTML(local.qrySeminar.seminarName),50) });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWL_program.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addSWReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.programID = int(val(arguments.event.getTrimValue('pid',0)));
			local.programType = arguments.event.getValue('ft','');

			local.strAddRegPerms = {};
			structInsert(local.strAddRegPerms,'SWL','addSWLRegistrantSignUp');
			structInsert(local.strAddRegPerms,'SWOD','addSWODRegistrantSignUp');
			structInsert(local.strAddRegPerms,'SWB','addBundleRegistrantSignUp');

			// security
			if (arguments.event.getValue('mc_adminToolInfo.myRights.#local.strAddRegPerms[local.programType]#',0) is 0)
				application.objCommon.redirect('#this.link.message#&message=2');

			local.qryProgram = QueryNew("programID,programName,programSubTitle,offerCredit,publisherOrgCode,programType,freeRateDisplay","integer,varchar,varchar,boolean,varchar,varchar,varchar");

			switch(local.programType) {
				case "SWL":
					local.strProgram = CreateObject("component","seminarWebSWL").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.listProgramsLink = buildCurrentLink(arguments.event,"listSWL");
					local.editProgramLink = buildCurrentLink(arguments.event,"editSWLProgram");
					local.swBrand = arguments.event.getValue('mc_siteInfo.swlBrand');
					if (local.strProgram.qrySeminar.recordCount gt 0 and QueryAddRow(local.qryProgram)) {
						QuerySetCell(local.qryProgram,"programID",local.strProgram.qrySeminar.seminarID);
						QuerySetCell(local.qryProgram,"programName",local.strProgram.qrySeminar.seminarName);
						QuerySetCell(local.qryProgram,"programSubTitle",local.strProgram.qrySeminar.seminarSubTitle);
						QuerySetCell(local.qryProgram,"offerCredit",local.strProgram.qrySeminar.offerCredit);
						QuerySetCell(local.qryProgram,"publisherOrgCode",local.strProgram.qrySeminar.publisherOrgCode);
						QuerySetCell(local.qryProgram,"programType",local.programType);
						QuerySetCell(local.qryProgram,"freeRateDisplay",local.strProgram.qrySeminar.freeRateDisplay);
					}
					break;
				case "SWOD": 
					local.strProgram = CreateObject("component","seminarWebSWOD").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.listProgramsLink = buildCurrentLink(arguments.event,"listSWOD");
					local.editProgramLink = buildCurrentLink(arguments.event,"editSWODProgram");
					local.swBrand = arguments.event.getValue('mc_siteInfo.swodBrand');
					if (local.strProgram.qrySeminar.recordCount gt 0 and QueryAddRow(local.qryProgram)) {
						QuerySetCell(local.qryProgram,"programID",local.strProgram.qrySeminar.seminarID);
						QuerySetCell(local.qryProgram,"programName",local.strProgram.qrySeminar.seminarName);
						QuerySetCell(local.qryProgram,"programSubTitle",local.strProgram.qrySeminar.seminarSubTitle);
						QuerySetCell(local.qryProgram,"offerCredit",1);
						QuerySetCell(local.qryProgram,"publisherOrgCode",local.strProgram.qrySeminar.publisherOrgCode);
						QuerySetCell(local.qryProgram,"programType",local.programType);
						QuerySetCell(local.qryProgram,"freeRateDisplay",local.strProgram.qrySeminar.freeRateDisplay);
					}
					break;
				case "SWB":
					local.objSWBundles = CreateObject("component","model.seminarweb.SWBundles");
					local.qryBundle = local.objSWBundles.getBundleByBundleID(bundleID=local.programID, orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
					local.qryItems = local.objSWBundles.getBundledItemsForCatalog(bundleID=local.programID, MCMemberID=0);
					local.listProgramsLink = buildCurrentLink(arguments.event,"listSWB");
					local.editProgramLink = buildCurrentLink(arguments.event,"editSWBProgram");
					local.swBrand = arguments.event.getValue('mc_siteInfo.swbBrand');
					if (local.qryBundle.recordCount gt 0 and QueryAddRow(local.qryProgram)) {
						QuerySetCell(local.qryProgram,"programID",local.qryBundle.bundleID);
						QuerySetCell(local.qryProgram,"programName",local.qryBundle.bundleName);
						QuerySetCell(local.qryProgram,"programSubTitle",local.qryBundle.bundleSubTitle);
						if (listValueCountNoCase(valuelist(local.qryItems.format),"SWTL") neq local.qryItems.recordcount)
							QuerySetCell(local.qryProgram,"offerCredit",1);
						else
							QuerySetCell(local.qryProgram,"offerCredit",0);
						QuerySetCell(local.qryProgram,"publisherOrgCode",local.qryBundle.publisherOrgCode);
						QuerySetCell(local.qryProgram,"programType",local.programType);
						QuerySetCell(local.qryProgram,"freeRateDisplay",local.qryBundle.freeRateDisplay);
					}
					break;
			}

			local.addSWRegLink = buildCurrentLink(arguments.event,"addSWReg");
			local.urlString = "&pid=#local.programID#&ft=#local.programType#";

			arguments.event.setValue('mainregurl',local.addSWRegLink & local.urlString);
			arguments.event.setValue('loadconfirmationurl',buildCurrentLink(arguments.event,"loadRegConfirmation") & "&mode=stream" & local.urlString);
			arguments.event.setValue('loadpaymenturl',buildCurrentLink(arguments.event,"loadRegPaymentScreen") & "&mode=stream" & local.urlString);
			arguments.event.setValue('returnregurl', local.editProgramLink & "&pid=#local.programID#&tab=registrants");
	
			// Build breadCrumb Trail 
			appendBreadCrumbs(arguments.event,{ link='#local.editProgramLink#&pid=#local.programID#&tab=registrants', text=encodeForHTML("#left(local.qryProgram.programName,60)##iif(len(local.qryProgram.programName) gt 60,DE('...'),DE(''))#") });
			appendBreadCrumbs(arguments.event,{ link='', text="Add Registrant" });

			local.data = CreateObject("component","SWReg").doSWReg(event=arguments.event, qryProgram=local.qryProgram, programType=local.programType);
		
			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="loadRegConfirmation" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","SWReg").loadRegConfirmation(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadRegPaymentScreen" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","SWReg").loadRegPaymentScreen(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="updateSWLProgramRecording" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.editSWLProgram = buildCurrentLink(arguments.event,"editSWLProgram") & "&pid=#arguments.event.getValue('seminarID',0)#&tab=recordings&showBadge=recordings&programAdded=#arguments.event.getValue('programAdded',false)#";

			local.saveResult = CreateObject("component","seminarWebSWL").updateSWLProgramRecording(event=arguments.event);
			if (not local.saveResult.success)
				application.objCommon.redirect("#this.link.message#&message=#local.saveResult.errmsg#");
			else 
				application.objCommon.redirect("#local.editSWLProgram#");
		</cfscript>
	</cffunction>

	<cffunction name="viewSWLProgress" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.seminarID = arguments.event.getTrimValue('pid',0)>
		<cfset local.enrollmentID = arguments.event.getTrimValue('eid',0)>
		<cfset local.depoMemberDataID = arguments.event.getTrimValue('did',0)>
		<cfset local.gridMode = arguments.event.getTrimValue('gridMode','')>

		<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollmentInfo.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWLRegProgressRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegProgressSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegProgressAll',0) is 1>
		<cfif NOT local.hasManageSWLRegProgressRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.objSWLAdmin = CreateObject("component","seminarWebSWL")>
		<cfset local.qrySeminarForms = local.objSWLAdmin.getSeminarForms(seminarID=local.seminarID)>
		<cfset local.qryFormDetails = local.objSWLAdmin.getSeminarFormDetailForRegistrants(seminarID=local.seminarID)>
		<cfset local.qryEnrollment = local.objSWLAdmin.getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfset local.viewResponseLink = buildLinkToTool(toolType='evaluationsAdmin',mca_ta='viewResponse')>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWL_program_registrants_progress.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showSWCreditOfferedGrid" access="public" returntype="void" output="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="participantOrgCode" type="string" required="yes">
		<cfargument name="publisherOrgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject('component','model.admin.admin')>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>

		<cfset local.qryCreditsGrid = local.objCredit.getCreditsGridBySeminar(seminarID=arguments.seminarID,siteCode=arguments.participantOrgCode)>
		<cfset local.qryAllSponsorsAndAuthorities = local.objCredit.getSponsorsAndAuthorities()>
		<cfset local.qryNationalPrograms = local.objCredit.getNationalPrograms()>
		<cfset local.qryStatuses = local.objCredit.getCreditStatuses()>
		<cfset local.isPublisher = arguments.participantOrgCode EQ arguments.publisherOrgCode>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.isPublisher>
			<cfset local.qrySponsorsAndAuthorities = local.qryAllSponsorsAndAuthorities>
		<cfelse>
			<cfset var participantOrgCode = arguments.participantOrgCode>
			<cfset local.qrySponsorsAndAuthorities = QueryFilter(local.qryAllSponsorsAndAuthorities, 
														function(thisRow) {
															return arguments.thisRow.creditSponsorOrgCode EQ participantOrgCode;
														})>
		</cfif>

		<cfif arguments.programType eq "SWL">
			<cfset local.editProgramAction = "editSWLProgram">
			<cfset local.navMethod = "listSWL">
		<cfelseif arguments.programType eq "SWOD">
			<cfset local.editProgramAction = "editSWODProgram">
			<cfset local.navMethod = "listSWOD">
		</cfif>
		
		<cfset local.editSWProgramCreditLink = local.objAdmin.buildLinkToTool(toolType='SeminarWebAdmin',mca_ta=local.editProgramAction,navMethod=local.navMethod) & "&pid=#arguments.seminarID#&tab=credit">
		<cfset local.getSWSeminarCreditFormUploadLink = local.objAdmin.buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='getSWSeminarCreditFormUpload') & "&pid=#arguments.seminarID#&mode=direct">
		<cfset local.viewSWSeminarCreditFormLink = local.objAdmin.buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewSWSeminarCreditForm') & "&pid=#arguments.seminarID#&mode=direct">
		
		<cfinclude template="frm_SW_program_credit.cfm">
	</cffunction>

	<cffunction name="getSWSeminarCreditFormUpload" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.uploadSWProgramCreditFormLink = buildCurrentLink(arguments.event,"uploadSWProgramCreditForm") & "&mode=direct">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SW_program_credit_formUpload.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="uploadSWProgramCreditForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<!--- attempt upload --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="tsadmin")>
			<cffile action="upload" filefield="frmPDF" destination="#local.strFolder.folderPath#" result="local.fileUploadResult" nameconflict="OVERWRITE">
			<cfset local.tmpOracleForm1 = "#local.strFolder.folderPath#/#local.fileUploadResult.ServerFile#">

			<!--- ensure PDF --->
			<cfif local.fileUploadResult.serverfileExt neq "pdf">
				<cffile action="DELETE" file="#local.tmpOracleForm1#">
				<p>File uploaded was not a valid PDF file. Try again.</p>
				<cfabort>
			</cfif>

			<!--- put on s3 and audit log --->
			<cfquery name="local.qryAddToS3UploadQueueAndAuditLog" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @seminarCreditID int, @objectkey VARCHAR(400), @s3UploadReadyStatusID int, @orgID int, @siteID int, @seminarID int,
						@programType varchar(4), @msgjson varchar(max), @nowdate datetime = getdate(), @recordedByMemberID int;

					SET @seminarCreditID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('scId')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @objectkey = 'swodprograms/form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf';

					EXEC dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

					BEGIN TRAN;
						INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated) 
						VALUES (@s3UploadReadyStatusID, 'seminarweb', @objectkey,
							<cfqueryparam value="#local.strFolder.folderPathUNC#\#local.fileUploadResult.ServerFile#" cfsqltype="CF_SQL_VARCHAR">,
							0, @nowdate, @nowdate);

						UPDATE seminarWeb.dbo.tblSeminarsAndCredit
						SET hasForm1 = 1
						WHERE seminarCreditID = @seminarCreditID;
					COMMIT TRAN;

					SELECT @orgID = mcs.orgID, @siteID = mcs.siteID , @seminarID = sac.seminarID, 
						@programType = CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END,
						@msgjson = 'Form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf uploaded to Credit Authority [' + ca.code + ' - ' + ca.authorityName + ' (' + cs.sponsorName + ')] associated to '
					FROM seminarWeb.dbo.tblSeminarsAndCredit AS sac
					INNER JOIN seminarWeb.dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
					INNER JOIN seminarWeb.dbo.tblParticipants AS p ON p.participantID = s.participantID
					INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
					INNER JOIN seminarWeb.dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
					INNER JOIN seminarWeb.dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN seminarWeb.dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
					LEFT OUTER JOIN seminarWeb.dbo.tblSeminarsSWOD AS swod ON swod.seminarID = sac.seminarID
					LEFT OUTER JOIN seminarWeb.dbo.tblSeminarsSWLive AS swl ON swl.seminarID = sac.seminarID
					WHERE sac.seminarCreditID = @seminarCreditID;

					SET @msgjson = @msgjson + @programType + '-' + cast(@seminarID as varchar(10));

					INSERT INTO dbo.queue_mongo (msgjson)
					VALUES('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS varchar(10)) + ',
						"SITEID":' + CAST(@siteID AS varchar(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
						"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.form1FilePath = "#application.paths.SharedTempNoWeb.path#/swForm1/Form1_#arguments.event.getTrimValue('scId')#.pdf">
			<cfif FileExists(local.form1FilePath)>
				<cffile action = "delete" file = "#local.form1FilePath#">
			</cfif>

			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
		</cftry>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">top.reloadSWProgramCredit(); top.MCModalUtils.hideModal();</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSWSeminarCreditForm" access="public" returntype="void" output="no">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarCreditID = arguments.event.getTrimValue('scId')>
		<cfset local.objectKey = "swodprograms/form1_#local.seminarCreditID#.pdf">

		<cfsetting requesttimeout="300">
		<cfcontent reset="true">
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath='', displayName="Form1_#local.seminarCreditID#.pdf", forceDownload=1, s3bucket='seminarweb', s3objectKey=local.objectKey, s3expire=1, s3requesttype="vhost")>
	</cffunction>

	<cffunction name="manageCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>

		<cfset local.seminarID = arguments.event.getTrimValue('pid',0)>
		<cfset local.depoMemberDataID = arguments.event.getTrimValue('did',0)>
		<cfset local.enrollmentID = arguments.event.getTrimValue('eid',0)>
		<cfset local.swType = arguments.event.getTrimValue('swType','')>
		<cfset local.gridMode = arguments.event.getTrimValue('gridMode','')>

		<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollmentInfo.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>
		<cfif local.swType EQ "SWOD">
			<cfset local.hasManageSWODRegCreditRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegCreditSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegCreditAll',0) is 1>
			<cfif NOT local.hasManageSWODRegCreditRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>
		<cfelseif local.swType EQ "SWL">
			<cfset local.hasManageSWLRegCreditRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegCreditSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegCreditAll',0) is 1>
			<cfif NOT local.hasManageSWLRegCreditRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>
		</cfif>

		<cfif local.swType eq "SWL">
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		</cfif>
		<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
		<cfset local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.seminarID,siteCode=arguments.event.getValue('mc_siteInfo.siteCode'))>
		<cfset local.qrySelectedCredits = local.objCredit.getEnrollmentCreditsByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfset local.qryCredit = local.strCredit.qryCredit>

		<cfquery name="local.qryIDRequired" dbtype="query">
			select distinct seminarCreditID
			from [local].qryCredit
			where isIDRequired = 1
			and creditIDText <> ''
		</cfquery>
		<cfquery name="local.qryCredRequired" dbtype="query">
			select distinct seminarCreditID
			from [local].qryCredit
			where isCreditRequired = 1
		</cfquery>

		<cfset local.overrideAllowed = true>

		<!--- if no overrideAllowed, cannot add any new credit selections --->
		<cfset local.prgStartedMessage = false>
		<cfif local.swType eq "SWOD" OR (local.swType eq "SWL" and local.qrySeminar.dateStart lt now())>
			<cfset local.prgStartedMessage = true>
		</cfif>
		<cfif local.prgStartedMessage>
			<cfset local.updateSWLRegistrantCreditLink = buildCurrentLink(arguments.event,"confirmCredit") & "&mode=direct&pid=#local.seminarID#&eid=#local.enrollmentID#&swType=#local.swType#&gridMode=#local.gridMode#">
		<cfelse>
			<cfset local.updateSWLRegistrantCreditLink = buildCurrentLink(arguments.event,"saveCredit") & "&mode=direct&pid=#local.seminarID#&eid=#local.enrollmentID#&swType=#local.swType#&gridMode=#local.gridMode#">
		</cfif>

		<cfif NOT overrideAllowed>
			<cfset local.qryCreditTmp = local.strCredit.qryCredit>
			<cfquery name="local.qryCredit" dbtype="query">
				SELECT c.*
				FROM [local].qryCreditTmp c, [local].qrySelectedCredits sc
				WHERE sc.seminarCreditID = c.seminarCreditID
				UNION
				SELECT c.*
				FROM [local].qryCreditTmp c
				WHERE c.isCreditRequired = 1
			</cfquery>
		</cfif>

		<!--- which credit was previously selected? (do not include required credit that was not already selected) --->
		<!--- this is used to determine which credit has been added --->
		<cfset local.qryCreditTmp = local.strCredit.qryCredit>
		<cfquery name="local.qryCreditPreviouslySelected" dbtype="query">
			select tmp.seminarCreditID
			FROM [local].qryCreditTmp tmp, [local].qrySelectedCredits sc
			WHERE sc.seminarCreditID = tmp.seminarCreditID
		</cfquery>

		<cfset local.previouslySelectedCreditIDs = ValueList(local.qryCreditPreviouslySelected.seminarCreditID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWCredit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="confirmCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.seminarID = arguments.event.getTrimValue('pid',0)>
		<cfset local.enrollmentID = arguments.event.getTrimValue('eid',0)>
		<cfset local.formerSCID = arguments.event.getTrimValue('formerSCID','')>
		<cfset local.previouslySelected = arguments.event.getTrimValue('previouslySelected','')>
		<cfset local.frmcreditlink = arguments.event.getTrimValue('frmcreditlink','')>
		<cfset local.swType = arguments.event.getTrimValue('swType','')>
		<cfset local.gridMode = arguments.event.getTrimValue('gridMode','')>
		
		<cfset local.newlyAdded = "">
		<cfloop list="#local.frmcreditlink#" index="local.i">
			<cfif NOT listFind(local.previouslySelected,local.i)>
				<cfset local.newlyAdded = listAppend(local.newlyAdded,local.i)>
			</cfif>
		</cfloop>

		<cfset local.qryGetEnrollment = local.objCredit.getEnrollmentsInfo(enrollmentID=local.enrollmentID)>

		<!--- if not completed yet, or if completed but are not adding any new credits, just forward to saveCredit since all changes are ok --->
		<cfif len(local.qryGetEnrollment.dateCompleted) is 0 OR (len(local.qryGetEnrollment.dateCompleted) and listLen(local.newlyAdded) is 0)>
			<cfreturn saveCredit(arguments.event)>
		</cfif>

		<cfset local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.seminarID,siteCode=arguments.event.getValue('mc_siteInfo.siteCode'))>
		<cfset local.qryCredit = local.strCredit.qryCredit>
		<cfset local.qryGetDurations = local.objCredit.getEnrollmentsDurations(enrollmentID=local.enrollmentID)>
		<cfset local.strProgress = local.objSWOD.getProgressByEnrollmentID(enrollmentID=local.enrollmentID)>

		<cfset local.arrCreditData = arrayNew(1)>
		<cfloop collection="#arguments.event.getCollection()#" item="local.key">			
			<cfif GetToken(local.key,1,'_') EQ "scid" and val(GetToken(local.key,2,'_')) gt 0>
				<cfset local.thisNum = val(GetToken(local.key,2,'_'))>
				<cfset local.tmpStr = { creditID = local.key, value = arguments.event.getTrimValue('scid_#local.thisNum#','') }>
				<cfset arrayAppend(local.arrCreditData,local.tmpStr)>
			</cfif>
		</cfloop>

		<cfset local.saveSWRegistrantCreditLink = buildCurrentLink(arguments.event,"saveCredit") & "&mode=direct&pid=#local.seminarID#&eid=#local.enrollmentID#&swType=#local.swType#&gridMode=#local.gridMode#">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWCreditConfirm.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>

		<cfset local.enrollmentID = arguments.event.getTrimValue('eid',0)>
		<cfset local.swType = arguments.event.getTrimValue('swType','')>
		<cfset local.gridMode = arguments.event.getTrimValue('gridMode','')>

		<cfset local.creditLinks = arguments.event.getTrimValue('frmCreditLink','')>
		<cfset local.newlyAdded = arguments.event.getTrimValue('newlyAdded','')>
		<cfset local.formerSCID = arguments.event.getTrimValue('formerSCID','')>
		
		<cfloop list="#local.creditLinks#" index="local.thisCreditID">
			<cfif Len(arguments.event.getTrimValue('scid_#local.thisCreditID#',''))>
				<cfset local.objCredit.addEnrollmentCredit(enrollmentID=local.enrollmentID, seminarCreditID=local.thisCreditID, idnumber=arguments.event.getTrimValue('scid_#local.thisCreditID#'))>
			<cfelse>
				<cfset local.objCredit.addEnrollmentCredit(enrollmentID=local.enrollmentID, seminarCreditID=local.thisCreditID, idnumber="")>
			</cfif>
		</cfloop>

		<cfloop list="#local.newlyAdded#" index="local.thisCreditID">
			<cfset local.objCredit.updateECCert(local.enrollmentID, local.thisCreditID, arguments.event.getTrimValue('earned#local.thisCreditID#'), val(arguments.event.getTrimValue('time#local.thisCreditID#')))>
		</cfloop>

		<cfloop list="#local.formerSCID#" index="local.thisCreditID">
			<cfif NOT listFind(local.creditLinks, local.thisCreditID)>
				<cfset local.objCredit.removeEnrollmentCredit(enrollmentID=local.enrollmentID, seminarCreditID=local.thisCreditID)>
			</cfif>
		</cfloop>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.saveSWCreditResult('#local.swType#','#local.gridMode#');
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getCommunication" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>

		<cfset local.programID = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>
		<cfset local.depoMemberDataID = int(val(arguments.event.getTrimValue('did',0)))>
		<cfset local.SWType = arguments.event.getTrimValue('SWType','SWL')>

		<cfset local.qryEnrollment = local.objSWCommon.getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>
		
		<cfif local.SWType EQ "SWL">
			<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWLRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>

			<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
			<cfset local.qryOutgoing = local.objSWCommon.getSWLOutgoingEmails(enrollmentID=local.enrollmentID)>
		<cfelseif local.SWType EQ "SWOD">
			<cfset local.hasManageSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWODRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>

			<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
			<cfset local.qryOutgoing = local.objSWCommon.getSWODOutgoingEmails(enrollmentID=local.enrollmentID)>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_program_registrants_communication.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="resendInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
		<cfset local.objSWLSeminar = createObject("component","model.admin.seminarweb.seminarwebSWL")>

		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.depoMemberDataID = int(val(arguments.event.getTrimValue('did',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>
		<cfset local.SWType = arguments.event.getTrimValue('SWType','SWL')>
		
		<cfset local.qryEnrollment = local.objSWCommon.getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
		<cfset local.registrantEmail = local.qryMemberData.email>
		<cfset local.registrantOverrideEmail = local.qryEnrollment.overrideEmail>

		<cfif local.SWType EQ "SWL">
			<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWLRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>

			<cfset local.objAdminSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>
			<cfset local.qrySeminar = local.objAdminSWL.getSeminarBySeminarID(seminarID=local.seminarId)>
			<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
			<cfset local.participantID = local.participantData.participantID>
			<cfset local.scheduledTaskDetails = local.objSWLSeminar.getScheduledTaskDetails(local.participantID)>
			<cfset local.showInfoAlert = LEN(local.qrySeminar.dateStart) AND local.qrySeminar.dateStart GT now() AND local.scheduledTaskDetails.isRegistrantInstructionsEnabled>
			<cfif local.showInfoAlert>
				<cfset var timeFormatter = function(timeframe) {
					if (timeframe == "1d") {
						return "1 day";
					} else if (timeframe == "1h") {
						return "1 hour";
					} else if (Right(timeframe, 1) == "d") {
						return Replace(timeframe, "d", " days", "all");
					} else if (Right(timeframe, 1) == "h") {
						return Replace(timeframe, "h", " hours", "all");
					}
					return timeframe;
				}>
			
				<!--- Use listMap to transform the list based on the callback function --->
				<cfset local.formattedTimeframes = listMap(local.scheduledTaskDetails.registrantSelectedTimeframes, timeFormatter)>			
				<cfset local.formattedTimeframes = Replace(formattedTimeframes, ",", ", ", "all")>
			</cfif>
			<cfset local.strEmailContent = local.objSWLiveEmails.generateConfirmationEmail(enrollmentID=local.qryEnrollment.enrollmentID)>
		<cfelseif local.SWType EQ "SWOD">
			<cfset local.hasManageSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWODRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>

			<cfset local.objSWODEmails = CreateObject("component","model.seminarweb.SWODEmails")>
			<cfset local.strEmailContent = local.objSWODEmails.generateConfirmationEmail(enrollmentID=local.qryEnrollment.enrollmentID)>
			<cfset local.showInfoAlert = 0>
		</cfif>
		
		<cfset local.sendInstructionsLink = buildCurrentLink(arguments.event,"doSendInstructions") & "&SWType=#local.SWType#&mode=direct&pID=#local.seminarId#">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWRegEmailInstructions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doSendInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		
		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>
		<cfset local.SWType = arguments.event.getTrimValue('SWType','SWL')>

		<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>
		
		<cfif local.SWType EQ "SWL">
			<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWLRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>
			
			<cfset local.objAdminSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>
			<cfset local.qrySeminar = local.objAdminSWL.getSeminarBySeminarID(seminarid=local.seminarId)>

			<cfset local.objSWLiveEmails.sendConfirmation(seminarID=local.seminarId, enrollmentID=local.enrollmentID,
				performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="manualResendInstructions", withMaterials=0, orgcode=local.qryEnrollment.signUpOrgCode,
				emailOverride=arguments.event.getTrimValue('emailToUse',''), customText=arguments.event.getTrimValue('customtext',''),
				isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
		<cfelseif local.SWType EQ "SWOD">
			<cfset local.hasManageSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll',0) is 1>
			<cfif NOT local.hasManageSWODRegistrantsRights>
				<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
			</cfif>

			<cfset local.objSWODEmails = CreateObject("component","model.seminarweb.SWODEmails")>
			<cfset local.objSWODEmails.sendConfirmation(seminarID=local.seminarId, enrollmentID=local.enrollmentID,
				performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="manualResendInstructions", 
				orgcode=local.qryEnrollment.signUpOrgCode, emailOverride=arguments.event.getTrimValue('emailToUse',''), 
				customtext=arguments.event.getTrimValue('customtext',''),isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.$("##MCModalFooter").toggleClass('d-flex d-none');
				window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
			</script>
			<div class="p-3">
				<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
					<span class="font-size-lg d-block d-40 mr-2 text-center">
						<i class="fa-solid fa-circle-check"></i>
					</span>
					<span>E-mail sent.</span>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="sendMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>

		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.depoMemberDataID = int(val(arguments.event.getTrimValue('did',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>

		<cfset local.qryEnrollment = local.objSWCommon.getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
		<cfif NOT local.hasManageSWLRegistrantsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWLiveEmails").generateMaterialsEmail(seminarID=local.seminarId, orgcode=local.qryEnrollment.signUpOrgCode,enrollmentID = local.enrollmentID)>
		<cfset local.registrantEmail = local.qryMemberData.email>
		<cfset local.registrantOverrideEmail = local.qryEnrollment.overrideEmail>
		<cfset local.sendEmailMaterialsLink = buildCurrentLink(arguments.event,"sendEmailMaterials") & "&mode=direct&pID=#local.seminarId#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWL_program_registrants_sendMaterials.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="sendSWLReplayLink" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>

		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.depoMemberDataID = int(val(arguments.event.getTrimValue('did',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>

		<cfset local.qryEnrollment = local.objSWCommon.getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
		<cfif NOT local.hasManageSWLRegistrantsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWLiveEmails").generateReplayConfirmationEmail(enrollmentID=local.qryEnrollment.enrollmentID)>
		<cfset local.registrantEmail = len(local.qryMemberData.email) gt 0 ? local.qryMemberData.email : local.qryEnrollment.overrideEmail>
		<cfset local.sendEmailReplayLink = buildCurrentLink(arguments.event,"doSendSWLReplayLink") & "&mode=direct&pID=#local.seminarId#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWL_program_registrants_replayLink.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sendEmailMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>

		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>

		<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
		<cfif NOT local.hasManageSWLRegistrantsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.objSWLiveEmails.sendMaterials(seminarID=local.seminarId, enrollmentID=local.enrollmentID, performedBy=session.cfcuser.memberdata.depomemberdataid, 
			outgoingType="manualMaterials", emailToUse=arguments.event.getTrimValue('emailToUse'), orgcode=local.qryEnrollment.signUpOrgCode, 
			customText=arguments.event.getTrimValue('customtext',''))>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doSendSWLReplayLink" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>

		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>

		<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWLRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsSignUp',0) is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWLRegistrantsAll',0) is 1>
		<cfif NOT local.hasManageSWLRegistrantsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.objSWLiveEmails.sendReplayConfirmation(seminarID=local.seminarId, enrollmentID=local.enrollmentID, performedBy=session.cfcuser.memberdata.depomemberdataid, 
			outgoingType="manualSendReplayLink", orgcode=local.qryEnrollment.signUpOrgCode, emailOverride=arguments.event.getTrimValue('emailToUse'),
			customText=arguments.event.getTrimValue('customtext',''), isRegistrationConfirmation=0)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWProgramRegPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.programID = int(val(arguments.event.getValue('pid',0)))>
		
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="divExportSWProgramReg")>

		<cfset local.assocHandlesOwnPayment = CreateObject("component","seminarWebSWCommon").doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfquery name="local.qrySWProgramFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @SWAdminSiteResourceID int;
			select @usageID = dbo.fn_cf_getUsageID('SemWebCatalog',<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.swType#Enrollment">,null);
			set @SWAdminSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.siteResourceID#">;

			select f.fieldID, f.fieldReference
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where fu.usageID = @usageID
			and f.controllingSiteResourceID = @SWAdminSiteResourceID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by f.fieldOrder;
		</cfquery>

		<cfif local.programID neq 0>
			<cfset local.qrySeminarForms = createObject("component","seminarWebSWL").getSeminarForms(seminarID=local.programID)>
		</cfif>
		
		<cfswitch expression="#local.swType#">
			<cfcase value="SWL">
				<cfset local.doExportSWProgramRegLink = buildCurrentLink(arguments.event,"exportSWLRegistrants") & "&pID=#local.programID#&gridmode=#arguments.event.getValue('gridmode','')#&mode=stream">
				<cfset local.doExportSWAllProgramRegLink = buildCurrentLink(arguments.event,"exportSWLAllRegistrants") & "&pID=#local.programID#&gridmode=#arguments.event.getValue('gridmode','')#&mode=stream">
			</cfcase>
			<cfcase value="SWOD">
				<cfset local.doExportSWProgramRegLink = buildCurrentLink(arguments.event,"exportSWODRegistrants") & "&pID=#local.programID#&gridmode=#arguments.event.getValue('gridmode','')#&mode=stream">
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportSWProgramReg.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWLRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.gridmode = arguments.event.getValue('gridmode','');

			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'));
			
			if (local.gridmode EQ "exportregsearch") {
				local.pDateFrom = arguments.event.getValue('frpDateFrom','');
				local.pDateTo = arguments.event.getValue('frpDateTo','');
				local.pKeyword = arguments.event.getTrimValue('frpKeyword','');
				local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');
				local.pPublisherType = arguments.event.getTrimValue('frpPubType','');
				local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
				local.rAttended = arguments.event.getValue('frrAttended','');
				local.rDateFrom = arguments.event.getValue('frrDateFrom','');
				local.rDateTo = arguments.event.getValue('frrDateTo','');
				local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
				local.rCredits = arguments.event.getValue('frrCredits','');

				local.reportFileName = "SWL-Registrants.csv";

				CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'),
					mode="exportregsearch", seminarID=0, rAttended=local.rAttended, rDateFrom=local.rDateFrom, rDateTo=local.rDateTo, rHideDeleted=local.rHideDeleted,
					rCredits=local.rCredits, memberID=0, pDateFrom=local.pDateFrom, pDateTo=local.pDateTo, pKeyword=local.pKeyword, pProgramCode=local.pProgramCode,
					pPublisherType=local.pPublisherType, pHideInactive=local.pHideInactive, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName,
					fieldSetID=arguments.event.getValue('fsid',0), exportFieldIDList=arguments.event.getTrimValue('swfidlist',''));
			} else {	
				local.seminarId = arguments.event.getTrimValue('pid');
				local.rAttended = arguments.event.getValue('rAttended','');
				local.rDateFrom = arguments.event.getValue('rDateFrom','');
				local.rDateTo = arguments.event.getValue('rDateTo','');
				local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
				local.rCredits = arguments.event.getValue('rCredits','');

				local.reportFileName = "SWL-#local.seminarId#-Registrants.csv";

				CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'),
					mode="export", seminarID=local.seminarId, rAttended=local.rAttended, rDateFrom=local.rDateFrom, rDateTo=local.rDateTo, rHideDeleted=local.rHideDeleted, 
					rCredits=local.rCredits, memberID=0, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, 
					fbFormID=arguments.event.getValue('swformid',0), fieldSetID=arguments.event.getValue('fsid',0), exportFieldIDList=arguments.event.getTrimValue('swfidlist',''));
			}
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					doDownloadSWProgramReg('#local.stDownloadURL#');
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportSWLAllRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();

			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'));

			local.seminarId = arguments.event.getTrimValue('pid');

			local.reportFileName = "SWL-#local.seminarId#-AllRegistrants.csv";

			CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getAllRegistrants(seminarID=local.seminarId, memberID=0, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");

			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					doDownloadSWProgramReg('#local.stDownloadURL#');
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="viewCertificate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWcertificates = CreateObject("component","model.seminarweb.SWCertificates")>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
		
		<cfscript>
		local.swType = arguments.event.getValue('swtype','');

		if(listFindNoCase("SWL,SWOD",local.swType)){
			local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=val(arguments.event.getValue('eid')));

			if (local.qryEnrollmentInfo.recordcount is 0)
				application.objCommon.redirect('#this.link.message#&mode=direct&message=1');

			if (local.swType EQ "SWOD") {
				local.hasViewSWODCertificateRights = (arguments.event.getValue('mc_adminToolInfo.myRights.sendSWODCertificatesSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.sendSWODCertificatesAll',0) is 1;
				if (NOT local.hasViewSWODCertificateRights)
					application.objCommon.redirect('#this.link.message#&mode=direct&message=2');
			} elseif (local.swType EQ "SWL") {
				local.hasViewSWLCertificateRights = (arguments.event.getValue('mc_adminToolInfo.myRights.sendSWLCertificatesSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.sendSWLCertificatesAll',0) is 1;
				if (NOT local.hasViewSWLCertificateRights)
					application.objCommon.redirect('#this.link.message#&mode=direct&message=2');
			}
		}
		</cfscript>
		
		<cfswitch expression="#arguments.event.getValue('certaction','')#">
			<cfcase value="loadCert">
				<cftry>
					<cfsetting requesttimeout="300">
					<cfif local.swType EQ "SWCP">
						<cfset local.strCertificate = local.objSWcertificates.generateCPCertificate(depomemberDataID=arguments.event.getValue('dID'), programID=arguments.event.getValue('pID'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
					<cfelse>
						<cfset local.strCertificate = local.objSWcertificates.generateCertificate(enrollmentid=arguments.event.getValue('eID'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
					</cfif>
					<cfif len(local.strCertificate.certificateURL)>
						<cflocation url="#local.strCertificate.certificateURL#&swtype=#local.swType#" addtoken="No">
					</cfif>
					<cfsavecontent variable="local.data">
						<cfoutput>No certificate generated.</cfoutput>
					</cfsavecontent>
					
					<cfcatch>
						<cfset application.objError.extendRequestTimeout() />
						<cfset application.objError.sendError(cfcatch=cfcatch) />
						<cfset local.data = "Error Generating Certificate.">
					</cfcatch>
				</cftry>
			</cfcase>
			<cfcase value="emailCert">
				<cfif isValid("regex",arguments.event.getValue('_email',''),application.regEx.email)>
					<cfif local.swType EQ "SWCP">
						<cfset local.objSWcertificates.sendCPCertificateByEmail(depomemberdataid=arguments.event.getValue('dID'),programID=arguments.event.getValue('pID'),performedBy=session.cfcuser.memberdata.depomemberdataid,outgoingType="manualCertificate",emailToUse=arguments.event.getValue('_email',''))>
					<cfelse>
						<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=arguments.event.getValue('eID'), 
						performedBy=session.cfcuser.memberdata.depomemberdataID, outgoingType="manualCertificate", emailToUse=arguments.event.getValue('_email',''), customtext=arguments.event.getTrimValue('customtext',''), isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
					</cfif>
				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">top.MCModalUtils.hideModal();</script>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfif local.swType EQ "SWCP">
					<cfset local.qryMemberData = local.objSWCommon.getMemberInfo(depomemberdataid=arguments.event.getValue('dID'))>
					<cfset local.userEmail = local.qryMemberData.email>
					<cfset local.certificateLink = "#this.link.viewCertificatePDF#&swtype=#local.swType#&pID=#arguments.event.getValue('pID')#&dID=#arguments.event.getValue('dID')#">
				<cfelse>
					<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWCertificates").generateCertificateEmail(enrollmentID = local.qryEnrollmentInfo.enrollmentID)>
					<cfset local.userEmail = len(local.qryEnrollmentInfo.email) gt 0 ? local.qryEnrollmentInfo.email : local.qryEnrollmentInfo.overrideEmail>
					<cfset local.certificateLink = "#this.link.viewCertificatePDF#&swtype=#local.swType#&eid=#arguments.event.getValue('eID')#">
				</cfif>
				
				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfinclude template="frm_registrantCertificate.cfm">
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doEmailCert" access="public" returntype="void" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="emailToUse" type="string" required="yes">
		
		<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=arguments.enrollmentID, 
			performedBy=session.cfcuser.memberdata.depomemberdataID, outgoingType="manualCertificate", emailToUse=arguments.emailToUse)>
	</cffunction>

	<cffunction name="listSWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSWODSeminar = createObject("component","model.admin.seminarweb.seminarwebSWOD");
			local.seminarsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODList&mode=stream';
			local.seminarsExportLink = buildCurrentLink(arguments.event,"exportSWODSeminars") & "&mode=stream";
			local.submitProgramLink = buildCurrentLink(arguments.event,"submitSWODProgram") & "&mode=stream";
			local.editProgramLink = buildCurrentLink(arguments.event,"editSWODProgram");
			local.deleteSWProgramLink =  buildCurrentLink(arguments.event,"deleteSWProgram") & "&mode=direct";
			local.deactivateSWODProgramsLink = buildCurrentLink(arguments.event,"deactivateSWODPrograms") & "&mode=direct";
			
			local.hasManageSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsSignUp',0) is 1 OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll',0) is 1);
			if (local.hasManageSWODRegistrantsRights) {
				local.SWODRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODRegistrants&gridmode=regsearchgrid&mode=stream";
				local.exportRegPromptLink = buildCurrentLink(arguments.event,"exportSWProgramRegPrompt") & "&gridmode=exportregsearch&swType=SWOD&mode=direct";
				local.manageCreditLink = buildCurrentLink(arguments.event,"manageCredit") & "&mode=direct";
				local.viewProgressLink = buildCurrentLink(arguments.event,"viewProgress") & "&mode=direct";
				local.getCommunicationLink = buildCurrentLink(arguments.event,"getCommunication") & "&mode=direct";
				local.resendInstructionsLink = buildCurrentLink(arguments.event,"resendInstructions") & "&mode=direct";
				local.changeRegistrantPriceLink =  buildCurrentLink(arguments.event,"changeRegistrantPrice") & "&mode=direct";
				local.removeEnrollmentLink =  buildCurrentLink(arguments.event,"removeEnrollment") & "&mode=direct";

				local.transactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
				local.myRightsTransactionsAdmin = buildRightAssignments(local.transactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
				local.addSWODPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
				if (local.myRightsTransactionsAdmin.transAllocatePayment is 1)
					local.allocateSWODPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
			}

			local.pageHeading = arguments.event.getValue('mc_siteInfo.swodBrand');
			
			local.selectedTab = event.getTrimValue("tab","programs");
			local.lockTab = "";
			if(event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}
			
			local.rDateFrom = "#month(now())#/1/#year(now())#";
			local.rDateTo = "#dateformat(now(),'m/d/yyyy')#";
			local.activatedDateFrom = '';
			local.activatedDateTo = '';
			local.origPublishDateFrom = '';
			local.origPublishDateTo = '';
			local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation;
		</cfscript>
		<cfset local.SWODFilter = CreateObject("component","model.admin.seminarweb.seminarwebSWOD").getSWODFilter()>
		<cfset local.participantID = local.participantData.participantID>
		<cfset local.scheduledTaskDetails = local.objSWODSeminar.getScheduledTaskDetails(local.participantID)>
		<cfset local.listProgramsLink = buildCurrentLink(arguments.event,"listSWOD")>
		<cfset local.sampleSWODImportTemplateLink = buildCurrentLink(arguments.event,"sampleSWODImportTemplate") & "&mode=stream">
		<cfset local.showImpTemplate = true>
		<cfif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processSWODImport(event=arguments.event)>
		</cfif>
		<cfset local.deactivateProgramCount = local.objSWODSeminar.getCountOfDefaultProgramsToDeactivate(participantID=local.participantID)>
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWOD.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- import data --->
	<cffunction name="sampleSWODImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","SeminarWebImport").generateSWODImportTemplate(orgID=arguments.event.getValue('mc_siteInfo.orgID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processSWODImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","SeminarWebImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importSWODPrograms(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=buildCurrentLink(arguments.event,"listSWOD"))>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="exportSWODSeminars" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.keyword = arguments.event.getValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType',0);
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.activatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.activatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.origPublishDateFrom = arguments.event.getValue('fOrigPublishDateFrom','');
			local.origPublishDateTo = arguments.event.getValue('fOrigPublishDateTo','');
			local.syndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);

			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "OnDemandPrograms.csv";
			
			CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getPrograms(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), 
				mode='export', keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType, hideInactive=local.hideInactive,
				activatedDateFrom=local.activatedDateFrom, activatedDateTo=local.activatedDateTo, origPublishDateFrom=local.origPublishDateFrom,
				origPublishDateTo=local.origPublishDateTo, syndicatedOnly=local.syndicatedOnly, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWODPrograms('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="submitSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
		<cfset local.managingSiteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.submissionID = arguments.event.getValue('submissionID',0)>
		<cfset local.fileUploadSettings = CreateObject("component","seminarWebSWCommon").getUploadFileSettingsForSubmitProgram(orgCode=arguments.event.getValue('mc_siteInfo.siteCode'))>
		<cfset local.fileUploadSettingsJSON = serializeJSON(local.fileUploadSettings)>

		<cfset local.qrySubmissionDetail = createObject("component","seminarWebSWOD").getSWODSubmissionDetail(submissionID=local.submissionID)>
		<cfset local.readOnly = local.qrySubmissionDetail.status eq "R">

		<cfif local.submissionID gt 0>
			<cfset local.participantOrgCode = local.qrySubmissionDetail.participantOrgCode>
			<cfset local.participantOrgID = local.qrySubmissionDetail.participantOrgID>
			<cfset local.participantSiteID = local.qrySubmissionDetail.participantSiteID>
			<cfset local.formLink = buildCurrentLink(arguments.event,"convertSubmissionToOnDemand") & "&submissionID=#local.submissionID#&mode=stream">
			<cfset local.submitButtonText = "Convert to OnDemand">
			
			<cfset local.arrSyndPrices = arrayNew(1)>
			<cfif len(local.qrySubmissionDetail.priceSyndication)>
				<cfset local.arrGroupNodes = XMLSearch(local.qrySubmissionDetail.priceSyndication,"//pricegroup")>

				<cfloop array="#local.arrGroupNodes#" item="local.thisNode" index="local.thisIndex">
					<cfset arrayAppend(local.arrSyndPrices, {
						"autoid":local.thisIndex,
						"pricecategory":local.thisNode.group.XMLText,
						"price":NumberFormat(ReReplace(local.thisNode.price.XMLText,"[^0-9\.]","","ALL"),'9.99')
					})>
				</cfloop>
			</cfif>
		<cfelse>
			<cfset local.participantOrgCode = arguments.event.getValue('mc_siteInfo.siteCode')>
			<cfset local.participantOrgID = arguments.event.getValue('mc_siteInfo.orgID')>
			<cfset local.participantSiteID = arguments.event.getValue('mc_siteInfo.siteID')>
			<cfset local.formLink = buildCurrentLink(arguments.event,"saveSubmitSWODProgram") & "&mode=stream">
			<cfset local.submitButtonText = "Submit Program">
		</cfif>

		<cfset local.featureImageConfigID = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getFeaturedImageConfigID(referenceID=local.participantSiteID, referenceType="swProgram")>
		<cfset local.hasProgramFeaturedImageConfig = local.featureImageConfigID gt 0>
		<cfset local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&pid=0&mode=direct&siteCode=#local.participantOrgCode#">
		<cfset local.manageCopyRatesLink = buildCurrentLink(arguments.event,"manageCopyRates") & "&mode=direct">

		<cfset local.qryFrequentGroups = createObject("component","model.admin.permissions.permissionAdmin").getFrequentGroups(orgID=local.participantOrgID)>
		<cfquery name="local.qryGroups" datasource="#application.dsn.membercentral.dsn#">
			SELECT groupID, isSystemGroup, groupPathSortOrder AS thePath, groupPathExpanded AS thePathexpanded, groupDesc
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.participantOrgID#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
			ORDER BY groupPathSortOrder;
		</cfquery>
		<cfquery name="local.qryGroupsSYS" dbtype="query">
			select *
			from [local].qryGroups
			where isSystemGroup = 1
			order by thePath
		</cfquery>
		<cfquery name="local.qryGroupsNonSYS" dbtype="query">
			select *
			from [local].qryGroups
			where isSystemGroup = 0
			order by thePath
		</cfquery>

		<cfquery name="local.qryCategories" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT c.categoryID, c.categoryName
			FROM dbo.tblCategories AS c 
			INNER JOIN dbo.tblParticipants AS p ON c.participantID = p.participantID
				AND p.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.participantOrgCode#">
			ORDER BY c.categoryName;
		</cfquery>

		<cfset local.qryAllSponsorsAndAuthorities = local.objCredit.getSponsorsAndAuthorities()>
		<cfset var participantOrgCode = local.participantOrgCode>
		<cfset local.qrySiteSponsorsAndAuthorities = QueryFilter(local.qryAllSponsorsAndAuthorities, 
			function(thisRow) {
				return arguments.thisRow.creditSponsorOrgCode EQ participantOrgCode;
			})>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.qrySponsorsAndAuthorities = local.qryAllSponsorsAndAuthorities>
		<cfelse>
			<cfset local.qrySponsorsAndAuthorities = local.qrySiteSponsorsAndAuthorities>
		</cfif>
		<cfset local.qryCreditStatuses = local.objCredit.getCreditStatuses()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWOD_submitProgram.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSubmitSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.resultStruct = CreateObject("component","seminarWebSWOD").insertSWODSubmission(event=arguments.event)>

		<cfreturn returnAppStruct(serializeJSON(local.resultStruct),'echo')>
	</cffunction>

	<cffunction name="convertSubmissionToOnDemand" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.resultStruct = CreateObject("component","seminarWebSWOD").convertSubmissionToOnDemand(event=arguments.event)>

		<cfreturn returnAppStruct(serializeJSON(local.resultStruct),'echo')>
	</cffunction>

	<cffunction name="editSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = StructNew();
			local.seminarId = int(val(arguments.event.getTrimValue('pid',0)));
			local.swType = 'SWOD';
			local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode');
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.programAdded = arguments.event.getValue('programAdded',false);
			local.lastIncompleteSectionIndex = arguments.event.getValue('lastIncompleteSectionIndex',0);

			if (local.seminarId eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");

			local.manageCreditLink = buildCurrentLink(arguments.event,"manageCredit") & "&mode=direct";
			local.viewProgressLink = buildCurrentLink(arguments.event,"viewProgress") & "&mode=direct";
			local.getCommunicationLink = buildCurrentLink(arguments.event,"getCommunication") & "&mode=direct";
			local.resendInstructionsLink = buildCurrentLink(arguments.event,"resendInstructions") & "&mode=direct";
			local.editSWODTitleLink = buildCurrentLink(arguments.event,"editSWODTitle") & "&seminarID=#local.seminarID#&mode=direct";
			local.editSWODProgram = buildCurrentLink(arguments.event,"editSWODProgram") & "&pid=#local.seminarId#";
			local.listProgramsLink = buildCurrentLink(arguments.event,"listSWOD");
			local.editSWBProgramLink = buildCurrentLink(arguments.event,"editSWBProgram");
	
			local.selectedTab = event.getTrimValue("tab","details");
			local.lockTab = "";
			if(event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}

			local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon");
			local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors");
			local.objCredit = CreateObject("component","model.seminarweb.SWCredits");
			local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars");
			local.objAdminSWOD = CreateObject("component","seminarWebSWOD");
			local.objAdminSWParticipants = createObject("component","seminarWebParticipants");
			local.objSWBundles = CreateObject("component","model.seminarweb.SWBundles");
			local.objSWForms =  createObject("component","seminarWebForm");
			local.objAdminSWCommon = createObject("component","seminarWebSWCommon");
			local.seminarwebSWCreditsObj = createObject("component","seminarWebSWCredits");
			
			local.qrySeminar = local.objSWOD.getSeminarBySeminarID(local.seminarID);
			if (val(local.qrySeminar.seminarID) is 0) {
				application.objCommon.redirect("#this.link.message#&message=1");
			} else {
				local.speakerBio = local.objAuthor.getSpeakersInfoByProgramIDForCatalog(programID=local.seminarID, programType="SWOD", mode="bs4");
				local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.seminarID, siteCode=local.siteCode);
				local.JSStruct = local.objCredit.getCreditInfoForCatalog(qryCredits=local.strCredit.qryCredit, isControlPanel=1);
				local.hasEditSWODProgramAllRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWODProgramAll') is 1;
				local.hasEditSWODProgramPublishRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWODProgramPublish') is 1;
				local.isPublisher = local.qrySeminar.publisherOrgCode EQ local.siteCode;

				local.hasEditRights = (local.hasEditSWODProgramAllRights OR local.hasEditSWODProgramPublishRights) AND local.isPublisher;
				local.hasManageCreditRights = local.hasEditSWODProgramAllRights OR local.hasEditSWODProgramPublishRights;
				local.hasLockSWProgramRights = local.hasEditRights AND arguments.event.getValue('mc_adminToolInfo.myRights.lockSWProgram') is 1;
				local.showBillingTab = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher;
				local.isSWProgramLocked = local.qrySeminar.lockSettings is 1;
				local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.siteCode);
				local.qryAssociation = local.strAssociation.qryAssociation;

				/* inactive syndicated program */
				if (NOT local.isPublisher AND NOT local.objAdminSWCommon.isActiveOptedIntoProgram(participantID=local.qryAssociation.participantID, programID=local.seminarID, programType='SWOD'))
					application.objCommon.redirect("#this.link.message#&message=1");

				if(local.programAdded AND local.lastIncompleteSectionIndex EQ 0) {
					local.qryAllSponsorsAndAuthorities = local.objCredit.getSponsorsAndAuthorities();
					var participantOrgCode = local.siteCode;
					local.qrySponsorsAndAuthorities = QueryFilter(local.qryAllSponsorsAndAuthorities, 
														function(thisRow) {
															return arguments.thisRow.creditSponsorOrgCode EQ participantOrgCode;
														});
														
					for (i = 1; i <= local.qrySponsorsAndAuthorities.recordCount; i++) {
						// Fetch each row
						local.CSALinkID = local.qrySponsorsAndAuthorities.CSALinkID[i];
						// Call the addSeminarCredit method
						local.seminarwebSWCreditsObj.addSeminarAllPublisherCredit(
							local.siteCode, 
							local.seminarID,   
							local.CSALinkID
						);
					}
				}

				local.updateSWODSyndicateProgramDetailsLink = buildCurrentLink(arguments.event,"updateSWODSyndicateProgram");
				local.qrySeminarSyndicateObj = local.objAdminSWCommon.getSeminarSyndicateData(local.seminarID,arguments.event.getValue('mc_siteInfo.sitecode'));
				if (local.hasEditRights) {
					/* Details Tab */
					local.strLearningObjectives = getLearningObjectives(programType="SWOD",programID=local.seminarID, isReadOnly=local.isSWProgramLocked, orgcode=local.siteCode, adminHomeResource=arguments.event.getValue('mc_adminNav.adminHomeResource'));
					local.qryProgramVideoFiles = local.objSWCommon.getProgramVideoFiles(programType="SWOD", programID=local.seminarID);
					local.qryLayouts = local.objAdminSWOD.getLayouts();
					local.qrySWODSeminars = local.objAdminSWOD.getPrerequisiteSeminars(seminarID=local.qrySeminar.seminarID, orgCode=local.siteCode);
					local.qryAuthors = local.objAuthor.getAuthorsBySeminarID(seminarID=local.qrySeminar.seminarID, authorType=arguments.event.getValue('atype','moderator'));
					if (val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0) {
						local.arrSWODConfigs = [ { "ftdExt":"#this.siteResourceID#_swodprogram", "controllingReferenceID":arguments.event.getValue('mc_siteInfo.siteID'), 
							"controllingReferenceType":"swProgram", "referenceID":local.seminarID, "referenceType":"swodProgram", "resourceType":"SeminarWebAdmin", 
							"resourceTypeTitle":local.qrySeminar.seminarName, "onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWEditProgram", 
							"header":'', "ftdImgClassList":"", "readOnly":local.isSWProgramLocked, "enableToggle":1
						} ];
						local.strSWODFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=local.siteCode, arrConfigs=local.arrSWODConfigs);
					}

					local.strSponsors = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsorsSelector(resourceType="Program",
						referenceType="swodProgram", referenceID=local.seminarID, selectorID='swodProgramSponsors', readOnly=local.isSWProgramLocked);
					local.getSponsor = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsors(mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'),referenceType="swodProgram", referenceID=local.seminarID);
					/* Fields Tab */
					local.arrSWODEnrollmentFields = [{ "title"="", "intro"='',
						"detailID"=local.seminarID, "gridext"="#this.siteResourceID#_SWODEnrollmentFields", "initGridOnLoad"=false, "controllingSRID"=this.siteResourceID, 
						"resourceType"='SemWebCatalog', "areaName"='SWODEnrollment', "readOnly":local.isSWProgramLocked ? 1 : 0 }];
					local.strSWODEnrollmentFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrSWODEnrollmentFields);

					/* Links Tab */
					local.SWLinksLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLinks&pid=#local.seminarID#&swtype=SWOD&mode=stream";
					local.editSWLink = buildCurrentLink(arguments.event,"editSWLink") & "&pid=#local.seminarID#&swtype=swod&mode=direct";
					
					/* Speaker Tab */
					local.speakerListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=getSWProgramAuthors&pid=#local.seminarID#&ft=SWOD';
					local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&pid=#local.seminarID#&swtype=SWOD&mode=direct";

					/* Subjects Tab */
					local.categoriesListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=listSWCategories&pid=#local.seminarID#&ft=SWOD';
					local.addSWCategoryLink = buildCurrentLink(arguments.event,"addSWCategory") & "&pid=#local.seminarID#&swtype=SWOD&mode=direct";
					
					/* Titles Tab */
					local.titlesListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=listSWODTitles&mode=stream&sid=#local.seminarID#";
					local.addSWODTitleLink = buildCurrentLink(arguments.event,"addSWODTitle") & "&pid=#local.seminarID#&mode=direct";
					local.seminarTitlesCount = createObject("component","model.seminarweb.SWODSeminars").getSeminarTitlesBySeminarID(seminarID=local.seminarID).recordCount;
				}
				if (!local.isPublisher) {
					/* Speaker Tab */
					local.speakerListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=getSWProgramAuthors&pid=#local.seminarID#&ft=SWOD';
					local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&pid=#local.seminarID#&swtype=SWOD&mode=direct&isPublisher=false";
					local.enrollmentCount = local.objAdminSWCommon.getEnrollmentCount(seminarID=local.seminarID, participantID=local.qryAssociation.participantID).count;
				}

				/* Rates Tab */
				local.hasManageSWODRatesRights = local.hasEditSWODProgramAllRights OR local.hasEditSWODProgramPublishRights;
				if (local.hasManageSWODRatesRights) {
					local.isFeaturedProgram = local.objSWCommon.isFeaturedProgram(catalogOrgCode=local.siteCode, programID=local.seminarID, programType="SWOD");
					
					if (NOT local.isPublisher)
						local.qryOptInSeminarRateSettings = local.objAdminSWCommon.getSWProgramRateSettings(participantID=local.qryAssociation.participantID, programID=local.seminarID, programType='SWOD');
					
					local.hasSWODRateChangeRights = local.isPublisher OR local.qrySeminar.allowOptInRateChange IS 1;
					if (local.hasSWODRateChangeRights) {
						local.manageCopyRatesLink = buildCurrentLink(arguments.event,"manageCopyRates") & "&mode=direct";
						local.editSWRate = buildCurrentLink(arguments.event,"editSWRate") & "&mode=direct";
						local.copySWRate = buildCurrentLink(arguments.event,"copySWRate") & "&mode=direct";
					}
					
					local.permissionsGridAddLink = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
					local.ratesListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWProgramRates&programID=#local.seminarID#&ft=SWOD&mode=stream";
					if(local.qrySeminar.handlesOwnPayment) {
						if (val(local.qrySeminar.revenueGLAccountID)) {
							local.tmpStrAccount = CreateObject("component", "model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qrySeminar.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
							local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
						} else {
							local.GLAccountPath = "";
						}

						local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountIDCatalog",
							"idFldValue": val(local.qrySeminar.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses accounting settings designated GL Account.)",
							"clearBtnTxt": "Remove Override" };
						local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
					}
				}

				local.hasManageOptInRights = local.qrySeminar.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and local.isPublisher;
				local.canOptOutOfSyndicateSite = local.qrySeminar.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and !local.isPublisher;
				local.showSWODOptInTab = local.hasManageOptInRights AND val(local.qrySeminar.allowSyndication) is 1;
				if (local.showSWODOptInTab) {
					/* Opt-In Tab */
					local.qryNationalPrograms = local.objCredit.getNationalPrograms();
				}

				local.hasManageSWFormRights = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWForms') and local.isPublisher;
				if (local.hasManageSWFormRights) {
					/* Forms Tab */
					local.SWFormsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSeminarForms&sid=#local.seminarID#&mode=stream";
					local.editFormLink = buildCurrentLink(arguments.event,"editSeminarForm") & "&pid=#local.seminarID#&swtype=SWOD&mode=direct";
					local.qryOrgForms = local.objSWForms.getFormsByType(siteID=local.siteID, format='E');
					local.qryOrgSurveyForms = local.objSWForms.getFormsByType(siteID=local.siteID, format='S');
				}

				local.hasManageSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsSignUp',0) is 1 OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll',0) is 1);
				if (local.hasManageSWODRegistrantsRights) {
					/* Registrants Tab */
					local.SWODRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODRegistrants&mode=stream&pID=#local.seminarId#";
					local.exportRegPromptLink = buildCurrentLink(arguments.event,"exportSWProgramRegPrompt") & "&pID=#local.seminarID#&swType=SWOD&mode=direct";
					local.exportRegistrantsFormResponsesLink = buildCurrentLink(arguments.event,"exportSWRegistrantsFormResponses") & "&mode=stream&swType=SWOD&pID=#local.seminarID#";
					local.addSWRegLink = buildCurrentLink(arguments.event,"addSWReg") & "&pID=#local.seminarId#&ft=SWOD&mode=direct";
					local.changeRegistrantPriceLink =  buildCurrentLink(arguments.event,"changeRegistrantPrice") & "&mode=direct";
					local.removeEnrollmentLink =  buildCurrentLink(arguments.event,"removeEnrollment") & "&mode=direct";

					local.qrySeminarForms = createObject("component","seminarWebSWL").getSeminarForms(seminarID=local.seminarID);

					local.transactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
					local.myRightsTransactionsAdmin = buildRightAssignments(local.transactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
					local.addSWODPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
					if (local.myRightsTransactionsAdmin.transAllocatePayment is 1)
						local.allocateSWODPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
				}

				local.allowRegistrants = 0;
				local.hasToggleSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.toggleSWODRegistrantsPublish') is 1 AND local.isPublisher) OR arguments.event.getValue('mc_adminToolInfo.myRights.toggleSWODRegistrantsAll') is 1;
				if (local.hasToggleSWODRegistrantsRights)
					local.allowRegistrants = local.qrySeminar.AllowRegistrants;

				local.hasAddSWODRegistrantRights = (arguments.event.getValue('mc_adminToolInfo.myRights.addSWODRegistrantSignUp') is 1);

				local.hasMassEmailSWODRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1);
				if (local.hasMassEmailSWODRegistrantsRights)
					local.massEmailRegistrantsLink = buildCurrentLink(arguments.event,"massEmailSWRegistrants") & "&pID=#local.seminarId#&ft=SWOD&mode=direct";
				
				local.hasMassEmailSWODCertificatesRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1);
				if (local.hasMassEmailSWODCertificatesRights)
					local.massEmailCertificatesLink = buildCurrentLink(arguments.event,"MassEmailCertificatesSWOD") & "&pID=#local.seminarId#&mode=direct";

				local.hasMassEmailSWODResendInstructionsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1);
				if (local.hasMassEmailSWODResendInstructionsRights)
					local.massEmailConnectionInstructionsLink = buildCurrentLink(arguments.event,"massEmailConnectionInstructionsSWOD") & "&pID=#local.seminarId#&mode=direct";

				if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
					/* Billing Tab */
					local.swBillingLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBillingLogs&pID=#local.seminarId#&ft=SWOD&mode=stream";
					local.qryOrgFormsForBilling = local.objSWForms.getFormsByMultipleTypes(siteID=local.siteID, formatList="E,S");
					local.qryMCBilling = CreateObject("component","seminarWebSWCommon").getMCBillingDetails(orgcode=local.siteCode);
				}
			}

			local.showSyndicationWarning = false;
			if (local.showSWODOptInTab AND local.hasEditRights) {
				var thesiteCode = local.siteCode;
				local.qryOptInParticipants = local.objAdminSWParticipants.getParticipantsOptedIntoSeminar(seminarID=local.seminarID).filter(function(row) { return arguments.row.orgcode neq thesiteCode; });
				if (NOT local.qryOptInParticipants.recordcount)
					local.showSyndicationWarning = true;
			}

			// Initialize the array to hold issues
			local.arrSeminarSetupIssues = [];
			local.arrSeminarSetupSuccess = [];
			local.validDates = false;
			local.validRates = false;
			local.bundleIsActive = false;
			local.bundleLinksArray  = [];
			local.bundleLinks = "";
			local.sellDatesInFutureIssue = "The seminar has future sell dates in the Catalog Details section.";
			local.notSoldInCatalogIssue = "The seminar is marked to not be sold in the Catalog Details section.";
			local.sellInCatalogWithActiveBundleSuccess = "The seminar is marked to sell in the Catalog Details section, the sale dates are not expired, rates are defined, and it's included in an Active Bundle for sale.";
			local.notSellInCatalogInactiveBundleStatement = 'The seminar is marked to not sell in the Catalog Details section, but is included in an Active Bundle for sale.';
			local.sellingSuccessStatement = 'The seminar is marked to sell in the Catalog Details section, the sale dates are not expired and rates are defined.';
			local.expiredDatesIssue = 'The seminar has expired sale dates in the Catalog Details section.';
			local.ratesIssue = 'Rates have not been defined in the Catalog Details section.';
			local.publishedIssues = 'The seminar''s "Program Status" is marked "Inactive" in the Basics section.';
			local.publishedSuccessStatement = 'The "Program Status" is marked "Active" in the Basics section.';
			local.qryBundleDetails = local.objSWBundles.getBundleDetailsFromProgramID(programID = local.seminarID); 
			if (local.qryBundleDetails.recordCount > 0 ) {
				local.activeBundles = queryFilter(local.qryBundleDetails, function(row) {
									return len(row.dateCatalogStart) AND row.dateCatalogEnd GT now() AND row.status EQ 'A';
								})
				if(local.activeBundles.recordCount) {
					local.bundleIsActive = true;
					for (var i = 1; i <= local.activeBundles.recordCount; i++) {
						arrayAppend(local.bundleLinksArray, '<a href="##" onclick="editSWBProgram(' & local.activeBundles.bundleID[i] & ')">SWB-' & local.activeBundles.bundleID[i] & '</a>');
					}
					local.bundleLinks = " (" & arrayToList(local.bundleLinksArray, ", ") & ")";
				}
			}
			local.qryRates = local.objAdminSWCommon.getSeminarRatesBySeminarID(participantID=local.qryAssociation.participantID, seminarID=local.seminarID);
			// Check if the seminar is marked Inactive
			if (local.qrySeminar.isPublished eq 0) 
				arrayAppend(local.arrSeminarSetupIssues, local.publishedIssues);
			else
				arrayAppend(local.arrSeminarSetupSuccess, local.publishedSuccessStatement);
			
			//If sell is ON
			if ((local.isPublisher AND LEN(local.qrySeminar.dateCatalogStart)) OR 
				(!local.isPublisher AND local.qrySeminarSyndicateObj.sellCatalog)) {
				//Check for Dates and Rates
				if(LEN(local.qrySeminar.dateCatalogEnd) AND local.qrySeminar.dateCatalogEnd LT now())  
					arrayAppend(local.arrSeminarSetupIssues, local.expiredDatesIssue);
				else if(LEN(local.qrySeminar.dateCatalogStart) AND local.qrySeminar.dateCatalogStart GT now())
					arrayAppend(local.arrSeminarSetupIssues, local.sellDatesInFutureIssue);
				else local.validDates = true;
				if(local.qryRates.recordCount EQ 1 AND !LEN(local.qryRates.rateID)) 
					arrayAppend(local.arrSeminarSetupIssues, local.ratesIssue);
				else local.validRates = true;
			}
			else 
			{ 	//If it is part of Bundle then check for any active Bundle. If not found then go inside
				if (local.qryBundleDetails.recordCount > 0) {
					if(local.activeBundles.recordCount EQ 0) 
						arrayAppend(local.arrSeminarSetupIssues, local.notSoldInCatalogIssue);
				}//If it is not part of Bundle then it is standalone seminar and sell is OFF
				else 
					arrayAppend(local.arrSeminarSetupIssues, local.notSoldInCatalogIssue);
			}
			if(!local.qrySeminar.AllowRegistrants)
				arrayAppend(local.arrSeminarSetupIssues, "New registrations are disallowed on the Registrants tab.");
			else
				arrayAppend(local.arrSeminarSetupSuccess, "New registrations are allowed on the Registrants tab.");	

			if (local.qryBundleDetails.recordCount > 0) { 
				if(local.activeBundles.recordCount > 0) {
					if(local.validDates AND local.validRates)
						//If Active and Selling
						arrayAppend(local.arrSeminarSetupSuccess, local.sellInCatalogWithActiveBundleSuccess & local.bundleLinks);
					else //Active bundle and not selling	
						arrayAppend(local.arrSeminarSetupSuccess, local.notSellInCatalogInactiveBundleStatement & local.bundleLinks);
				}//Inactive bundle and selling
				else if(local.validDates AND local.validRates)
					arrayAppend(local.arrSeminarSetupSuccess, local.sellingSuccessStatement);
			}//Standalone seminar  
			else if(local.validDates AND local.validRates)
				arrayAppend(local.arrSeminarSetupSuccess, local.sellingSuccessStatement);
			
			appendBreadCrumbs(arguments.event,{ link='', text=left(encodeForHTML(local.qrySeminar.seminarName),50) });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWOD_program.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWODRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.gridmode = arguments.event.getValue('gridmode','');
			
			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'));
				
			if (local.gridmode EQ "exportregsearch") {
				local.pKeyword = arguments.event.getTrimValue('frpKeyword','');
				local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');
				local.pPublisherType = arguments.event.getTrimValue('frpPubType','');
				local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
				local.rCompleted = arguments.event.getValue('frrCompleted','');
				local.rdateFrom = arguments.event.getValue('frrDateFrom','');
				local.rdateTo = arguments.event.getValue('frrDateTo','');
				local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
				local.cdateFrom = arguments.event.getValue('frrDateCompletedFrom','');
				local.cdateTo = arguments.event.getValue('frrDateCompletedTo','');
				
				local.reportFileName = "SWOD-Registrants.csv";

				CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'),
					mode="exportregsearch", seminarID=0, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, completed=local.rCompleted, 
					cdateFrom=local.cdateFrom, cdateTo=local.cdateTo, memberID=0, pKeyword=local.pkeyword, pProgramCode=local.pProgramCode, pPublisherType=local.ppublisherType, 
					pHideInactive=local.pHideInactive, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, 
					fieldSetID=arguments.event.getValue('fsid',0), exportFieldIDList=arguments.event.getTrimValue('swfidlist',''));
			} else {
				local.seminarId = arguments.event.getTrimValue('pid');
				local.rdateFrom = arguments.event.getValue('rDateFrom','');
				local.rdateTo = arguments.event.getValue('rDateTo','');
				local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
				local.cdateFrom = arguments.event.getValue('cDateFrom','');
				local.cdateTo = arguments.event.getValue('cDateTo','');
				local.completed = arguments.event.getTrimValue('fCompleted','');
				
				local.reportFileName = "SWOD-#local.seminarId#-Registrants.csv";

				CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'),
					mode="export", seminarID=local.seminarId, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, completed=local.completed, 
					cdateFrom=local.cdateFrom, cdateTo=local.cdateTo, memberID=0, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName,
					fbFormID=arguments.event.getValue('swformid',0), fieldSetID=arguments.event.getValue('fsid',0), exportFieldIDList=arguments.event.getTrimValue('swfidlist',''));
			}
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					doDownloadSWProgramReg('#local.stDownloadURL#');
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="viewProgress" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strAccessDetails = structNew()>

		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
		<cfset local.objAdminSWOD = CreateObject("component","seminarWebSWOD")>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>
		<cfset local.gridMode = arguments.event.getTrimValue('gridMode','grid')>

		<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfif local.qryEnrollmentInfo.recordcount is 0>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.hasManageSWODRegResponseRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegResponseSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegResponseAll',0) is 1>
		<cfset local.hasManageSWODRegProgressRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegProgressSignUp',0) is 1 AND local.qryEnrollmentInfo.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegProgressAll',0) is 1>
		<cfif NOT local.hasManageSWODRegProgressRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(local.enrollmentID,1)>
		<cfset local.strProgress = local.objSWOD.getProgressByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfset local.qryFiles = local.objSWOD.getFilesByEnrollmentID(enrollmentID=local.enrollmentID)>
		<cfset local.qryMemberData = local.objSWCommon.getMemberInfoForSemWeb(enrollmentID=local.enrollmentID)>
		<cfset local.qrySeminarForms = CreateObject("component","seminarWebSWL").getSeminarForms(seminarID=local.qryEnrollment.seminarID)>

		<cfset local.viewProgressDetailLink = "#buildCurrentLink(arguments.event,"viewProgress")#&eid=#local.enrollmentID#&pid=#local.qryEnrollment.seminarID#&from=#arguments.event.getTrimValue('from','')#&gridMode=#local.gridMode#&mode=direct">
		<cfset local.activityLogDownloadLink = buildCurrentLink(arguments.event,"generateActivityLogDownload") & "&eid=#local.enrollmentID#&mode=stream">
		<cfset local.debugLogDownloadLink = buildCurrentLink(arguments.event,"generateDebugLogDownload") & "&eid=#local.enrollmentID#&mode=stream">
		<cfset local.accessDetailsDownloadLink = buildCurrentLink(arguments.event,"generateAccessDetailsDownload") & "&eid=#local.enrollmentID#&mode=stream">
		<cfset local.viewResponseLink = buildLinkToTool(toolType='evaluationsAdmin',mca_ta='viewResponse') & "&eid=#local.enrollmentID#&pid=#local.qryEnrollment.seminarID#&from=#arguments.event.getTrimValue('from','')#&gridMode=#local.gridMode#">

		<cfoutput query="local.qryFiles" group="fileID">
			<cfquery name="local.qryfiledetails" dbtype="query">
				select *
				from [local].qryFiles
				where fileID = #local.qryFiles.fileID#
			</cfquery>
			<cfset local.strAccessDetails[local.qryFiles.fileID] = local.objSWOD.bitORFiles(local.qryfiledetails)>
		</cfoutput>

		<cfquery name="local.qrySessions" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select logaccessID, dateEntered, dateLastModified, timeSpent
			from dbo.tblLogAccessSWOD
			where enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.enrollmentID#">
			order by dateEntered desc

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.numSeconds = arraySum(listToArray(valueList(local.qrySessions.timeSpent)))>
		<cfset local.stTimeSpent = local.objSWOD.generateTimeLengthString(val(local.numSeconds))>

		<cfset local.isCompleted = false>
		<cfif len(local.strProgress.qryProgress.datecompleted)>
			<cfset local.isCompleted = true>
		</cfif>

		<cfset local.qryCreditSelections = local.objAdminSWOD.getCreditSelections(enrollmentID=local.qryEnrollment.enrollmentID)>
		<cfset local.updateDatesFormLink = buildCurrentLink(arguments.event,"updateSWODEnrolleeDatesAndCredits") & "&eid=#local.enrollmentID#&pid=#local.qryEnrollment.seminarID#&from=#arguments.event.getTrimValue('from','')#&mode=stream">
		
		<cfset local.selectedTab = arguments.event.getTrimValue("tab","completion")>
		<cfset local.lockTab = "">
		<cfif arguments.event.getTrimValue("lockTab","false")>
			<cfset local.lockTab = local.selectedTab>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWOD_enrollment_progress.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateSWODEnrolleeDatesAndCredits" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.enrollmentID = int(val(arguments.event.getTrimValue('eid',0)))>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>

		<cfset createObject('component','seminarwebSWOD').updateEnrolleeDatesAndCredits(event=arguments.event, enrollmentID=local.enrollmentID)>

		<cfset local.viewProgressDetailLink = "#buildCurrentLink(arguments.event,"viewProgress")#&eid=#local.enrollmentID#&pid=#local.seminarID#&from=#arguments.event.getTrimValue('from','')#&gridMode=#arguments.event.getTrimValue('gridMode','grid')#&tab=updatedates&mode=direct">

		<cflocation url="#local.viewProgressDetailLink#" addtoken="false">
	</cffunction>
	
	<cffunction name="listSWCP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.listSWCPProgramsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWCPList&mode=stream';
			local.programsExportLink = buildCurrentLink(arguments.event,"exportSWCPPrograms") & "&mode=stream";
			local.editProgramLink = buildCurrentLink(arguments.event,"editSWCPProgram");

			local.pageHeading = arguments.event.getValue('mc_siteInfo.swcpBrand');
			local.selectedTab = arguments.event.getTrimValue("tab","programs");
			local.lockTab = "";
			if (arguments.event.getTrimValue("lockTab","false")) 
				local.lockTab = local.selectedTab;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWCP.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportSWCPPrograms" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.keyword = arguments.event.getValue('fKeyword','');
			
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "SWCPPrograms.csv";
			
			CreateObject("component","model.admin.seminarWeb.seminarwebSWCP").getPrograms(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), 
				mode='export', keyword=local.keyWord, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWCP('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWODTitle" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.titleID = int(val(arguments.event.getTrimValue('pid',0)));
			local.programAdded = arguments.event.getValue('programAdded',false);
			local.seminarID = arguments.event.getValue('seminarID',0);
			local.editSWODProgramLink = buildCurrentLink(arguments.event,"editSWODProgram") & "&pid=#local.seminarID#";

			if (local.titleID eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");
	
			local.selectedTab = event.getTrimValue("tab","details");
			local.lockTab = event.getTrimValue("lockTab","false") ? local.selectedTab  : "";

			local.strTitle = CreateObject("component","model.seminarweb.SWTitles").getTitle(titleID=local.titleID);
			
			if (val(local.strTitle.qryTitle.titleID) is 0) {
				application.objCommon.redirect("#this.link.message#&message=1");
			} else {
				local.tmpRights = arguments.event.getValue('mc_adminToolInfo.myRights');
				local.isPublisher = local.strTitle.qryTitle.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode');

				local.hasEditSWTLRights = (local.tmpRights.editSWODProgramAll is 1 OR local.tmpRights.editSWODProgramPublish is 1) AND local.isPublisher;
				local.hasLockSWProgramRights = local.hasEditSWTLRights AND local.tmpRights.lockSWProgram is 1;
				local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isProgramLocked(titleID=local.titleID);
				local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteinfo.sitecode'));
				local.qryAssociation = local.strAssociation.qryAssociation;

				/* inactive syndicated program */
				if (NOT local.isPublisher AND NOT createObject("component","seminarWebSWCommon").isActiveOptedIntoProgram(participantID=local.qryAssociation.participantID, programID=local.titleID, programType='SWTL'))
					application.objCommon.redirect("#this.link.message#&message=1");

				if (local.hasEditSWTLRights) {
					/* Details Tab */
					local.updateSWTLProgramDetailsLink = buildCurrentLink(arguments.event,"updateSWTLProgramDetails") & "&mode=stream";

					/* Files Tab */
					local.getTitleFilesInfoLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getTitleFilesInfo&pid=#local.titleID#&mode=stream";
					local.editSWTLFileLink = buildCurrentLink(arguments.event,"editSWTLFile") & "&pid=#local.titleID#&mode=stream";
					local.saveSWTLFilesInfoLink = buildCurrentLink(arguments.event,"saveSWTLFilesInfo") & "&pid=#local.titleID#&mode=stream";
					local.replaceSWTLFileLink = buildCurrentLink(arguments.event,"replaceSWTLFile") & "&pid=#local.titleID#&mode=direct";
				}
			}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWTL_program.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editSWCPProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = StructNew();
			local.programID = int(val(arguments.event.getTrimValue('pid',0)));

			if (local.programID eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");
			
			local.selectedTab = arguments.event.getTrimValue("tab","details");
			local.lockTab = "";
			if(arguments.event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}

			local.objSWCP = CreateObject("component","model.seminarweb.SWCertPrograms");
			local.qryProgram = local.objSWCP.getProgram(programID=local.programID);

			if (val(local.qryProgram.programID) is 0 OR val(local.qryProgram.isSWCP) is 0) {
				application.objCommon.redirect("#this.link.message#&message=1");
			} else {
				local.hasEditRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWCPProgram') and local.qryProgram.orgCode eq arguments.event.getValue('mc_siteInfo.sitecode');
				if (local.hasEditRights) {
					/* Details Tab */
					local.updateSWCPProgramDetailsLink = buildCurrentLink(arguments.event,"updateSWCPProgramDetails");

					/* Included Seminars Tab */
					local.SWCPIncSeminarsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWCPIncludedSeminars&mode=stream&pID=#local.programID#";
					local.editSWLProgram = buildCurrentLink(arguments.event,"editSWLProgram");
					local.editSWODProgram = buildCurrentLink(arguments.event,"editSWODProgram");
				} else {
					/* Summary Tab */
					local.qryItems = local.objSWCP.getItems(programID=local.qryProgram.programID);
				}

				local.hasManageSWCPRegistrantsRights = ((arguments.event.getValue('mc_adminToolInfo.myRights.manageSWCPRegistrantsPublish') is 1 and local.qryProgram.orgCode eq arguments.event.getValue('mc_siteInfo.sitecode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWCPRegistrantsAll') is 1);
				if (local.hasManageSWCPRegistrantsRights) {
					local.SWCPRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWCPRegistrants&mode=stream&pID=#local.programID#";
					local.exportRegistrantsLink = buildCurrentLink(arguments.event,"exportSWCPRegistrants") & "&mode=stream&pID=#local.programID#";
					local.viewProgressLink = buildCurrentLink(arguments.event,"viewProgressSWCP") & "&mode=direct";
					local.viewProgressDetailLink = buildCurrentLink(arguments.event,"viewProgress") & "&mode=direct";
				}

				appendBreadCrumbs(arguments.event,{ link='', text=left(htmleditformat(local.qryProgram.programName),50) });
			}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWCP_program.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportSWCPRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.programID = arguments.event.getTrimValue('pid');

			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'));
			local.reportFileName = "SWCPRegExport.csv";
			
			CreateObject("component","model.admin.seminarWeb.seminarwebSWCP").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'),
				mode="export", programID=local.programID, memberID=0, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName,
				searchValue=arguments.event.getTrimValue('fSearch',''));
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">doExportSWCPRegistrants('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="viewProgressSWCP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = StructNew();
			local.objSWCP = CreateObject("component","model.seminarweb.SWCertPrograms");
			local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon");

			local.strAccessDetails = structNew();
			local.programID = int(val(arguments.event.getTrimValue('pid',0)));
			local.depomemberdataID = int(val(arguments.event.getTrimValue('did',0)));
			
			if (local.programID eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");

			local.viewProgressLink = buildCurrentLink(arguments.event,"viewProgressSWCP") & "&mode=direct&pID=#local.programID#";
			local.viewProgressStepTwoLink =buildCurrentLink(arguments.event,"viewProgress")&"&mode=direct";
			local.qryProgram = local.objSWCP.getProgram(programID=local.programID);
			local.qryItems = local.objSWCP.getCertProgramProgressByDepoMemberDataID(local.qryProgram.programID,local.depomemberdataID);

			local.qryMemberData = local.objSWCommon.getMemberInfoForOrg(depoMemberDataID=local.depomemberdataID, orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWCP_enrollment_progress.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageSettings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = StructNew();
			local.hasManageSWSettingsAll = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsAll',0) is 1;
			local.hasManageSWSettingsOwn = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsOwn',0) is 1;

			if (NOT (local.hasManageSWSettingsAll OR local.hasManageSWSettingsOwn)) {
				application.objCommon.redirect('#this.link.message#&message=1');
			}

			local.objSWP = CreateObject("component","model.seminarweb.SWParticipants");
			local.orgCode = arguments.event.getValue('mc_siteInfo.sitecode');
			local.strAssociation = local.objSWP.getAssociationDetails(orgcode=local.orgCode);
			local.qryAssociation = local.strAssociation.qryAssociation;

			var currTab = arguments.event.getValue('tab','details');
			if (currTab eq '') currTab = 'details';
			local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','');
			local.arrTabs = arrayNew(1);

			local.security.ManageAdvancedSettings = checkRights(arguments.event,'ManageAdvancedSettings');

			local.pageHeading = "SeminarWeb Settings";
		</cfscript>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Details', id='details', fn='dsp_SWSettings_details', needAllRightPerms="0" }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.objSWParticipants = createObject("component","seminarWebParticipants")>
			<cfset local.qryAssociationMarketingData = local.objSWP.getAssociationMarketingData(orgcode=local.orgCode)>
			<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="orgIdentityID", selectedValueID=val(local.qryAssociation.orgIdentityID), allowBlankOption=false)>
			<cfset local.qryAds = local.objSWP.getAssociationAds(local.orgCode)>
			<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
			<cfwddx action="wddx2cfml" input="#local.qryAssociation.wddxTimeZones#" output="local.strZones">
			<cfset local.strMasterZones = CreateObject("component","model.seminarweb.SWCommon").getMasterTimeZones()>
			<cfset local.qryNationalPrograms = local.objSWParticipants.getParticipantNationalPrograms(participantID=local.qryAssociation.participantID)>
			<cfset local.qryCurrentProfiles = local.objSWParticipants.getParticipantMerchantProfiles(participantID=local.qryAssociation.participantID)>
			<cfset local.qryWebsiteCarousels = CreateObject("component","model.admin.carousels.carousels").getCarousels(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

			<cfset local.updatePrimaryLogoLink = buildCurrentLink(arguments.event,"updatePrimaryLogo")>
			<cfset local.deleteParticipantLink = buildCurrentLink(arguments.event,"deleteParticipant") & "&mode=stream">

			<!--- revenue GL Account when assn takes the money --->
			<cfif val(local.qryAssociation.revenueGLAccountID) gt 0>
				<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryAssociation.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
				<cfset local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded>
			<cfelse>
				<cfset local.GLAccountPath = "">
			</cfif>
			<cfset local.strRevenueGLAcctWidgetData = { label="Default Revenue GL Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector', 
				idFldName="revenueGLAccountID", idFldValue=val(local.qryAssociation.revenueGLAccountID), pathFldValue=local.GLAccountPath, pathNoneTxt="(no account selected)" }>
			<cfset local.strRevenueGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>
			
			<cfset local.err = arguments.event.getValue('err','')>
			
			<cfset local.qryParticipantEvents = local.objSWParticipants.getParticipantEvents(participantID=local.qryAssociation.participantID)>
			<cfif local.qryParticipantEvents.recordCount>
				<cfset local.savedCalendarID = local.qryParticipantEvents.calendarID>
				<cfset local.savedCategoryIDList = ValueList(local.qryParticipantEvents.categoryID)>
			<cfelse>
				<cfset local.savedCalendarID = 0>
				<cfset local.savedCategoryIDList = "">
			</cfif>
			<cfset local.qryCalendars = CreateObject("component","model.events.calendar").getCalendars(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

			<cfscript>
				local.objSWCommon = createObject("component","seminarWebSWCommon");
				local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector");
				local.objMemberFieldsets = createObject("component","model.admin.memberFieldSets.memberFieldSets");
				local.SWAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'));

				// Reg FieldSets
				local.qryResultsFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.SWAdminSRID, area='SWRegResults', module="SeminarWeb");
				local.resultsFieldsetID = val(local.qryResultsFieldSet.fieldsetID);
				local.resultsUseID = val(local.qryResultsFieldSet.useID);

				local.qryNewAcctFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.SWAdminSRID, area='SWRegNewAcct', module="SeminarWeb");
				local.newAcctFieldsetID = val(local.qryNewAcctFieldSet.fieldsetID);
				local.newAcctUseID = val(local.qryNewAcctFieldSet.useID);

				local.strResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteid'), selectorID="SWRegResults", selectedValue=local.resultsFieldsetID, allowBlankOption=false);
				local.strNewAcctFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteid'), selectorID="SWRegNewAcct", selectedValue=local.newacctFieldsetID,
					allowBlankOption=false);

				local.SWAppSettingsXML = local.objSWCommon.getSWAppSettingsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
				local.showSWRegPhoto = xmlSearch(local.SWAppSettingsXML,'string(/settings/setting[@name="showSWRegPhoto"]/@value)');
				local.forceLoginForSWReg = xmlSearch(local.SWAppSettingsXML,'string(/settings/setting[@name="forceLoginForSWReg"]/@value)');
			</cfscript>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Styling', id='styling', fn='dsp_SWSettings_branding', needAllRightPerms="0" }>


		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Email Templates', id='emailTemplates', fn='/model/admin/emailTemplates/dsp_templates', needAllRightPerms="0" }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfscript>
				local.strETData = { 
					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					treeCode='ETSEMWEB',
					title="SeminarWeb Email Templates ", 
					intro="Here you manage email templates used by SeminarWeb.",
					gridext="#this.siteResourceID#_1",
					gridwidth=690,
					initGridOnLoad=true
				};
				local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
			</cfscript>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Image Settings', id='featuredImages', fn='dsp_SWSettings_featuredImages', needAllRightPerms="0" }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
			<cfset local.qryFeaturedImageConfigs = local.objFeaturedImages.getFeaturedImageConfigsForSite(siteID=arguments.event.getValue('mc_siteInfo.siteID'), excludeEmpty=1)>
			<cfset local.qryFeaturedImageConfigs.sort("isPlatformWide","desc")>
			<cfset local.qrySiteFeaturedImageConfigs = local.qryFeaturedImageConfigs.filter(
															function(thisRow) { 
																return arguments.thisRow.isPlatformWide eq 0; 
															})>

 			<cfset local.arrSWProgramFeaturedImgSizeConfigs = [ { "sizeReferenceType":"swProgramDetail", "sizeColumnName":"swProgramDetailFeatureImageSizeID" },
 																{ "sizeReferenceType":"swProgramListings", "sizeColumnName":"swProgramListingsFeatureImageSizeID" },
 																{ "sizeReferenceType":"swFeaturedProgramLanding", "sizeColumnName":"swFtdProgramLandingFeatureImageSizeID" },
																{ "sizeReferenceType":"swOtherProgramDetail", "sizeColumnName":"swOtherProgramDetailFeatureImageSizeID" } ]>
			<cfset local.qrySWProgramFeaturedImgSetttings = local.objFeaturedImages.getFeaturedmageConfigSettings(configReferenceID=arguments.event.getValue('mc_siteInfo.siteID'), 
															configReferenceType="swProgram", configParentTable="dbo.sites", configParentTableReferenceCol="siteID",
															configColumnName="swProgramFeatureImageConfigID", arrFeaturedImgSizeConfigs=local.arrSWProgramFeaturedImgSizeConfigs)>

			<!--- format default images --->
			<cfif val(local.qrySWProgramFeaturedImgSetttings.swProgramFeatureImageConfigID)>
				<cfset local.imageUUID = createUUID()>
				<cfset local.SWSiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='SW')>

				<cfif len(arguments.event.getValue('mc_siteinfo.swlBrand'))>
					<cfset local.qrySWLPlatformImage = local.objFeaturedImages.getFeaturedImageDetails(referenceID=local.SWSiteID, referenceType='platformSWLProgram')>
					<cfset local.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qrySWLPlatformImage.orgCode)#/#LCASE(local.qrySWLPlatformImage.siteCode)#/featuredimages/originals/">

					<cfset local.arrSWLConfig=[{
						"ftdExt":"#this.siteResourceID#_swl", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
						"controllingReferenceType":"swProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
						"referenceType":"defaultSWLProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb Live", 
						"onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWFtdImgSettings", "header":"", 
						"ftdImgClassList":"pl-3 mb-5", "enableToggle":1 
					}]>
					<cfset local.strSWLFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), arrConfigs=local.arrSWLConfig)>

				</cfif>
				<cfif len(arguments.event.getValue('mc_siteinfo.swodBrand'))>
					<cfset local.qrySWODPlatformImage = local.objFeaturedImages.getFeaturedImageDetails(referenceID=local.SWSiteID, referenceType='platformSWODProgram')>
					<cfset local.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qrySWODPlatformImage.orgCode)#/#LCASE(local.qrySWODPlatformImage.siteCode)#/featuredimages/originals/">

					<cfset local.arrSWODConfig = [{
						"ftdExt":"#this.siteResourceID#_swod", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
						"controllingReferenceType":"swProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
						"referenceType":"defaultSWODProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb OnDemand", 
						"onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWFtdImgSettings", "header":"", 
						"ftdImgClassList":"pl-3 mb-5", "enableToggle":1
					}]>

					<cfset local.strSWODFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), arrConfigs=local.arrSWODConfig)>
				</cfif>

				<cfset local.qrySWBPlatformImage = local.objFeaturedImages.getFeaturedImageDetails(referenceID=local.SWSiteID, referenceType='platformSWBProgram')>
				<cfset local.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qrySWBPlatformImage.orgCode)#/#LCASE(local.qrySWBPlatformImage.siteCode)#/featuredimages/originals/">

				<cfset local.arrSWBConfig = [{
					"ftdExt":"#this.siteResourceID#_swb", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
					"controllingReferenceType":"swProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
					"referenceType":"defaultSWBProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb Bundles", 
					"onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWFtdImgSettings", "header":"", 
					"ftdImgClassList":"pl-3 mb-5", "enableToggle":1
				}]>

				<cfset local.strSWBFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), arrConfigs=local.arrSWBConfig)>

				<cfset local.qryMCEVPlatformImage = local.objFeaturedImages.getFeaturedImageDetails(referenceID=local.SWSiteID, referenceType='platformMCEVProgram')>
				<cfset local.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qryMCEVPlatformImage.orgCode)#/#LCASE(local.qryMCEVPlatformImage.siteCode)#/featuredimages/originals/">

				<cfset local.arrEVConfig=[{
					"ftdExt":"#this.siteResourceID#_mcev", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
					"controllingReferenceType":"swProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
					"referenceType":"defaultMCEVProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"MemberCentral Events", 
					"onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWFtdImgSettings", "header":"", 
					"ftdImgClassList":"pl-3 mb-5", "enableToggle":1
				}]>

				<cfset local.strEVFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), arrConfigs=local.arrEVConfig)>
			</cfif>
			
			<cfset local.arrSponsorFeaturedImgSizeConfigs = [ { "sizeReferenceType":"viewEventDetails", "sizeColumnName":"evSponsorFeatureImageSizeID" },
																{ "sizeReferenceType":"viewSemwebDetails", "sizeColumnName":"swSponsorFeatureImageSizeID" } ]>
			<cfset local.qrySponsorFeaturedImgSetttings = local.objFeaturedImages.getFeaturedmageConfigSettings(configReferenceID=arguments.event.getValue('mc_siteInfo.siteID'), 
															configReferenceType="evSiteSponsor", configParentTable="dbo.sites", configParentTableReferenceCol="siteID",
															configColumnName="sponsorFeatureImageConfigID", arrFeaturedImgSizeConfigs=local.arrSponsorFeaturedImgSizeConfigs)>

			<cfset local.arrSpeakerFeaturedImgSizeConfigs = [ { "sizeReferenceType":"viewSemwebDetails", "sizeColumnName":"swSpeakerFeatureImageSizeID" } ]>
			<cfset local.qrySpeakerFeaturedImgSetttings = local.objFeaturedImages.getFeaturedmageConfigSettings(configReferenceID=arguments.event.getValue('mc_siteInfo.siteID'), 
															configReferenceType="swSiteSpeaker", configParentTable="dbo.sites", configParentTableReferenceCol="siteID",
															configColumnName="swSpeakerFeatureImageConfigID", arrFeaturedImgSizeConfigs=local.arrSpeakerFeaturedImgSizeConfigs)>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Subject Areas', id='subjectAreas', fn='dsp_SWSettings_subjectAreas', needAllRightPerms="0" }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.editSubjectAreaLink		= buildCurrentLink(arguments.event,"editSubjectArea")  & "&mode=direct">
			<cfset local.categoriesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWSubjectAreasList&mode=stream'>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWSettings.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSubjectArea" access="public" output="false" returntype="struct" hint="add/edit Subject Area">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();
			local.categoryid = val(arguments.event.getValue('categoryid',0));
			local.categoryname = "";
			if(local.categoryid gt 0){
				local.qryCategory = CreateObject("model.seminarWeb.SWCategories").getCategoryByCategoryID(categoryID=local.categoryID);
				local.categoryname = local.qryCategory.categoryName;
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWSettings_editsubjectArea.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="updateBranding" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = StructNew();
			local.hasManageSWSettingsAll = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsAll',0) is 1;
			local.hasManageSWSettingsOwn = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsOwn',0) is 1;

			if (NOT (local.hasManageSWSettingsAll OR local.hasManageSWSettingsOwn)) {
				application.objCommon.redirect('#this.link.message#&message=2');
			}

			createObject("component","SeminarWebParticipants").updateBranding(event=arguments.event);
			application.objCommon.redirect('#this.link.manageSettings#&tab=styling&refreshAdminNavData=1');
		</cfscript>
	</cffunction>

	<cffunction name="deleteParticipant" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.hasManageSWSettingsAll = arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsAll',0) is 1;
			local.hasManageAdvancedSettings = checkRights(arguments.event,'ManageAdvancedSettings') is 1;

			if (NOT (local.hasManageSWSettingsAll AND local.hasManageAdvancedSettings)) {
				application.objCommon.redirect('#this.link.message#&message=2');
			}
		</cfscript>

		<cfif arguments.event.getValue('delConfirm') eq "DELETE">
			<cfset CreateObject("component","SeminarWebParticipants").deleteParticipant(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
			<cflocation url="#arguments.event.getValue('mc_adminNav.adminHome')#" addtoken="no">
		</cfif>	

		<cflocation url="#this.link.manageSettings#" addtoken="no">
	</cffunction>

	<cffunction name="updatePrimaryLogo" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strPrimaryLogo = { file='', fileExt='', error='' }>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsAll',0)>
			<cflocation url="#this.link.message#&message=2" addtoken="false">
		</cfif>

		<cfif len(arguments.event.getValue('publisherLogo',''))>
			<cftry>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfset local.destination = "#application.paths.RAIDUserAssetRoot.path#common/semwebimages/logos/">
				<cffile action="upload" filefield="publisherLogo" destination="#local.strFolder.folderPath#" nameConflict="makeUnique" result="local.upload" accept="image/*">

				<cfset local.strPrimaryLogo.file = local.upload.serverFile>
				<cfset local.strPrimaryLogo.fileExt = local.upload.serverFileExt>

				<cfif not DirectoryExists(local.destination)>
					<cfset DirectoryCreate(local.destination)>
				</cfif>

				<cffile action="copy" source="#local.strFolder.folderPath#/#local.strPrimaryLogo.file#" destination="#local.destination##arguments.event.getValue('mc_siteInfo.sitecode')#.#local.strPrimaryLogo.fileExt#">
			<cfcatch>
				<cfset local.strPrimaryLogo.error = cfcatch.message>
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfif NOT len(local.strPrimaryLogo.error)>
			<cfquery name="local.updateAssociation" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @participantID int;
				SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.siteCode')#">);

				UPDATE dbo.tblParticipants 
				SET usePublisherLogo = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('usePublisherLogo',0)#">
					<cfif len(local.strPrimaryLogo.fileExt)>
						, logoFileExtension = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strPrimaryLogo.fileExt#">
					</cfif>
				WHERE participantID = @participantID;
			</cfquery>

			<cflocation url="#this.link.manageSettings#&tab=details&showBadge=primarylogo" addtoken="no">
		<cfelse>
			<cflocation url="#this.link.manageSettings#&tab=details&err=#urlEncode(local.strPrimaryLogo.error)#" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="showSpeakerSendInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.formLink = buildCurrentLink(arguments.event,"sendSpeakerInstructions") & "&mode=direct">
		<cfset local.objSWLSeminar = createObject("component","model.admin.seminarweb.seminarwebSWL")>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
		<cfset local.participantID = local.participantData.participantID>
		<cfset local.scheduledTaskDetails = local.objSWLSeminar.getScheduledTaskDetails(local.participantID)>
		<cfset local.showInfoAlert = LEN(local.qrySeminar.dateStart) AND local.qrySeminar.dateStart GT now() AND local.scheduledTaskDetails.isSpeakerInstructionsEnabled>

		<cfif local.showInfoAlert>
			<cfset var timeFormatter = function(timeframe) {
				if (timeframe == "1d") {
					return "1 day";
				} else if (timeframe == "1h") {
					return "1 hour";
				} else if (Right(timeframe, 1) == "d") {
					return Replace(timeframe, "d", " days", "all");
				} else if (Right(timeframe, 1) == "h") {
					return Replace(timeframe, "h", " hours", "all");
				}
				return timeframe;
			}>
		
			<!--- Use listMap to transform the list based on the callback function --->
			<cfset local.formattedTimeframes = listMap(local.scheduledTaskDetails.speakerSelectedTimeframes, timeFormatter)>
			<cfset local.formattedTimeframes = Replace(formattedTimeframes, ",", ", ", "all")>
		</cfif>
		
		<cfif arguments.event.getTrimValue('all',0) is 1>
			<cfset local.contentMode = "all">
		<cfelse>
			<cfset local.contentMode = "single">
		</cfif>
		<cfset local.strEmailContent = CreateObject("component","seminarWebAuthors").generateSpeakerConnectionEmail(seminarID=arguments.event.getValue('pid',0), authorID=arguments.event.getValue('aid',0), mode=local.contentMode)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_speakerSendInstructions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sendSpeakerInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structnew()>
		<cfif arguments.event.getValue('authorID',0) EQ 0>
			<cfset CreateObject("component","model.admin.seminarweb.seminarWebSWL").sendSpeakerInstructionsToAllAuthors(seminarID=arguments.event.getValue('seminarID',0), performedBy=session.cfcuser.memberdata.depoMemberDataID, outgoingType="manualResendInstructions", allowResend=true, customText=arguments.event.getValue('customText',''), isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.swl_speaker_table.draw();
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>Speaker instructions successfully sent to all speakers listed for the webinar.</span>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.emailSent = CreateObject("component","seminarWebAuthors").sendSpeakerInstructions(
				seminarID=arguments.event.getValue('seminarID',0),
				authorID=arguments.event.getValue('authorID',0),
				performedBy=session.cfcuser.memberdata.depoMemberDataID,
				outgoingType="manualResendInstructions",
				orgCode=arguments.event.getValue('mc_siteinfo.sitecode'),
				emailOverride=arguments.event.getValue('emailOverride',''),
				customText=arguments.event.getValue('customText',''),
				isImportantCustomText=arguments.event.getTrimValue('isImportantText',0)
			)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.swl_speaker_table.draw();
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<cfif local.emailSent>
							<span>E-mail sent.</span>
						<cfelse>
							<span>Invalid Email Address.</span>
						</cfif>		
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editAuthor" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.authorID = arguments.event.getValue('aid',0)>
		<cfset local.siteCode = arguments.event.getValue('siteCode', arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.isPublisher = arguments.event.getValue('isPublisher',true)>
		<cfset local.readOnly = ''>
		<cfif NOT local.isPublisher>
			<cfset local.readOnly = 'readOnly'>
		</cfif>

		<cfset local.qryAuthor = createObject("component","model.seminarWeb.SWAuthors").getAuthor(authorID=local.authorID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"saveAuthor") & "&mode=stream&swtype=#local.swType#&siteCode=#local.siteCode#">
		<cfset local.authorID = val(local.qryAuthor.authorID)>

		<cfset local.editSWLProgram = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWLProgram',navMethod='listSWL')>
		<cfset local.editSWODProgram = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWODProgram',navMethod='listSWOD')>
		
		<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.featureImageConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.event.getValue('mc_siteinfo.siteid'), referenceType="swSiteSpeaker")>

		<cfif local.authorID gt 0>
			<cfset local.dataTableRootURL = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&aid=#local.authorID#&mode=stream&meth='>
			<cfset local.authorSeminarsListLink = '#local.dataTableRootURL#getAuthorSeminars'>
			<cfset local.exportSWAuthorProgramsLink = buildCurrentLink(arguments.event,"exportSWAuthorPrograms") & "&mode=stream">
			<cfif local.featureImageConfigID gt 0>
				<cfset local.arrConfigs = [ { "ftdExt":"#local.authorID#_swauthor", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
					"controllingReferenceType":"swSiteSpeaker", "referenceID":local.authorID, "referenceType":"SWSpeaker", 
					"resourceType":"SeminarWebAdmin", "resourceTypeTitle":"#local.qryAuthor.firstName# #local.qryAuthor.lastName#", 
					"preEditHandler":"editSWAuthorPhotoPreHandler", "onDeleteImageHandler":"", "onSaveImageHandler":"", "header":'', "ftdImgClassList":"pl-3" 
				} ]>
				<cfset local.strFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), arrConfigs=local.arrConfigs)>
			</cfif>
			<cfset local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(local.siteCode).defaultTimeZoneID>
			<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
			<cfset local.timeZoneAbbr = local.objTZ.getTZAllFromTZID(timeZoneID=local.defaultTimeZoneID).timeZoneAbbr>
		<cfelse>
			<cfset local.saveAuthorFtdImageURL = buildCurrentLink(arguments.event,"saveAuthorFeaturedImage") & "&mode=stream">
		</cfif>

		<cfswitch expression="#local.swType#">
			<cfcase value="Authors">
				<cfset local.authorLabel = arguments.event.getValue('atype','')>
				<cfset local.returnTabHeading = "Speakers and Moderators">
			</cfcase>
			<cfdefaultcase>
				<cfset local.authorLabel = ucFirst(arguments.event.getValue('atype','Speaker'))>
				<cfset local.returnTabHeading = "#local.authorLabel#s">
			</cfdefaultcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfif local.authorID gt 0>
				<cfinclude template="frm_SWAuthor.cfm">
			<cfelse>
				<cfinclude template="frm_SWAuthorDetails.cfm">
			</cfif>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveAuthor" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		
		<cfset local.authorID = arguments.event.getValue('authorID',0)>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.authorType = arguments.event.getValue('authorType','Speaker')>

		<cfif local.authorID gt 0>
			<cfset local.operation = "update">
			<cfset createObject("component","seminarWebAuthors").updateAuthor(event=arguments.event)>
		<cfelse>
			<cfset local.operation = "insert">
			<cfset local.authorID = createObject("component","seminarWebAuthors").insertAuthor(event=arguments.event)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					function onCompleteSaveAuthor(){
						<cfif len(arguments.event.getValue('onSaveRetFunction',''))>
							var resObj = {
								action:'#local.operation#',
								authorid:#local.authorID#,
								authorname:'#arguments.event.getTrimValue('lastname','')#, #arguments.event.getTrimValue('firstname','')#'
							};
							top.#arguments.event.getValue('onSaveRetFunction')#(resObj);
						<cfelse>
							top.reloadSWAuthorGrids('#encodeForJavaScript(local.authorType)#');
							top.MCModalUtils.hideModal();
						</cfif>
					}

					<cfif local.operation eq "insert" and val(arguments.event.getTrimValue('hasFtdImage',0))>
						uploadAuthorFtdImage(#val(local.authorID)#, onCompleteSaveAuthor);
					<cfelse>
						onCompleteSaveAuthor();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveAuthorFeaturedImage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.authorID = arguments.event.getValue('authorid',0)>
		<cfset local.featureImageConfigID = arguments.event.getValue('ficid',0)>

		<cfset local.result = createObject("component","model.admin.common.modules.featuredImages.featuredImages").addFeaturedImage(orgCode=arguments.event.getValue('mc_siteinfo.orgcode'),
			siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), fileToUpload="authorFtdImage", referenceID=local.authorID, referenceType="SWSpeaker",
			featureImageConfigIDList=local.featureImageConfigID)>

		<cfreturn returnAppStruct("Uploaded file successfully.","echo")>
	</cffunction>

	<cffunction name="addSWCategory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.categoryID = 0>

		<cfset local.formLink = buildCurrentLink(arguments.event,"insertSWCategory") & "&swType=#local.swType#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWCategory.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertSWCategory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		
		<cfset local.insertResult = createObject("component","seminarWebCategories").insertCategory(
			siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), programID=arguments.event.getValue('programID',0),
			swType=arguments.event.getValue('swType',''), categoryName=arguments.event.getValue('categoryName'))>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.reloadSWSubjects('#local.swType#');
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSeminarForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWForm = createObject("component","seminarWebForm")>

		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.seminarFormID = arguments.event.getValue('sfid',0)>

		<cfset local.qryForm = local.objSWForm.getSeminarForm(seminarFormID=local.seminarFormID)>
		<cfset local.qrySections = local.objSWForm.getSeminarFormSectionsAndQCount(formID=val(local.qryForm.formID))>

		<cfset local.formLink = buildCurrentLink(arguments.event,"saveSeminarForm") & "&swtype=#local.swType#&mode=stream">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWForm.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSeminarForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset createObject("component","seminarWebForm").updateSeminarForm(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.reloadSWForms('#arguments.event.getTrimValue('loadpoint','')#');
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWLink" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.linkID = arguments.event.getValue('linkid',0)>

		<cfset local.qryLink = createObject("component","seminarWebSWCommon").getSWLinkDetails(linkID=local.linkID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWLink.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="manageRegAttendance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.swType = arguments.event.getValue('swtype','')>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>

		<cfset local.tmpRights = arguments.event.getValue('mc_adminToolInfo.myRights')>

		<cfset local.hasManageSWLRegAttendanceRights = (local.tmpRights.manageSWLRegAttendanceSignUp is 1 OR local.tmpRights.manageSWLRegAttendanceAll is 1)>
		<cfif NOT local.hasManageSWLRegAttendanceRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="manageAttendance", 
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',1), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

		<cfset local.formLink = buildCurrentLink(arguments.event,"saveRegAttendance") & "&pid=#local.seminarID#&mode=direct">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrantAttendance.cfm">
		</cfsavecontent>
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRegAttendance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.tmpRights = arguments.event.getValue('mc_adminToolInfo.myRights')>

		<cfset local.hasManageSWLRegAttendanceRights = (local.tmpRights.manageSWLRegAttendanceSignUp is 1 OR local.tmpRights.manageSWLRegAttendanceAll is 1)>
		<cfif NOT local.hasManageSWLRegAttendanceRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset createObject("component","seminarWebSWL").saveRegAttendance(event=arguments.event, seminarID=arguments.event.getValue('pid',0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif LEN(arguments.event.getValue('fromUploadAttendance',''))>
					<script language="javascript">
						top.dofilterSWLProgramRegistrants(); 
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
					</script>
					<div id="successMessage" class="alert alert-success" role="alert">
						<div class="d-flex align-items-center">
							<div class="bg-success rounded-circle d-flex justify-content-center align-items-center" style="width: 30px; height: 30px; margin-right: 10px;">
								<i class="fas fa-check text-white"></i>
							</div>
							<span>
								Attendance successfully uploaded from Zoom Webinar. To continue making changes to your attendance, click 'Manage Attendance' after closing this window.
							</span>
						</div>
					</div>
				<cfelse>
					<script language="javascript">
						top.dofilterSWLProgramRegistrants(); 
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
					<div id="successMessage" class="alert alert-success" role="alert">
						<div class="d-flex align-items-center">
							<div class="bg-success rounded-circle d-flex justify-content-center align-items-center" style="width: 30px; height: 30px; margin-right: 10px;">
								<i class="fas fa-check text-white"></i>
							</div>
							<span>
								Attendance successfully uploaded.
							</span>
						</div>
					</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="uploadAttendance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.swType = arguments.event.getValue('ft','')>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
	
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>

		<cfset local.hasUploadAttendanceRights = arguments.event.getValue('mc_adminToolInfo.myRights.uploadAttendance') and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND listFind("2,3",local.qrySeminar.providerID)>

		<cfif local.hasUploadAttendanceRights>
			<cfif local.qrySeminar.providerID is 3>
				<cfset local.reportLink = buildCurrentLink(arguments.event,"pullWebAttendanceZoomWebinars") & "&pid=#local.seminarID#&mode=stream">
			</cfif>
			<cfset local.downloadReportLink = buildCurrentLink(arguments.event,"downloadWebAttendance") & "&pid=#local.seminarID#&mode=stream">
			<cfset local.uploadXMLFormLink = buildCurrentLink(arguments.event,"saveWebAttendanceReportXML") & "&pid=#local.seminarID#&mode=direct">
			<cfset local.uploadfileFormLink = buildCurrentLink(arguments.event,"saveWebAttendanceReportCSV") & "&pid=#local.seminarID#&mode=direct">
			<cfset local.manageAttendanceLink = buildCurrentLink(arguments.event,"manageRegAttendance") & "&pID=#local.seminarId#&ft=SWL&mode=direct">
			<cfset local.fromUploadAttendance = 1>
			<cfset local.formLink = buildCurrentLink(arguments.event,"saveRegAttendance") & "&pid=#local.seminarID#&mode=stream">

			<cfif local.qrySeminar.providerID is 3>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_uploadRegistrantAttendance.cfm">
				</cfsavecontent>
			<cfelse>
				<cfset local.data = '<div class="p-2">Stream Provider is not eligible for upload.</div>'>
			</cfif>
		<cfelse>
			<cfset local.data = '<div class="p-2">You do not have rights to this section.</div>'>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="pullWebAttendanceZoomWebinars" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="500">

		<cfset local.data = "Unable to pull report from Zoom Webinars. There may not be any participants reported as of yet.">
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.strZoomInfo = CreateObject("component","model.seminarweb.SWZoomWebinar").getRegistrantReport(webinarID=local.qrySeminar.ZoomWebinarID)>
		<cfset local.manageAttendanceLink = buildCurrentLink(arguments.event,"manageRegAttendance") & "&pID=#local.seminarId#&ft=SWL&mode=stream">

		<cfif local.strZoomInfo.success and local.strZoomInfo.arrParticipants.len() gt 0>
			<cfset local.strReturn = CreateObject("component","seminarWebSWL").prepareWebAttendanceZoomWebinars(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), seminarID=local.seminarID, strWebinarReport=local.strZoomInfo)>
			
			<cfif local.strReturn.success>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script type="text/javascript">
							var oReport = new Object();
							#toScript(local.strReturn.csvFolder, "oReport.fnf")#
							#toScript(local.strReturn.csvFileName, "oReport.fn")#
							#toScript(local.strReturn.xmlReport, "oReport.rpt")#
							
							$('##xmlReport').val('#local.strReturn.xmlReport#');
							$('##stepNo2').removeClass('d-none').load('#local.manageAttendanceLink#&' + top.$('##frmRegistrantFilter').serialize());
						</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadWebAttendance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.strDownload = application.objDocDownload.decryptDownloadURL(d=arguments.event.getValue('f',''))>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strDownload.sourceFilePath#/#arguments.event.getValue('fn','')#", displayName=arguments.event.getValue('fn',''), 
			deleteSourceFile=false, forceDownload=true)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveWebAttendanceReportXML" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="300">

		<cfset local.data = "Error Importing Attendance Report. Contact MemberCentral for assistance.">
		<cfset local.objSWLAdmin = createObject("component","seminarWebSWL")>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.qrySeminar = createObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.hasUploadAttendanceRights = arguments.event.getValue('mc_adminToolInfo.myRights.uploadAttendance') and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND listFind("2,3",local.qrySeminar.providerID)>

		<cfif local.hasUploadAttendanceRights AND local.objSWLAdmin.saveWebAttendanceXML(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), seminarID=local.seminarID, performedBy=session.cfcuser.memberdata.depomemberdataid, xmlReport=arguments.event.getValue('xmlReport',''))>
			<cfif local.qrySeminar.offerCertificate is 1>
				<cfset local.objSWLAdmin.autoSendCertificateToAll(event=arguments.event, seminarID=local.seminarID, performedBy=session.cfcuser.memberdata.depomemberdataid)>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfoutput>
				<script language="javascript">
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
				</script>
				<div class="p-3">
					#local.data#
				</div>
			</cfoutput>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveWebAttendanceReportCSV" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="300">

		<cfif len(arguments.event.getValue('frmReport',''))>
			<cfset local.objSWLAdmin = createObject("component","seminarWebSWL")>
			<cfset local.seminarID = arguments.event.getValue('pid',0)>
			<cfset local.qrySeminar = createObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
			<cfset local.hasUploadAttendanceRights = arguments.event.getValue('mc_adminToolInfo.myRights.uploadAttendance') and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND listFind("2,3",local.qrySeminar.providerID)>

			<cfif local.hasUploadAttendanceRights AND local.objSWLAdmin.saveWebAttendanceCSV(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), seminarID=local.seminarID, performedBy=session.cfcuser.memberdata.depomemberdataid)>
				<cfif local.qrySeminar.offerCertificate is 1>
					<cfset local.objSWLAdmin.autoSendCertificateToAll(event=arguments.event, seminarID=local.seminarID, performedBy=session.cfcuser.memberdata.depomemberdataid)>
				</cfif>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.dofilterSWLProgramRegistrants(); 
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
					<div class="p-3">
						<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
							<span class="font-size-lg d-block d-40 mr-2 text-center">
								<i class="fa-solid fa-circle-check"></i>
							</span>
							<span>Attendance successfully uploaded.</span>
						</div>
					</div>
				</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
							window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
						</script>
						<div class="p-3">
							<h4>Error Importing Attendance Report</h4>
							<p>We were not able to import the report. Contact development for assistance.</p>
							<p>Ensure you have removed all columns except <b>enrollmentID, enterDate, exitDate, numMinutes, polling</b></p>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		<cfelse>
			<cfset local.data = "<h4>Error Importing Attendance Report</h4><p>File uploaded was not a valid Attendance report. Try again.</p>">
			<cfoutput>
				<script language="javascript">
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
				</script>
				<div class="p-3">
					#local.data#
				</div>
			</cfoutput>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadSWLZoomWebinarsRecordings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="500">

		<cfset local.data = "<div class='m-2 text-center'>Unable to retrieve the recording files from Zoom Webinars.</div>">
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.strZoomInfo = CreateObject("component","model.seminarweb.SWZoomWebinar").getWebinarRecordings(webinarID=local.qrySeminar.ZoomWebinarID)>
		
		<cfif local.strZoomInfo.success>
			<cfif local.strZoomInfo.arrRecordings.len()>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_SW_program_recordings.cfm">
				</cfsavecontent>
			<cfelse>
				<cfset local.data = "<div class='m-2 text-center'>This webinar does not have any saved recordings at this time.</div>">
			</cfif>
		<cfelseif NOT local.strZoomInfo.success AND structKeyExists(local.strZoomInfo,"message")>
			<cfset local.data = "<div class='m-2 text-center'>#local.strZoomInfo.message#</div>">
		</cfif>
		<cfset local.data = "<div class='card card-box mt-3'><div class='card-header bg-light py-1'><div class='card-header--title font-weight-bold font-size-md'>Recording Files</div></div><div class='card-body p-2'>#local.data#</div></div>"/>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateActivityLogDownload" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.siteCode'))>

		<cfset local.qrySessions = createObject("component","seminarWebSWOD").getActivityLogSessions(enrollmentid=arguments.event.getValue('eid',0), logaccessid=arguments.event.getValue('laid',0))>
		
		<cfset local.fileToWrite = "#local.strFolder.folderPath#/activityLog_#arguments.event.getValue('eid',0)#_#arguments.event.getValue('laid',0)#.txt">
		<cfset local.joFileWriter = createObject('component','model.system.common.FileWriter.FileWriter').init(local.fileToWrite,'US-ASCII',32768)>
		
		<cfloop query="local.qrySessions">
			<cfif len(local.qrySessions.activityLog)>
				<cfset local.joFileWriter.writeLine(local.qrySessions.activityLog)>
			<cfelse>
				<cfset local.fileName = "#local.qrySessions.logAccessID#-activity.txt">
				<cfset local.keyMod = numberFormat(local.qrySessions.logAccessID mod 1000,"0000")>
				<cfset local.objectKey = lcase("swodaccesslogs/#local.keyMod#/#local.fileName#")>
				<cfset local.localFile = "#application.paths.seminarWebFiles.path#SWODAccessLogs\#local.keyMod#\#local.fileName#">

				<!--- look S3 first. If not there, look locally --->
				<cfif application.objS3.s3FileExists(bucket='seminarweb', objectKey=local.objectKey, requestType='vhost')>
					<cfset local.arrAmzHeaders = arrayNew(1)>
					<cfset local.tmpStr = { key="response-content-disposition", value="inline; filename=""#local.fileName#""; filename*=UTF-8''#urlEncodedFormat(local.fileName)#" }>
					<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
					<cfset local.s3FileURL = application.objS3.s3Url(bucket='seminarweb', objectKey=local.objectKey, canonicalizedAmzHeaders=local.arrAmzHeaders)>

					<!--- immediate download to local directory --->
					<cfhttp method="get" url="#local.s3FileURL#" path="#local.strFolder.folderPath#" file="#local.fileName#" timeout="60" throwonerror="true">
					<cffile action="read" file="#local.strFolder.folderPath#\#local.fileName#" variable="local.activityLog">
					<cfset local.joFileWriter.writeLine(local.activityLog)>
				<cfelseif FileExists(local.localFile)>
					<cffile action="read" file="#local.localFile#" variable="local.activityLog">
					<cfset local.joFileWriter.writeLine(local.activityLog)>
				</cfif>
			</cfif>
		</cfloop>
		<cfset local.joFileWriter.close()>
		
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.fileToWrite, displayName="activityLog_#arguments.event.getValue('eid',0)#_#arguments.event.getValue('laid',0)#.txt", 
			deleteSourceFile=false, forceDownload=true)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateDebugLogDownload" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.siteCode'))>

		<cfset local.qrySessions = createObject("component","seminarWebSWOD").getDebugLogSessions(enrollmentid=arguments.event.getValue('eid',0), logaccessid=arguments.event.getValue('laid',0))>

		<cfset local.fileToWrite = "#local.strFolder.folderPath#/debugLog_#arguments.event.getValue('eid',0)#_#arguments.event.getValue('laid',0)#.txt">
		<cfset local.joFileWriter = createObject('component','model.system.common.FileWriter.FileWriter').init(local.fileToWrite,'US-ASCII',32768)>
		<cfloop query="local.qrySessions">
			<cfif len(local.qrySessions.debugLog)>
				<cfset local.joFileWriter.writeLine(local.qrySessions.debugLog)>
			<cfelse>
				<cfset local.fileName = "#local.qrySessions.logAccessID#-debug.txt">
				<cfset local.keyMod = numberFormat(local.qrySessions.logAccessID mod 1000,"0000")>
				<cfset local.objectKey = lcase("swodaccesslogs/#local.keyMod#/#local.fileName#")>
				<cfset local.localFile = "#application.paths.seminarWebFiles.path#SWODAccessLogs\#local.keyMod#\#local.fileName#">

				<!--- look S3 first. If not there, look locally --->
				<cfif application.objS3.s3FileExists(bucket='seminarweb', objectKey=local.objectKey, requestType='vhost')>
					<cfset local.arrAmzHeaders = arrayNew(1)>
					<cfset local.tmpStr = { key="response-content-disposition", value="inline; filename=""#local.fileName#""; filename*=UTF-8''#urlEncodedFormat(local.fileName)#" }>
					<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
					<cfset local.s3FileURL = application.objS3.s3Url(bucket='seminarweb', objectKey=local.objectKey, canonicalizedAmzHeaders=local.arrAmzHeaders)>

					<!--- immediate download to local directory --->
					<cfhttp method="get" url="#local.s3FileURL#" path="#local.strFolder.folderPath#" file="#local.fileName#" timeout="60" throwonerror="true">
					<cffile action="read" file="#local.strFolder.folderPath#\#local.fileName#" variable="local.debugLog">
					<cfset local.joFileWriter.writeLine(local.debugLog)>
				<cfelseif FileExists(local.localFile)>
					<cffile action="read" file="#local.localFile#" variable="local.debugLog">
					<cfset local.joFileWriter.writeLine(local.debugLog)>
				</cfif>
			</cfif>
		</cfloop>
		<cfset local.joFileWriter.close()>
		
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.fileToWrite, displayName="debugLog_#arguments.event.getValue('eid',0)#_#arguments.event.getValue('laid',0)#.txt", 
			deleteSourceFile=false, forceDownload=true)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateAccessDetailsDownload" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "The file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.objPlayer = createObject('component','model.seminarweb.SWODPlayerHTML5')>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.siteCode'))>
		
		<cfquery name="local.qryDetails" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT f.fileID as theFileID, laf.logaccessid, f.fileTypeID, laf.accessDetails, laf.lastTimeCode, laf.timeSpent, f.fileName
			FROM dbo.tblLogAccessSWODFiles AS laf 
			INNER JOIN dbo.tblLogAccessSWOD AS la ON laf.logAccessID = la.logAccessID 
			INNER JOIN dbo.tblFiles AS f ON laf.fileID = f.fileID
				AND f.isDeleted = 0
			WHERE la.enrollmentID = <cfqueryparam value="#arguments.event.getValue('eid',0)#" cfsqltype="CF_SQL_INTEGER">
			<cfif arguments.event.getValue('fid',0) gt 0>
				AND f.fileID = <cfqueryparam value="#arguments.event.getValue('fid',0)#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			ORDER BY f.fileID, laf.fileLogAccessID
		</cfquery>	

		<cfset local.fileToWrite = "#local.strFolder.folderPath#/accessDetails_#arguments.event.getValue('eid',0)#.csv">
		<cfset local.joFileWriter = createObject('component','model.system.common.FileWriter.FileWriter').init(local.fileToWrite,'US-ASCII',32768)>
		<cfset local.joFileWriter.writeLine('fileID,fileName,logaccessid,accessDetails,lastTimeCode,timeSpent,played,notPlayed')>
		
		<cfloop query="local.qryDetails">
			<cfif listFind("3,4",fileTypeID)>
				<cfset local.arrReport = local.objPlayer.getPlayReportFromAccessDetails(accessDetails,1)>
				<cfset local.Played = "">
				<cfset local.notPlayed = "">
				<cfloop from="1" to="#Arraylen(local.arrreport)#" index="local.i">
					<cfif local.arrreport[local.i].hasBeenPlayed is 1>
						<cfset local.Played = listAppend(local.Played, "#generateTimeCodeString(seconds=local.arrreport[local.i].starttime)# - #generateTimeCodeString(seconds=local.arrreport[local.i].endtime)#")>
					<cfelse>
						<cfset local.notPlayed = listAppend(local.notPlayed, "#generateTimeCodeString(seconds=local.arrreport[local.i].starttime)# - #generateTimeCodeString(seconds=local.arrreport[local.i].endtime)#")>
					</cfif>
				</cfloop>
			<cfelse>
				<cfset local.Played = "">
				<cfset local.notPlayed = "">
			</cfif>
			<cfset local.joFileWriter.writeLine('#theFileID#,"#fileName#",#logaccessid#,"*#accessDetails#",#lastTimeCode#,#timeSpent#,"#replace(local.Played, ",", ", ", "ALL")#","#replace(local.notPlayed, ",", ", ", "ALL")#"')>
		</cfloop>
		<cfset local.joFileWriter.close()>
		
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.fileToWrite, displayName="accessDetails_#arguments.event.getValue('eid',0)#.csv", 
			deleteSourceFile=false, forceDownload=true)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateTimeCodeString" access="private" output="false" returntype="string">
		<cfargument name="seconds" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.numhours = int(arguments.seconds / 60 / 60)> 
		<cfset local.numminutes = int((arguments.seconds - (local.numhours * 3600)) / 60)>
		<cfset local.numseconds = arguments.seconds - (local.numhours * 3600) - (local.numminutes * 60)> 
		
		<cfset local.returnString = "#numberformat(local.numhours,"00")#:#numberformat(local.numminutes,"00")#:#numberformat(local.numseconds,"00")#">
	
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="massEmailInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.SWType = arguments.event.getTrimValue('ft','SWL')>
		<cfset local.objSWLSeminar = createObject("component","model.admin.seminarweb.seminarwebSWL")>

		<cfif local.SWType eq 'SWL'>
			<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailInstr", 
				seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
				rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

			<cfif local.qryRegistrants.recordCount is 0>
				<cfreturn returnAppStruct("No Registrants Found.","echo")>
			</cfif>
			
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
			<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
			<cfset local.participantID = local.participantData.participantID>
			<cfset local.scheduledTaskDetails = local.objSWLSeminar.getScheduledTaskDetails(local.participantID)>
			<cfset local.showInfoAlert = LEN(local.qrySeminar.dateStart) AND local.qrySeminar.dateStart GT now() AND 
										local.scheduledTaskDetails.isRegistrantInstructionsEnabled>
			<cfif local.showInfoAlert>
				<cfset var timeFormatter = function(timeframe) {
					if (timeframe == "1d") {
						return "1 day";
					} else if (timeframe == "1h") {
						return "1 hour";
					} else if (Right(timeframe, 1) == "d") {
						return Replace(timeframe, "d", " days", "all");
					} else if (Right(timeframe, 1) == "h") {
						return Replace(timeframe, "h", " hours", "all");
					}
					return timeframe;
				}>
			
				<!--- Use listMap to transform the list based on the callback function --->
				<cfset local.formattedTimeframes = listMap(local.scheduledTaskDetails.registrantSelectedTimeframes, timeFormatter)>
				<cfset local.formattedTimeframes = Replace(formattedTimeframes, ",", ", ", "all")>
			</cfif>
			<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWLiveEmails").generateConfirmationEmail(enrollmentID=val(local.qryRegistrants.enrollmentID[1]))>

			<cfset local.formLink = buildCurrentLink(arguments.event,"doMassEmailInstructions") & "&pid=#local.seminarID#&ft=#local.SWType#&mode=direct">

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_massEmailInstructions.cfm">
			</cfsavecontent>
		</cfif>
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doMassEmailInstructions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.SWType = arguments.event.getTrimValue('ft','SWL')>
		
		<cfif local.SWType EQ "SWL">
			<cfsetting requesttimeout="600">

			<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarid=local.seminarID)>
			<cfset local.qryEnrollments = CreateObject("component","seminarwebSWL").getEnrollments(seminarid=local.seminarID)>

			<cfquery name="local.qryRecipients" dbtype="query">
				select enrollmentID, orgCode
				from [local].qryEnrollments
				where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.event.getValue('enrollmentIDListFiltered','')#">)
				and email <> ''
			</cfquery>

			<cfif local.qryRecipients.recordCount is 0>
				<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
			</cfif>

			<cfloop query="local.qryRecipients">
				<cfset local.objSWLiveEmails.sendConfirmation(seminarID=local.seminarID, enrollmentID=local.qryRecipients.enrollmentID,
					performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="manualResendInstructions", withMaterials=0, orgcode=local.qryRecipients.orgCode, 
					customtext=arguments.event.getTrimValue('customtext',''), isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
			</cfloop>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailSWRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.swType = arguments.event.getValue('ft','')>
		<cfset local.strFilters = structNew()>
		<cfset local.strResourceTitle = { resourceTitle='Registrants', resourceTitleDesc='<b>Note:</b> Only registrants that registered from your site are eligible recipients using this tool. We''ve modified the recipient list automatically.', templateEditorLabel='Compose your message.' }>

		<cfif local.swType EQ "SWL">
			<cfset local.resourceType = "SeminarWebLive">
			<cfset structInsert(local.strFilters, 'pid', arguments.event.getValue('pid',0))>
			<cfset structInsert(local.strFilters, 'ft', local.swType)>
			<cfset structInsert(local.strFilters, 'rAttended', arguments.event.getValue('rAttended',''))>
			<cfset structInsert(local.strFilters, 'rDateFrom', arguments.event.getValue('rDateFrom',''))>
			<cfset structInsert(local.strFilters, 'rDateTo', arguments.event.getValue('rDateTo',''))>
			<cfset structInsert(local.strFilters, 'rHideDeleted', arguments.event.getValue('rHideDeleted',1))>
			<cfset structInsert(local.strFilters, 'rCredits', arguments.event.getValue('rCredits',''))>
		<cfelseif local.swType EQ "SWOD">
			<cfset local.resourceType = "SeminarWebOnDemand">
			<cfset structInsert(local.strFilters, 'pid', arguments.event.getValue('pid',0))>
			<cfset structInsert(local.strFilters, 'ft', local.swType)>
			<cfset structInsert(local.strFilters, 'rDateFrom', arguments.event.getValue('rDateFrom',''))>
			<cfset structInsert(local.strFilters, 'rDateTo', arguments.event.getValue('rDateTo',''))>
			<cfset structInsert(local.strFilters, 'fCompleted', arguments.event.getValue('fCompleted',''))>
			<cfset structInsert(local.strFilters, 'cDateFrom', arguments.event.getValue('cDateFrom',''))>
			<cfset structInsert(local.strFilters, 'cDateTo', arguments.event.getValue('cDateTo',''))>
			<cfset structInsert(local.strFilters, 'rHideDeleted', arguments.event.getValue('rHideDeleted',1))>
		</cfif>
		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType=local.resourceType, 
					recipientType='Registrants', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
					mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&mode=stream",
					emailTemplateTreeCode="ETSEMWEB" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSWCommon = CreateObject("seminarWebSWCommon");
			local.seminarID = arguments.event.getValue('seminarID');
			local.swType = arguments.event.getValue('ft','');

			local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.assocHandlesOwnPayment = local.objSWCommon.doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.qryRate = local.objSWCommon.getSeminarRateByRateID(participantID=local.participantID, rateID=arguments.event.getValue('rateID',0));
			local.qryGetRateGroupings = local.objSWCommon.getSeminarRateGroupsbySeminarID(participantID=local.participantID, seminarID=local.seminarID);
			
			arguments.event.setValue('rateid',val(local.qryRate.rateid));
			arguments.event.setValue('rateGroupingID',val(local.qryRate.rateGroupingID));
			arguments.event.setValue('rateName',local.qryRate.rateName);
			arguments.event.setValue('rate',local.qryRate.rate);
			arguments.event.setValue('isHidden',local.qryRate.isHidden);
			arguments.event.setValue('revenueGLAccountID',val(local.qryRate.revenueGLAccountID));

			if (local.assocHandlesOwnPayment) {
				if (val(local.qryRate.revenueGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryRate.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.GLAccountPath = "";
				}

				local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountID",
					"idFldValue": val(local.qryRate.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses Program's designated GL Account.)",
					"clearBtnTxt": "Remove Override" };
				local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
			}

			local.formlink = buildCurrentLink(arguments.event,"saveSWRate") & "&seminarID=#local.seminarID#&ft=#local.swType#&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SW_program_rates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSWRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.objSWCommon = CreateObject("seminarWebSWCommon");
			local.rateID = arguments.event.getValue('rateID',0);
			local.parentRateID=arguments.event.getValue('parentRateID',0);
			local.copyPerms = int(val(arguments.event.getValue('selCopyPerms',0)));
			local.isSeminarLocked = local.objSWCommon.isSeminarLocked(seminarID=arguments.event.getValue('seminarID',0));
			local.hasSWRateChangeRights = local.objSWCommon.hasSWProgramRateChangeRights(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), programID=arguments.event.getValue('seminarID'), programType=arguments.event.getValue('ft'));
			if (local.isSeminarLocked OR NOT local.hasSWRateChangeRights)
				application.objCommon.redirect("#this.link.message#&message=3");

			local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'));

			local.newRateGrouping = "";
			if (arguments.event.getValue('rateGroupingID','') EQ "new") {
				local.newRateGrouping = arguments.event.getTrimValue('newRateGroupingName','');
				arguments.event.setValue('rateGroupingID',0)
			}

			local.strRate = local.objSWCommon.saveSeminarRate(participantID=local.participantID,
					rateID=arguments.event.getValue('rateID',0),
					seminarID=arguments.event.getValue('seminarID',0), 
					rateGroupingID=arguments.event.getValue('rateGroupingID',0), 
					rateName=arguments.event.getTrimValue('rateName',''),
					rate=replace(arguments.event.getValue('rate',0),'$','','ALL'),
					isHidden=arguments.event.getValue('isHidden',0),
					revenueGLAccountID=arguments.event.getValue('revenueGLAccountID',0),
					newRateGrouping=local.newRateGrouping,
					parentRateID=local.parentRateID,
					copyPerms=local.copyPerms,
					siteID =arguments.event.getValue('mc_siteinfo.siteid')
				);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadSWRatesGrid();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copySWRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objSWCommon = CreateObject("seminarWebSWCommon");
			local.seminarID = arguments.event.getValue('seminarID');
			local.swType = arguments.event.getValue('ft','');
			local.rateID = arguments.event.getValue('rateID',0);
			local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.assocHandlesOwnPayment = local.objSWCommon.doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.qryRate = local.objSWCommon.getSeminarRateByRateID(participantID=local.participantID, rateID=arguments.event.getValue('rateID',0));
			local.qryGetRateGroupings = local.objSWCommon.getSeminarRateGroupsbySeminarID(participantID=local.participantID, seminarID=local.seminarID);
			
			arguments.event.setValue('rateid',val(local.qryRate.rateid));
			arguments.event.setValue('rateGroupingID',val(local.qryRate.rateGroupingID));
			arguments.event.setValue('rateName',local.qryRate.rateName);
			arguments.event.setValue('rate',local.qryRate.rate);
			arguments.event.setValue('isHidden',local.qryRate.isHidden);
			arguments.event.setValue('revenueGLAccountID',val(local.qryRate.revenueGLAccountID));

			if (local.assocHandlesOwnPayment) {
				if (val(local.qryRate.revenueGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryRate.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.GLAccountPath = "";
				}

				local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountID",
					"idFldValue": val(local.qryRate.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses Program's designated GL Account.)",
					"clearBtnTxt": "Remove Override" };
				local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
			}

			local.formlink = buildCurrentLink(arguments.event,"saveSWRate") & "&seminarID=#local.seminarID#&ft=#local.swType#&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SW_program_copy_rates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageCopyRates" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.ft = arguments.event.getValue('ft','')>
		<cfset local.copyAction = arguments.event.getValue('copyAction','')>
		<cfif listFindNoCase("SWL,SWOD", local.ft)>
			<cfset local.programDisplayName = "Program">
		<cfelseif listFindNoCase("SWB", local.ft)>
			<cfset local.programDisplayName = "Bundle">
		</cfif>
		<cfif local.ft eq "SWL">
			<cfset local.fDateFrom = dateFormat(dateAdd("d",-180,now()), "m/d/yyyy")>
			<cfset local.fDateTo = dateFormat(dateAdd("d",30,now()), "m/d/yyyy")>
		</cfif>

		<cfset local.participantID = CreateObject("component","seminarWebSWCommon").getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.programsList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=listSWProgramsForCopyRates&mode=stream&pid=#local.programID#&ft=#local.ft#">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SW_program_copyRates.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="changeRegistrantPrice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSWCommon = CreateObject("component","seminarWebSWCommon")>

		<cfset local.enrollmentID = arguments.event.getValue('eid',0)>
		<cfset local.gridNum = arguments.event.getValue('gnum','')>
		<cfset local.SWType = arguments.event.getTrimValue('ft','SWL')>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfset local.hasChangeEnrollmentFeeRights = false>

		<cfif local.SWType eq 'SWL'>
			<cfset local.qryEnrollment = CreateObject("component","seminarWebSWL").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>

			<cfset local.hasdeleteSWLRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.siteCode')) OR local.tmpRights.deleteSWLRegistrantAll is 1>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasdeleteSWLRegistrantRights AND NOT local.qryEnrollment.handlesOwnPayment AND NOT val(local.qryEnrollment.bundleOrderID)>
		<cfelseif local.SWType eq 'SWOD'>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>

			<cfset local.hasdeleteSWODRegistrantRights = (local.tmpRights.deleteSWODRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.siteCode')) OR local.tmpRights.deleteSWODRegistrantAll is 1>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasdeleteSWODRegistrantRights AND NOT local.qryEnrollment.handlesOwnPayment AND NOT val(local.qryEnrollment.bundleOrderID)>
		<cfelseif local.SWType eq 'SWB'>
			<cfset local.qryBundleOrder = CreateObject("component","model.admin.seminarweb.seminarWebSWB").getBundleOrder(orderID=local.enrollmentID)>

			<cfset local.hasDeleteSWBRegistrantRights = local.qryBundleOrder.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.siteCode') OR application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasDeleteSWBRegistrantRights AND NOT local.qryBundleOrder.handlesOwnPayment>
		</cfif>

		<cfif NOT local.hasChangeEnrollmentFeeRights>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfset local.qryEnrollmentFees = local.objSWCommon.getEnrollmentFees(referenceID=local.enrollmentID, programType=local.SWType)>
		
		<cfif listFindNoCase("SWL,SWOD", local.SWType)>
			<cfset local.strEnrollment = {
				referenceID: local.qryEnrollment.enrollmentID,
				programName: local.qryEnrollment.seminarName,
				depomemberdataID: local.qryEnrollment.depomemberdataID,
				firstName: local.qryEnrollment.firstName,
				lastName: local.qryEnrollment.lastName
			}>
		<cfelseif local.SWType eq "SWB">
			<cfset local.strEnrollment = {
				referenceID: local.qryBundleOrder.orderID,
				programName: local.qryBundleOrder.bundleName,
				depomemberdataID: local.qryBundleOrder.depomemberdataID,
				firstName: local.qryBundleOrder.firstName,
				lastName: local.qryBundleOrder.lastName
			}>
		</cfif>

		<cfset local.strEnrollment.feesTotal = local.qryEnrollmentFees.total>
		<cfset local.strEnrollment.feesTax = local.qryEnrollmentFees.tax>

		<cfset local.customerIDToUse = "olddid_#local.strEnrollment.depomemberdataID#">
		<cfset local.offerOnlinePaymentMethod = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeRegistrantPrice.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeEnrollment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.enrollmentID = arguments.event.getValue('eid',0)>
		<cfset local.gridNum = arguments.event.getValue('gnum','')>
		<cfset local.SWType = arguments.event.getTrimValue('ft','SWL')>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif local.SWType eq 'SWL'>
			<cfset local.qryEnrollment = CreateObject("component","seminarWebSWL").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>

			<cfset local.hasdeleteSWLRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.siteCode')) OR local.tmpRights.deleteSWLRegistrantAll is 1>
			<cfif NOT local.hasdeleteSWLRegistrantRights>
				<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
			</cfif>
		<cfelseif local.SWType eq 'SWOD'>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(enrollmentID=local.enrollmentID)>

			<cfset local.hasdeleteSWODRegistrantRights = (local.tmpRights.deleteSWODRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.event.getValue('mc_siteinfo.siteCode')) OR local.tmpRights.deleteSWODRegistrantAll is 1>
			<cfif NOT local.hasdeleteSWODRegistrantRights>
				<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
			</cfif>
		</cfif>

		<cfset local.isBundleOrder = val(local.qryEnrollment.bundleOrderID ?: 0) GT 0>

		<cfif local.qryEnrollment.handlesOwnPayment is 1>
			<cfstoredproc procedure="sw_getEnrollmentAmountBilled_Assn" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.enrollmentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.SWType#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" variable="local.amountBilled" scale="2">
			</cfstoredproc>
		<cfelse>
			<cfstoredproc procedure="sw_getEnrollmentAmountBilled_SW" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.enrollmentID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" variable="local.amountBilled" scale="2">
				<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" variable="local.salesTaxAmount" scale="2">
			</cfstoredproc>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_removeEnrollment.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeSWBundleOrder" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSWB = createObject("component", "seminarWebSWB")>
		<cfset local.orderID = int(val(arguments.event.getValue('oid',0)))>
		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteid')>
		
		<cfset local.qryBundleOrder = local.objSWB.getBundleOrder(orderID=local.orderID)>

		<cfif NOT local.qryBundleOrder.recordCount OR NOT local.objSWB.hasUpdateBundleRights(siteID=local.siteID, orderID=local.orderID, action="removeEnrollment", checkLockSettings=false)>
			<cflocation url="#this.link.message#&mode=direct&message=1" addtoken="false">
		</cfif>

		<cfif local.qryBundleOrder.handlesOwnPayment is 1>
			<cfstoredproc procedure="sw_getEnrollmentAmountBilled_Assn" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orderID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="SWB">
				<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" variable="local.amountBilled" scale="2">
			</cfstoredproc>
		<cfelse>
			<cfset local.qryBundleOrderTotals = local.objSWB.getBundleOrderTotals(orderID=local.orderID)>
			<cfset local.amountBilled = local.qryBundleOrderTotals.amountBilled>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_removeSWBEnrollment.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.objSWLSeminar = createObject("component","model.admin.seminarweb.seminarwebSWL")>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailMaterials", 
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>
		
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.materialsDoc = CreateObject("component","model.seminarweb.SWLiveSeminars").getMaterialsDocument(seminarID=local.seminarID, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
		<cfset local.participantID = local.participantData.participantID>
		<cfset local.scheduledTaskDetails = local.objSWLSeminar.getScheduledTaskDetails(local.participantID)>
		<cfset local.showInfoAlert = local.materialsDoc.recordcount AND LEN(local.qrySeminar.dateStart) AND 
									local.qrySeminar.dateStart GT now() AND local.scheduledTaskDetails.isWebinarMaterialEnabled>
		<cfif local.showInfoAlert>
			<cfset var timeFormatter = function(timeframe) {
				if (timeframe == "1d") {
					return "1 day";
				} else if (timeframe == "1h") {
					return "1 hour";
				} else if (Right(timeframe, 1) == "d") {
					return Replace(timeframe, "d", " days", "all");
				} else if (Right(timeframe, 1) == "h") {
					return Replace(timeframe, "h", " hours", "all");
				}
				return timeframe;
			}>
		
			<!--- Use listMap to transform the list based on the callback function --->
			<cfset local.formattedTimeframes = listMap(local.scheduledTaskDetails.webinarMaterialSelectedTimeframes, timeFormatter)>
			<cfset local.formattedTimeframes = Replace(formattedTimeframes, ",", ", ", "all")>
		</cfif>
		<cfset local.hasMassEmailSWLMaterialsRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode')>

		<cfif NOT local.hasMassEmailSWLMaterialsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>
		
		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWLiveEmails").generateMaterialsEmail(seminarID=local.seminarID, orgcode=local.qrySeminar.publisherOrgCode,enrollmentID = local.qryRegistrants.enrollmentID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doMassEmailMaterials") & "&mode=direct&pID=#local.seminarID#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmailMaterials.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doMassEmailMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
	
		<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarid=local.seminarID)>
		<cfset local.materialsDoc = CreateObject("component","model.seminarweb.SWLiveSeminars").getMaterialsDocument(seminarID=local.seminarID, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>
				
		<cfset local.hasMassEmailSWLMaterialsRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') and (local.materialsDoc.recordcount gt 0)>

		<cfif NOT local.hasMassEmailSWLMaterialsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWL").getEnrollments(seminarid=local.seminarID)>

		<cfquery name="local.qryRecipients" dbtype="query">
			select enrollmentID, email, orgCode
			from [local].qryEnrollments
			where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.event.getValue('enrollmentIDListFiltered','')#">)
			and email <> ''
		</cfquery>

		<cfif local.qryRecipients.recordCount is 0>
			<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
		</cfif>

		<cfloop query="local.qryRecipients">
			<cfset local.objSWLiveEmails.sendMaterials(seminarID=local.seminarID, enrollmentID=local.qryRecipients.enrollmentID, performedBy=session.cfcuser.memberdata.depomemberdataid, 
					outgoingType="manualMaterials", emailToUse=local.qryRecipients.email, orgcode=local.qryRecipients.orgCode, customText=arguments.event.getTrimValue('customtext',''))>
		</cfloop>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailConnectionInstructionsSWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", seminarID=arguments.event.getValue('pid',0), 
			rdateFrom=arguments.event.getValue('rDateFrom',''), rdateTo=arguments.event.getValue('rDateTo',''), completed=arguments.event.getValue('fCompleted',''),
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), cdateFrom=arguments.event.getValue('cDateFrom',''), cdateTo=arguments.event.getValue('cDateTo',''), memberID=0,
			emailTagTypeID=arguments.event.getValue('emailTagType',0))>
			
		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>
		
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWODSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.hasMassEmailSWODResendInstructionsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1 AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.siteCode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.sendSWODCertificatesAll') is 1>

		<cfif NOT local.hasMassEmailSWODResendInstructionsRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>
		<cfset local.objSWODEmails = CreateObject("component","model.seminarweb.SWODEmails")>
		<cfset local.strEmailContent = local.objSWODEmails.generateConfirmationEmail(enrollmentID=local.qryRegistrants.enrollmentID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"domassEmailconnectionInstructionsSWOD") & "&mode=direct&pID=#local.seminarID#">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmailConnectionInstructions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="domassEmailconnectionInstructionsSWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>

		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.qrySeminar = local.objSWOD.getSeminarBySeminarID(seminarid=local.seminarID)>

		<cfset local.hasMassEmailSWODResendInstructionsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1 AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.siteCode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWODRegistrantsAll') is 1>
		
		<cfif NOT local.hasMassEmailSWODResendInstructionsRights>
			<cflocation url="#this.link.message#&mode=stream&message=2" addtoken="false">
		</cfif>		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", seminarID=arguments.event.getValue('pid',0), 
			rdateFrom=arguments.event.getValue('rDateFrom',''), rdateTo=arguments.event.getValue('rDateTo',''), completed=arguments.event.getValue('fCompleted',''),
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), cdateFrom=arguments.event.getValue('cDateFrom',''), cdateTo=arguments.event.getValue('cDateTo',''), memberID=0,
			emailTagTypeID=arguments.event.getValue('emailTagType',0))>
		
		<cfset local.enrollmentIDListFiltered = ValueList(local.qryRegistrants.enrollmentID)>

		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWOD").getEnrollments(seminarid=local.seminarID,orgcode=local.qrySeminar.publisherOrgCode)>
		
		<cfquery name="local.qryEmailRecipients" dbtype="query">
			select enrollmentID, email, orgCode
			from [local].qryEnrollments
			where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.enrollmentIDListFiltered#">)
			and email <> '';
		</cfquery>
		<cfif local.qryEmailRecipients.recordCount gt 0 and local.qryEmailRecipients.recordCount is 0>
			<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
		</cfif>
		
		<cfloop query="local.qryEmailRecipients">
			<cfset local.objSWODEmails = CreateObject("component","model.seminarweb.SWODEmails")>
			<cfset local.objSWODEmails.sendConfirmation(seminarID=local.seminarId, enrollmentID=local.qryEmailRecipients.enrollmentID,
				performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="manualResendInstructions", 
				orgcode=local.qryEmailRecipients.orgCode, emailOverride=arguments.event.getTrimValue('emailToUse',''), 
				customtext=arguments.event.getTrimValue('customtext',''),isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
			
		</cfloop>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="MassEmailCertificatesSWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", seminarID=arguments.event.getValue('pid',0), 
			rdateFrom=arguments.event.getValue('rDateFrom',''), rdateTo=arguments.event.getValue('rDateTo',''), completed=arguments.event.getValue('fCompleted',''),
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), cdateFrom=arguments.event.getValue('cDateFrom',''), cdateTo=arguments.event.getValue('cDateTo',''), memberID=0,
			emailTagTypeID=arguments.event.getValue('emailTagType',0))>
			
		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>
		
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWODSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.hasMassEmailSWODCertificatesRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1 AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.siteCode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.sendSWODCertificatesAll') is 1>
	
		<cfif NOT local.hasMassEmailSWODCertificatesRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>
		
		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWCertificates").generateCertificateEmail(enrollmentID = local.qryRegistrants.enrollmentID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doMassEmailCertificatesSWOD") & "&mode=direct&pID=#local.seminarID#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmailCertificates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doMassEmailCertificatesSWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>

		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.qrySeminar = local.objSWOD.getSeminarBySeminarID(seminarid=local.seminarID)>

		<cfset local.hasMassEmailSWODCertificatesRights = (arguments.event.getValue('mc_adminToolInfo.myRights.massEmailSWODRegistrantsSignUp') is 1 AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.siteCode')) OR arguments.event.getValue('mc_adminToolInfo.myRights.sendSWODCertificatesAll') is 1>
		
		<cfif NOT local.hasMassEmailSWODCertificatesRights>
			<cflocation url="#this.link.message#&mode=stream&message=2" addtoken="false">
		</cfif>		
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWOD").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", seminarID=arguments.event.getValue('pid',0), 
			rdateFrom=arguments.event.getValue('rDateFrom',''), rdateTo=arguments.event.getValue('rDateTo',''), completed=arguments.event.getValue('fCompleted',''),
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), cdateFrom=arguments.event.getValue('cDateFrom',''), cdateTo=arguments.event.getValue('cDateTo',''), memberID=0,
			emailTagTypeID=arguments.event.getValue('emailTagType',0))>
		
		<cfset local.enrollmentIDListFiltered = ValueList(local.qryRegistrants.enrollmentID)>

		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWOD").getEnrollments(seminarid=local.seminarID,orgcode=local.qrySeminar.publisherOrgCode)>
		
		<cfquery name="local.qryRecipientsPassed" dbtype="query">
			select enrollmentID, email, orgCode, CreditCount
			from [local].qryEnrollments
			where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.enrollmentIDListFiltered#">)
			and passed = 1;
		</cfquery>

		<cfquery name="local.qryEmailRecipients" dbtype="query">
			select enrollmentID, email, orgCode, CreditCount
			from [local].qryRecipientsPassed
			where email <> '';
		</cfquery>

		<cfif local.qryRecipientsPassed.recordCount gt 0 and local.qryEmailRecipients.recordCount is 0>
			<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
		</cfif>
	
		<cfset local.emailSentCount = 0>
		<cfloop query="local.qryEmailRecipients">
			<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.qryEmailRecipients.enrollmentID)>
			<cfif local.qryEmailRecipients.creditCount eq 0 OR (
					(local.qryEmailRecipients.creditCount gt 0 and local.qrySeminar.offerCertificate is 1)
				)>
				<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=local.qryEmailRecipients.enrollmentID, 
					performedBy=session.cfcuser.memberdata.depomemberdataID, outgoingType="manualCertificate", emailToUse=local.qryEnrollmentInfo.email,customtext=arguments.event.getTrimValue('customtext',''), isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
				<cfset local.emailSentCount++>
			</cfif>
		</cfloop>

		<cfif local.emailSentCount eq 0>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
						top.$("##MCModalFooter").removeClass('d-flex');
						top.$("##MCModalFooter").addClass('d-none');
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
					</script>
					<div class="alert alert-danger p-3">No Registrants With Certificates Found.</div>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").removeClass('d-flex');
					top.$("##MCModalFooter").addClass('d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="MassEmailCertificatesSWL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", 
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>
			
		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>
		
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		<cfset local.hasMassEmailSWLCertificatesRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') and NOT local.qrySeminar.isOpen and local.qrySeminar.offerCertificate is 1>
		
		<cfif NOT local.hasMassEmailSWLCertificatesRights>
			<cflocation url="#this.link.message#&mode=stream&message=2" addtoken="false">
		</cfif>
		
		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWCertificates").generateCertificateEmail(enrollmentID = local.qryRegistrants.enrollmentID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doMassEmailCertificatesSWL") & "&mode=direct&pID=#local.seminarID#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmailCertificates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doMassEmailCertificatesSWL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>

		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.qrySeminar = local.objSWL.getSeminarBySeminarID(seminarid=local.seminarID)>

		<cfset local.hasMassEmailSWLCertificatesRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode') and NOT local.qrySeminar.isOpen and local.qrySeminar.offerCertificate is 1>
		
		<cfif NOT local.hasMassEmailSWLCertificatesRights>
			<cflocation url="#this.link.message#&mode=stream&message=2" addtoken="false">
		</cfif>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailCert", 
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

		<cfset local.enrollmentIDListFiltered = ValueList(local.qryRegistrants.enrollmentID)>

		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWL").getEnrollments(seminarid=local.seminarID)>

		<cfquery name="local.qryRecipientsPassed" dbtype="query">
			select enrollmentID, email, orgCode, CreditCount
			from [local].qryEnrollments
			where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.enrollmentIDListFiltered#">)
			and passed = 1;
		</cfquery>

		<cfquery name="local.qryEmailRecipients" dbtype="query">
			select enrollmentID, email, orgCode, CreditCount
			from [local].qryRecipientsPassed
			where email <> '';
		</cfquery>

		<cfif local.qryRecipientsPassed.recordCount gt 0 and local.qryEmailRecipients.recordCount is 0>
			<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
		</cfif>
		
		<cfset local.emailSentCount = 0>
		<cfloop query="local.qryEmailRecipients">
			<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=local.qryEmailRecipients.enrollmentID)>
			<cfset local.progress = local.objSWL.getSeminarProgress(local.qryEmailRecipients.enrollmentID)>
			<cfif local.qryEmailRecipients.creditCount eq 0 OR (
					(local.qryEmailRecipients.creditCount gt 0 and local.progress.qrySeminarProgress.hasCertificate is 1)
					OR
					(local.qryEmailRecipients.creditCount gt 0 AND local.progress.qrySeminarProgress.allPostTestCompleted gt 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted gt 0)
				)>
				<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=local.qryEmailRecipients.enrollmentID, 
					performedBy=session.cfcuser.memberdata.depomemberdataID, outgoingType="manualCertificate", emailToUse=local.qryEnrollmentInfo.email,customtext=arguments.event.getTrimValue('customtext',''), isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>
				<cfset local.emailSentCount++>
			</cfif>
		</cfloop>

		<cfif local.emailSentCount eq 0>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
						top.$("##MCModalFooter").removeClass('d-flex');
						top.$("##MCModalFooter").addClass('d-none');
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },5000);
					</script>
					<div class="alert alert-danger p-3">No Registrants With Certificates Found.</div>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").removeClass('d-flex');
					top.$("##MCModalFooter").addClass('d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailSWLReplayLink" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>
		
		<cfset local.qryRegistrants = createObject("component","seminarWebSWL").getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailReplayLink", 
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''), 
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>

		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>

		<cfset local.hasMassEmailSWLReplayLinkRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND local.qrySeminar.offerReplay EQ 1 AND local.qrySeminar.isUploadedReplay EQ 1 AND DateCompare(local.qrySeminar.replayExpirationDate,now()) NEQ -1>

		<cfif NOT local.hasMassEmailSWLReplayLinkRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.strEmailContent = CreateObject("component","model.seminarweb.SWLiveEmails").generateReplayConfirmationEmail(enrollmentID=local.qryRegistrants.enrollmentID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doMassEmailSWLReplayLink") & "&mode=direct&pID=#local.seminarID#">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massEmailReplayLink.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doMassEmailSWLReplayLink" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
	
		<cfset local.objSWLiveEmails = CreateObject("component","model.seminarweb.SWLiveEmails")>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarid=local.seminarID)>
		
		<cfset local.hasMassEmailSWLReplayLinkRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qrySeminar.publisherOrgCode EQ arguments.event.getValue('mc_siteInfo.sitecode') AND NOT local.qrySeminar.isOpen AND local.qrySeminar.offerReplay EQ 1 AND local.qrySeminar.isUploadedReplay EQ 1 AND DateCompare(local.qrySeminar.replayExpirationDate,now()) NEQ -1>

		<cfif NOT local.hasMassEmailSWLReplayLinkRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWL").getEnrollments(seminarid=local.seminarID)>

		<cfquery name="local.qryRecipients" dbtype="query">
			select enrollmentID, email, orgCode
			from [local].qryEnrollments
			where enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.event.getValue('enrollmentIDListFiltered','')#">)
			and email <> ''
		</cfquery>

		<cfif local.qryRecipients.recordCount is 0>
			<cfreturn returnAppStruct("No Email Recipients Found.","echo")>
		</cfif>

		<cfloop query="local.qryRecipients">
			<cfset local.objSWLiveEmails.sendReplayConfirmation(seminarID=local.seminarID, enrollmentID=local.qryRecipients.enrollmentID, 
				performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="manualSendReplayLink",orgcode=local.qryRecipients.orgCode, 
				emailOverride=local.qryRecipients.email, customText=arguments.event.getTrimValue('customtext',''), isRegistrationConfirmation=0)>
		</cfloop>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$("##MCModalFooter").toggleClass('d-flex d-none');
					window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
				</script>
				<div class="p-3">
					<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
						<span class="font-size-lg d-block d-40 mr-2 text-center">
							<i class="fa-solid fa-circle-check"></i>
						</span>
						<span>E-mail sent.</span>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="registerSWLRegistrantsForOnDemand" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.seminarID = int(val(arguments.event.getTrimValue('pid',0)))>

		<cfset local.objAdminSWL = createObject("component","seminarWebSWL")>
		
		<cfset local.qryRegistrants = local.objAdminSWL.getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="regForSWOD",
			seminarID=local.seminarID, rAttended=arguments.event.getValue('rAttended',''), rDateFrom=arguments.event.getValue('rDateFrom',''),
			rDateTo=arguments.event.getValue('rDateTo',''), rHideDeleted=arguments.event.getValue('rHideDeleted',''), rCredits=arguments.event.getValue('rCredits',''), memberID=0)>

		<cfif local.qryRegistrants.recordCount is 0>
			<cfreturn returnAppStruct("No Registrants Found.","echo")>
		</cfif>

		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>

		<cfset local.hasRegisterForSWODRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.isConvertedToSWOD is 1>

		<cfif NOT local.hasRegisterForSWODRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qryAssociatedSWODSeminar = local.objAdminSWL.getAssociatedSWODSeminar(seminarID=local.seminarID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doRegisterSWLRegistrantsForOnDemand") & "&pID=#local.seminarID#&mode=direct">
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_regSWLRegistrantsForSWOD.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doRegisterSWLRegistrantsForOnDemand" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWL = CreateObject("component","seminarwebSWL")>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.isFeeExempt = arguments.event.getValue('isFeeExempt',0)>

		<cfsetting requesttimeout="500">
	
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarid=local.seminarID)>
		<cfset local.hasRegisterForSWODRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.qrySeminar.isConvertedToSWOD is 1>

		<!--- no rights --->
		<cfif NOT local.hasRegisterForSWODRights>
			<cflocation url="#this.link.message#&mode=direct&message=2" addtoken="false">
		</cfif>

		<cfset local.qrySWODSeminar = local.objSWL.getAssociatedSWODSeminar(seminarID=local.seminarID)>
		<cfset local.qryEnrollments = local.objSWL.getSWLEnrollmentsForSWODRegistration(SWODSeminarID=local.qrySWODSeminar.seminarID, 
			enrollmentIDList=arguments.event.getValue('enrollmentIDListFiltered',''), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>

		<!--- no registrants found --->
		<cfif local.qryEnrollments.recordCount is 0>
			<cfsavecontent variable="local.message">
				<cfoutput>
					<div class="alert alert-danger p-2">
						There were no SWL registrants eligible for the SWOD program.<br/>
						The selected registrants may have already registered for the program.
					</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.objSWReg = CreateObject("component","SWReg")>
			<cfset local.objSWParticipants = CreateObject("component","model.seminarweb.SWParticipants")>
			<cfset local.objSWCredits = CreateObject("component","model.seminarweb.SWCredits")>
			
			<cfset local.programID = val(local.qryEnrollments.swodSeminarID)>
			<cfset local.qryProgram = QueryNew("programID,programName,programSubTitle,offerCredit,publisherOrgCode,programType,freeRateDisplay","integer,varchar,varchar,boolean,varchar,varchar,varchar")>

			<cfset local.strProgram = CreateObject("component","seminarWebSWOD").getSeminarForRegistrationByAdmin(seminarID=local.programID, catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0)>
			<cfif local.strProgram.qrySeminar.recordCount gt 0 and QueryAddRow(local.qryProgram)>
				<cfset QuerySetCell(local.qryProgram,"programID",local.strProgram.qrySeminar.seminarID)>
				<cfset QuerySetCell(local.qryProgram,"programName",local.strProgram.qrySeminar.seminarName)>
				<cfset QuerySetCell(local.qryProgram,"programSubTitle",local.strProgram.qrySeminar.seminarSubTitle)>
				<cfset QuerySetCell(local.qryProgram,"offerCredit",1)>
				<cfset QuerySetCell(local.qryProgram,"publisherOrgCode",local.strProgram.qrySeminar.publisherOrgCode)>
				<cfset QuerySetCell(local.qryProgram,"programType",'SWOD')>
				<cfset QuerySetCell(local.qryProgram,"freeRateDisplay",local.strProgram.qrySeminar.freeRateDisplay)>
			</cfif>

			<!--- register at a $0 rate --->
			<cfloop query="local.qryEnrollments">
				<cfset local.qryAssociation = local.objSWParticipants.getAssociationDetails(orgcode=local.qryEnrollments.orgCode).qryAssociation>
				
				<cfset local.billingState = "">
				<cfset local.stateIDForTax = 0>
				<cfset local.zipforTax = local.qryEnrollments.postalCode>

				<!--- SW handles registration --->
				<cfif local.qryAssociation.handlesOwnPayment is 0>
					<cfset local.billingState = local.qryEnrollments.state>
				<cfelse>
					<cfset local.stateIDForTax = val(local.qryEnrollments.stateID)>
				</cfif>

				<!--- same credit selections as user had on the live program --->
				<cfset local.qrySWLEnrollmentCredits = local.objSWCredits.getCreditsByEnrollmentID(local.qryEnrollments.enrollmentID)>
				<cfset local.qryCreditsForSWOD = local.objSWCredits.getCreditsforSeminar(seminarID=local.programID, siteCode=arguments.event.getValue('mc_siteinfo.sitecode')).qryCredit>

				<cfquery name="local.qryEligibleSelections" dbtype="query">
					SELECT authorityID, seminarCreditID, idNumber
					FROM [local].qrySWLEnrollmentCredits
					WHERE authorityID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="0#valueList(local.qryCreditsForSWOD.authorityID)#">)
				</cfquery>

				<cfset local.strCreditData = { structCreditData=structNew(), creditIDList="" }>

				<cfif local.qryEligibleSelections.recordCount and local.qryCreditsForSWOD.recordCount>
					<cfloop query="local.qryEligibleSelections">
						<cfquery name="local.qryThisCreditSelection" dbtype="query">
							SELECT seminarCreditID
							FROM [local].qryCreditsForSWOD
							WHERE authorityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryEligibleSelections.authorityID#">
						</cfquery>

						<cfset StructInsert(local.strCreditData.structCreditData, local.qryThisCreditSelection.seminarCreditID, local.qryEligibleSelections.idNumber)>
						<cfset local.strCreditData.creditIDList = listAppend(local.strCreditData.creditIDList, local.qryThisCreditSelection.seminarCreditID)>
					</cfloop>
				</cfif>
				<!--- add registrant --->
				<cfset local.strResult = local.objSWReg.addSWRegistrant(siteCode=local.qryAssociation.orgCode, programID=local.programID, programType='SWOD', rateID=0, 
					qryProgram=local.qryProgram, qryAssociation=local.qryAssociation, memberID=local.qryEnrollments.memberID, depoMemberDataID=local.qryEnrollments.depomemberdataID, 
					billingState=local.billingState, stateIDForTax=local.stateIDForTax, zipforTax=local.zipforTax, arrCustomFields=[], 
					strCreditData=local.strCreditData, transactionDate=now(), fromSWLSeminarID=local.seminarID, customText=arguments.event.getTrimValue('customtext',''),
				isImportantCustomText=arguments.event.getTrimValue('isImportantText',0))>

				<cfif local.strResult.success and local.strResult.enrollmentID and local.isFeeExempt>
					<cfset CreateObject("seminarWebSWCommon").toggleFeeExempt(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteID'), enrollmentID=local.strResult.enrollmentID, isFeeExempt=1, programType='SWOD')>
				</cfif>
			</cfloop>

			<cfsavecontent variable="local.message">
				<cfoutput>
					<div class="p-3">
						<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
							<span class="font-size-lg d-block d-40 mr-2 text-center">
								<i class="fa-solid fa-circle-check"></i>
							</span>
							#local.qryEnrollments.recordCount# registrant<cfif local.qryEnrollments.recordCount gt 1>s</cfif> have been registered for the associated OnDemand program.
							An email with program access instructions will be sent to each registrant.
						</div>
					</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
					top.$('##MCModalLabel').html('Registration Complete');
					top.setTimeout(function(){ top.MCModalUtils.hideModal(); }, 4000);
				</script>
				#local.message#
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="updateSWTLProgramDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.saveResult = CreateObject("component","seminarWebSWTL").updateSWTLProgram(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.saveResult.success>
					Updated Successfully.
				<cfelse>
					<script type="text/javascript">
						saveErrMsg = 'Unable to save the program detail';
					</script>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWTLFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWFile = CreateObject("component","seminarwebFiles")>

		<cfset local.titleID = arguments.event.getValue('pid',0)>
		<cfset local.fileID = arguments.event.getValue('fid',0)>

		<cfset local.qryFile = local.objSWFile.getFile(fileID=local.fileID)>
		<cfset local.qryFileTypes = local.objSWFile.getFileTypes()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWFile.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSWTLFilesInfo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.titleID = arguments.event.getValue('pid',0)>

		<cfset local.isProgramLocked = CreateObject("seminarWebSWCommon").isProgramLocked(titleID=local.titleID)>
		
		<cfif NOT local.isProgramLocked>
			<cfset local.arrFiles = arrayNew(1)>
			<cfloop list="#arguments.event.getValue('fileIDs','')#" index="local.fileID">
				<cfset local.tmpStr = structNew()>
				<cfset structInsert(local.tmpStr, "fileID", local.fileID)>
				<cfset structInsert(local.tmpStr, "fileTitle", arguments.event.getValue('fieldLabel_#local.fileID#',''))>
				<cfset arrayAppend(local.arrFiles, local.tmpStr)>
			</cfloop>

			<cfset createObject("component","seminarWebFiles").updateTitleFiles(titleID=titleID, arrFiles=local.arrFiles)>
			
			<cfset local.data = "Files Info Saved.">
		<cfelse>
			<cfset local.data = "The Title Program is Locked.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="replaceSWTLFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWFile = CreateObject("component","seminarwebFiles")>

		<cfset local.titleID = arguments.event.getValue('pid',0)>
		<cfset local.fileID = arguments.event.getValue('fid',0)>

		<cfset local.qryFile = local.objSWFile.getFile(fileID=local.fileID)>
		
		<cfset local.streamingExt = ''>
		<cfif ArrayLen(XMLSearch(local.qryFile.formatsAvailable, "/formats/format[@accesstype='S']")) GT 0>
			<cfset local.streamingExt = XMLSearch(local.qryFile.formatsAvailable, "/formats/format[@accesstype='S']")[1].xmlAttributes.ext>
		</cfif>

		<cfset local.downloadableExt = ''>
		<cfif ArrayLen(XMLSearch(local.qryFile.formatsAvailable, "/formats/format[@accesstype='D']")) GT 0>
			<cfset local.dFormats = XMLSearch(local.qryFile.formatsAvailable, "/formats/format[@accesstype='D']")>
			<cfset local.downloadableFileName = local.dFormats[1].xmlAttributes.ext>
			<cfif local.downloadableFileName NEQ ''>
				<!--- Extract file extension from filename --->
				<cfset local.downloadableExt = ListLast(local.downloadableFileName, ".")>
			</cfif>
		</cfif>
		
		<cfif local.streamingExt NEQ ''>
			<cfset local.extension = local.streamingExt>
		<cfelse>
			<cfset local.extension = local.downloadableExt>
		</cfif>
		<cfset local.filemode = "">
		<cfset local.allowedMimeTypes = "">
		<cfif ListFindNoCase("mp3,mp4", local.extension)>
			<cfset local.filemode =  "stream">
			<cfset local.allowedMimeTypes = "mp3,mp4">
		<cfelseif ListFindNoCase("pdf,doc,docx,ppt,pptx,xls,xlsx", local.extension)>
			<cfset local.filemode =  "download">
			<cfset local.allowedMimeTypes = "pdf,doc,docx,ppt,pptx,xls,xlsx">
		</cfif>

		<cfif len(local.filemode)>
			<cfset local.fileUploadSettings = local.objSWFile.getUploadFileSetUp(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteID'), mcproxy_sitecode=arguments.event.getValue('mc_siteinfo.sitecode'),
												titleID=local.titleID, fileID=local.fileID, fileMode=local.fileMode)>
			<cfset local.fileUploadSettingsJSON = serializeJSON(local.fileUploadSettings)>

			<cfset local.qryTitleFileInfo = local.objSWFile.getTitleFileInfo(titleID=local.titleID, fileID=local.fileID)>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_replaceSWFile.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "Invalid File">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listSWB" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.bundlesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBList&mode=stream';
			local.editProgramLink = buildCurrentLink(arguments.event,"editSWBProgram");

			local.pageHeading = arguments.event.getValue('mc_siteInfo.swbBrand');
			local.activatedDateFrom = '';
			local.activatedDateTo = '';
			local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation;
		</cfscript>
		
		<cfset local.SWBFilter = CreateObject("component","model.admin.seminarweb.seminarwebSWB").getSWBFilter()>
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWB.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWBProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = StructNew();
			local.bundleID = int(val(arguments.event.getTrimValue('pid',0)));

			local.objSWB = CreateObject("component","model.seminarweb.SWBundles");
			local.objSeminarwebSWB = CreateObject("component","model.admin.seminarweb.seminarwebSWB");
			local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon");
			local.objAdminSWCommon = createObject("component","seminarWebSWCommon");
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode');
			local.programAdded = arguments.event.getValue('programAdded',false);
			local.lastIncompleteSectionIndex = arguments.event.getValue('lastIncompleteSectionIndex',0);
			local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(local.siteCode).defaultTimeZoneID;
			local.timeZoneAbbr = local.objTZ.getTZAllFromTZID(timeZoneID=local.defaultTimeZoneID).timeZoneAbbr;
			
			if (local.bundleID eq 0)
				application.objCommon.redirect("#this.link.message#&message=1");

			local.editSWBProgram = buildCurrentLink(arguments.event,"editSWBProgram") & "&pid=#local.bundleID#";
				
			local.selectedTab = arguments.event.getTrimValue("tab","program");
			local.hasInactiveSeminarsIncluded = false;
			local.lockTab = "";
			if(event.getTrimValue("lockTab","false")) {
				local.lockTab = local.selectedTab;
			}

			local.qryBundle = local.objSWB.getBundleByBundleID(bundleID=local.bundleID, orgcode=local.siteCode);
			if (val(local.qryBundle.bundleID) is 0) {
				application.objCommon.redirect("#this.link.message#&message=1");
			} else {
				local.hasEditBundleAllRights = arguments.event.getValue('mc_adminToolInfo.myRights.editBundleAll') is 1;
				local.hasEditBundlePublishRights = arguments.event.getValue('mc_adminToolInfo.myRights.editBundlePublish') is 1;
				local.isPublisher = local.qryBundle.publisherOrgCode eq local.siteCode;

				local.hasEditRights = (local.hasEditBundleAllRights OR local.hasEditBundlePublishRights) AND local.isPublisher;
				local.isSWProgramLocked = local.qryBundle.lockSettings is 1;
				local.hasLockSWProgramRights = local.hasEditRights AND arguments.event.getValue('mc_adminToolInfo.myRights.lockSWProgram') is 1;
				local.showBillingTab = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.isPublisher;
				local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.siteCode);
				local.qryAssociation = local.strAssociation.qryAssociation;
				local.qryBundleSyndicateObj = local.objAdminSWCommon.getBundleSyndicateData(local.bundleID,local.siteCode);
				local.isFeaturedProgram = local.objSWCommon.isFeaturedProgram(catalogOrgCode=local.siteCode, programID=local.bundleID, programType="SWB");
				
				if(NOT local.isPublisher)
					local.enrollmentCount = local.objSeminarwebSWB.getEnrollmentCount(bundleID=local.bundleID, participantID=local.qryAssociation.participantID).count;
				local.listProgramsLink = buildCurrentLink(arguments.event,"listSWB");
				
				/* inactive syndicated program */
				if (NOT local.isPublisher AND NOT local.objAdminSWCommon.isActiveOptedIntoProgram(participantID=local.qryAssociation.participantID, programID=local.bundleID, programType='SWB'))
					application.objCommon.redirect("#this.link.message#&message=1");

				if (local.hasEditRights) {
					/* Details Tab */
					local.updateSWBProgramDetailsLink = buildCurrentLink(arguments.event,"updateSWBProgramDetails");
					local.strLearningObjectives = getLearningObjectives(programType="SWB",programID=local.bundleID, isReadOnly=local.isSWProgramLocked, orgcode=local.siteCode, adminHomeResource=arguments.event.getValue('mc_adminNav.adminHomeResource'));
					if (val(local.strAssociation.qryParticipantFeaturedImageSetup.swProgramFeatureImageConfigID) gt 0) {
						local.arrSWBConfigs = [ { "ftdExt":"#this.siteResourceID#_swbprogram", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
						 							"controllingReferenceType":"swProgram", "referenceID":local.bundleID, "referenceType":"swbProgram", "resourceType":"SeminarWebAdmin", 
													"resourceTypeTitle":local.qryBundle.bundleName, "onDeleteImageHandler":"", "onSaveImageHandler":"reloadSWEditProgram", 
													"header":'', "ftdImgClassList":"", "readOnly":local.isSWProgramLocked
												} ];
						local.strSWBFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=local.siteCode, arrConfigs=local.arrSWBConfigs);
					}
					
					local.strSponsors = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsorsSelector(resourceType="Bundle",
						referenceType="swbprogram", referenceID=local.bundleID, selectorID='swbProgramSponsors', readOnly=local.isSWProgramLocked);
					local.getSponsor = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsors(mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'),referenceType="swbProgram", referenceID=local.bundleID);

					
					/* Included Seminars Tab */
					local.editSWLProgram = buildCurrentLink(arguments.event,"editSWLProgram");
					local.editSWODProgram = buildCurrentLink(arguments.event,"editSWODProgram");
				}

				/* Rates Tab */
				local.hasManageSWBRatesRights = local.hasEditBundleAllRights OR local.hasEditBundlePublishRights;
				if (local.hasManageSWBRatesRights) {
					local.includedItemsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&mode=stream&meth=getSWBIncludedItems&bundleID=#local.bundleID#";
					local.qryItems = createObject("component","seminarWebSWB").getBundledItems(bundleID=local.bundleID);
					for (var i = 1; i <= local.qryItems.recordCount; i++) {
						if (local.qryItems.contentPublished[i] == 0) {
							local.hasInactiveSeminarsIncluded = true;
							break;
						}
					}
					local.hasIncludedSeminarsCount = local.qryItems.recordCount;
					if (NOT local.isPublisher)
						local.qryOptInBundleRateSettings = local.objAdminSWCommon.getSWProgramRateSettings(participantID=local.qryAssociation.participantID, programID=local.bundleID, programType='SWB');
					
					local.hasSWBRateChangeRights = local.isPublisher OR local.qryBundle.allowOptInRateChange IS 1;
					if (local.hasSWBRateChangeRights) {
						local.editSWBRateLink = buildCurrentLink(arguments.event,"editSWBRate") & "&pID=#local.bundleID#&mode=direct";
						local.manageCopyRatesLink = buildCurrentLink(arguments.event,"manageCopyRates") & "&mode=direct";
					}

					local.permissionsGridAddLink = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
					local.ratesListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWProgramRates&programID=#local.bundleID#&ft=SWB&mode=stream";
					if(local.qryBundle.handlesOwnPayment) {
						if (val(local.qryBundle.revenueGLAccountID)) {
							local.tmpStrAccount = CreateObject("component", "model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryBundle.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
							local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
						} else {
							local.GLAccountPath = "";
						}

						local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountIDCatalog",
							"idFldValue": val(local.qryBundle.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses accounting settings designated GL Account.)",
							"clearBtnTxt": "Remove Override" };
						local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
					}
				}

				local.hasManageOptInRights = local.qryBundle.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and local.isPublisher;
				local.showSWBOptInTab = local.hasManageOptInRights AND val(local.qryBundle.allowSyndication) is 1;
				
				local.canOptOutOfSyndicateSite = local.qryBundle.handlesOwnPayment is 0 and arguments.event.getValue('mc_adminToolInfo.myRights.manageSWOptIns') and !local.isPublisher;
				if (local.showSWBOptInTab) {
					/* Opt-Ins Tab */
					local.qryNationalPrograms = CreateObject("component","model.seminarweb.SWCredits").getNationalPrograms();
				}

				local.hasManageSWBRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.manageSWBRegistrantsSignUp',0) is 1 OR arguments.event.getValue('mc_adminToolInfo.myRights.manageSWBRegistrantsAll',0) is 1);
				if (local.hasManageSWBRegistrantsRights) {
					local.SWBRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBRegistrants&mode=stream&pID=#local.bundleID#";
					local.addSWRegLink = buildCurrentLink(arguments.event,"addSWReg") & "&pID=#local.bundleID#&ft=SWB&mode=direct";
					local.removeSWBOrderLink =  buildCurrentLink(arguments.event,"removeSWBundleOrder") & "&mode=direct";
					local.changeRegistrantPriceLink =  buildCurrentLink(arguments.event,"changeRegistrantPrice") & "&mode=direct";
				}

				local.hasAddSWBRegistrantsRights = (arguments.event.getValue('mc_adminToolInfo.myRights.addBundleRegistrantSignUp',0) is 1);				

				if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
					/* Billing Tab */
					local.swBillingLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBillingLogs&pID=#local.bundleID#&ft=SWB&mode=stream";
					local.qryMCBilling = CreateObject("component","seminarWebSWCommon").getMCBillingDetails(orgcode=local.siteCode);
				}
			}

			local.showSyndicationWarning = false;
			if (local.showSWBOptInTab AND local.hasEditRights) {
				var thesiteCode = local.qryBundle.publisherOrgCode;
				local.qryOptInParticipants = local.objSeminarwebSWB.getParticipantsOptedIntoBundle(bundleID=local.bundleID).filter(function(row) { return arguments.row.orgcode neq thesiteCode; });
				if (NOT local.qryOptInParticipants.recordcount)
					local.showSyndicationWarning = true;
			}

			// Initialize the array to hold issues
			local.arrSeminarSetupIssues = [];
			local.arrSeminarSetupSuccess = [];
			local.validDates = false;
			local.validRates = false;
			local.sellDatesInFutureIssue = "The bundle has future sell dates in the Catalog Details section.";
			local.notSoldInCatalogIssue = "The bundle is marked to not be sold in the catalog in the Catalog Details section.";
			local.sellingSuccessStatement = 'The bundle is marked to sell in the Catalog Details section, rates are defined, and the sale dates are not expired.';
			local.expiredDatesIssue = 'The bundle has expired sale dates in the Catalog Details section.';
			local.ratesIssue = 'Rates have not been defined in the Catalog Details section.';
			local.publishedIssues = 'The ''Bundle Status'' is marked as ''Inactive'' in the Basics section.';
			local.publishedSuccessStatement = 'The ''Bundle Status'' is marked ''Active'' in the Basics section.';
			local.qryRates = local.objSeminarwebSWB.getBundleRatesByBundleID(participantID=local.qryAssociation.participantID, bundleID=local.bundleID);
			// Check if the seminar is marked Inactive
			if (local.qryBundle.status eq 'I') 
				arrayAppend(local.arrSeminarSetupIssues, local.publishedIssues);
			else
				arrayAppend(local.arrSeminarSetupSuccess, local.publishedSuccessStatement);
			
			//If sell is ON
			if ((local.isPublisher AND LEN(local.qryBundle.dateCatalogStart)) OR 
				(!local.isPublisher AND local.qryBundleSyndicateObj.sellCatalog)) {
				//Check for Dates and Rates
				if(LEN(local.qryBundle.dateCatalogEnd) AND local.qryBundle.dateCatalogEnd LT now())  
					arrayAppend(local.arrSeminarSetupIssues, local.expiredDatesIssue);
				else if(LEN(local.qryBundle.dateCatalogStart) AND local.qryBundle.dateCatalogStart GT now())
					arrayAppend(local.arrSeminarSetupIssues, local.sellDatesInFutureIssue);
				else local.validDates = true;
				if(local.qryRates.recordCount EQ 1 AND !LEN(local.qryRates.rateID)) 
					arrayAppend(local.arrSeminarSetupIssues, local.ratesIssue);
				else local.validRates = true;
			} else arrayAppend(local.arrSeminarSetupIssues, local.notSoldInCatalogIssue);
			if(local.validDates AND local.validRates)
				arrayAppend(local.arrSeminarSetupSuccess, local.sellingSuccessStatement);

			appendBreadCrumbs(arguments.event,{ link='', text=left(htmleditformat(local.qryBundle.bundleName),50) });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWB_program.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSWBRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSWB = CreateObject("seminarWebSWB");
			local.objSWCommon = CreateObject("seminarWebSWCommon");
			local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.bundleID = arguments.event.getValue('pid',0);
			local.assocHandlesOwnPayment = local.objSWCommon.doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.qryRate = local.objSWB.getBundleRateByRateID(participantID=local.participantID, rateID=arguments.event.getValue('rateID',0));
			local.qryGetRateGroupings = local.objSWB.getBundleRateGroupsbyBundleID(participantID=local.participantID, bundleID=local.bundleID);
			
			arguments.event.setValue('rateid',val(local.qryRate.rateid));
			arguments.event.setValue('rateGroupingID',val(local.qryRate.rateGroupingID));
			arguments.event.setValue('rateName',local.qryRate.rateName);
			arguments.event.setValue('rate',local.qryRate.rate);
			arguments.event.setValue('isHidden',local.qryRate.isHidden);
			arguments.event.setValue('revenueGLAccountID',val(local.qryRate.revenueGLAccountID));

			if (local.assocHandlesOwnPayment) {
				if (val(local.qryRate.revenueGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryRate.revenueGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.GLAccountPath = "";
				}

				local.strRevenueGLAcctWidgetData = { "label": "Revenue GL Override", "btnTxt": "Choose GL Account", "glatid": 3, "widgetMode": "GLSelector", "idFldName": "revenueGLAccountID",
					"idFldValue": val(local.qryRate.revenueGLAccountID), "pathFldValue": local.GLAccountPath, "pathNoneTxt": "(No account selected; uses Bundle's designated GL Account.)",
					"clearBtnTxt": "Remove Override" };
				local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
			}

			local.formlink = buildCurrentLink(arguments.event,"saveSWBRate") & "&bundleID=#local.bundleID#&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWBRate.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSWBRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSWCommon = CreateObject("seminarWebSWCommon");
			local.objSWB = CreateObject("seminarWebSWB");
			local.rateID = arguments.event.getValue('rateID',0);

			local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.isBundleProgramLocked = local.objSWCommon.isBundleProgramLocked(bundleID=arguments.event.getValue('bundleID',0));
			local.hasSWRateChangeRights = local.objSWCommon.hasSWProgramRateChangeRights(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), programID=arguments.event.getValue('bundleID',0), programType='SWB');
			if (local.isBundleProgramLocked OR NOT local.hasSWRateChangeRights)
				application.objCommon.redirect("#this.link.message#&message=3");

			local.newRateGrouping = "";
			if (arguments.event.getValue('rateGroupingID','') EQ "new") {
				local.newRateGrouping = arguments.event.getTrimValue('newRateGroupingName','');
				arguments.event.setValue('rateGroupingID',0)
			}

			local.strRate = local.objSWB.saveBundleRate(participantID=local.participantID,
								rateID=arguments.event.getValue('rateID',0), 
								bundleID=arguments.event.getValue('bundleID',0), 
								rateGroupingID=arguments.event.getValue('rateGroupingID',0), 
								rateName=arguments.event.getTrimValue('rateName',''),
								rate=replace(arguments.event.getValue('rate',0),'$','','ALL'),
								isHidden=arguments.event.getValue('isHidden',0),
								revenueGLAccountID=arguments.event.getValue('revenueGLAccountID',0),
								newRateGrouping=local.newRateGrouping
							);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadSWRatesGrid();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listSWAuthors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.authorsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWAuthorsList&mode=stream";
			local.editAuthorLink = buildCurrentLink(arguments.event,"editAuthor") & "&swtype=Authors&mode=direct";
			local.authorsExportLink = buildCurrentLink(arguments.event,"exportSWAuthors") & "&mode=stream";
			local.qryAuthorTypes = CreateObject("component","seminarWebAuthors").getAuthorTypes();
			local.showAllAssociations = 0;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWAuthors.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWAuthors" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.firstName = arguments.event.getValue('fFirstName','');
			local.lastName = arguments.event.getValue('fLastName','');
			local.firm = arguments.event.getTrimValue('fFirm','');
			local.authorType = arguments.event.getValue('fAuthorType',0);
			local.showAllAssociations = arguments.event.getValue('showAllAssociations',0);
			local.participant = arguments.event.getValue('fParticipant',0);
			
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "Authors.csv";

			CreateObject("component","model.admin.seminarWeb.seminarWebAuthors").getAuthorsFromFilters(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'),
				mode="export", firstName=local.firstName, lastName=local.lastName, firm=local.firm, authorType=local.authorType,
				folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, showAllAssociations=local.showAllAssociations,
				participant=local.participant);
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWAuthors('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWAuthorPrograms" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.authorID = arguments.event.getValue('aid',0);
			local.programType = arguments.event.getValue('programType','');
			local.programTypeLabel = (local.programType EQ "SWL") ? "Webinars" : "OnDemands";

			local.qryAuthor = createObject("component","model.seminarWeb.SWAuthors").getAuthor(authorID=local.authorID);
			local.authorNameForExport = ReReplaceNoCase(local.qryAuthor.firstName & local.qryAuthor.lastName,"[^A-Z0-9]","","ALL");

			local.strFolder = application.objDocDownload.createHoldingFolder();
			local.reportFileName = "#local.authorNameForExport#_Linked#local.programTypeLabel#.csv";

			local.qryAuthorPrograms = CreateObject("component","seminarwebAuthors").getAuthorSeminarsForExport(authorID=local.authorID, programType=local.programType,
				folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);

			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">self.location.href = '/tsdd/#local.stDownloadURL#';</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getSWActivityStatements" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.generateDownloadLink = buildCurrentLink(arguments.event,"generateMonthlyBilling")>
		<cfset local.viewReportLink = buildCurrentLink(arguments.event,"viewMonthlyBillingDetails") & "&mode=stream">

		<cfquery name="local.qryReportMonths" datasource="#application.dsn.platformStatsMC.dsn#">
			SELECT runID, activityReportMonth 
			FROM dbo.sw_MonthlyBillingRun
			ORDER BY activityReportMonth DESC
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWActivityStatements.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateMonthlyBilling" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
		<cfset local.participantID = local.participantData.participantID>

		<cfset local.strFile = CreateObject("component","model.admin.seminarWeb.seminarWebDesktopAdmin").runMonthlyBilling(runID=arguments.event.getValue('runID',0), participantID=local.participantID, format=arguments.event.getValue('format','pdf'))>

		<cfif len(local.strFile.sourceFilePath)>
			<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.strFile.sourceFilePath, displayName=local.strFile.fileName, forceDownload=1, deleteSourceFile=1)>
		</cfif>

		<cfreturn returnAppStruct("Done","echo")>
	</cffunction>

	<cffunction name="viewMonthlyBillingDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.runID = arguments.event.getValue('runID',0)>
		<cfset local.billingPeriod = arguments.event.getValue('billingPeriod','')>
		<cfset local.df = createObject("java", "java.text.DecimalFormat")>
		<cfset local.linkEditMember = "/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit">

		<cfset local.participantData = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>
		<cfset local.participantID = local.participantData.participantID>

		<cfstoredproc procedure="sw_getMonthlyBillingDataByParticipant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.runID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
			<cfprocresult name="local.qryOrgSummary" resultset="1">
			<cfprocresult name="local.qryOrgProgramSummary" resultset="2">
			<cfprocresult name="local.qryOrgProgramDetails" resultset="3">
			<cfprocresult name="local.qryOrgRegistrantSummary" resultset="4">
		</cfstoredproc>

		<cfset local.qryOrgProgramSummary_SWL = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 1; })>
		<cfset local.qryOrgProgramSummary_SWOD = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 2; })>
		<cfset local.qryOrgProgramSummary_SWTL = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 3; })>
		<cfset local.qryOrgProgramSummary_SWM = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 4; })>
		<cfset local.qryOrgProgramDetails_M = local.qryOrgProgramDetails.filter(function(row) { return arguments.row.sectionCode eq 'MONSUP'; })>
		<cfset local.qryOrgProgramDetails_P = local.qryOrgProgramDetails.filter(function(row) { return arguments.row.sectionCode neq 'MONSUP'; })>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWActivityStatements_view.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="uploadSWLReplayVideo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>

		<cfset local.hasEditSWLProgramAllRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWLProgramAll') is 1>
		<cfset local.hasEditSWLProgramPublishRights = arguments.event.getValue('mc_adminToolInfo.myRights.editSWLProgramPublish') is 1>
		<cfset local.hasEditRights = (local.hasEditSWLProgramAllRights OR local.hasEditSWLProgramPublishRights) and arguments.event.getValue('publisherOrgCode','') eq arguments.event.getValue('mc_siteInfo.sitecode')>

		<cfif local.hasEditRights>
			<cfset local.fileUploadSettings = CreateObject("component","seminarWebSWL").getUploadSWLReplayFileSettings(seminarID=arguments.event.getValue('pid',0))>
			<cfset local.fileUploadSettingsJSON = serializeJSON(local.fileUploadSettings)>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_SWL_program_replayVideoUpload.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWRegistrantsFormResponses" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.objSWCommon = CreateObject("component","seminarWebSWCommon");
			local.gridmode = arguments.event.getValue('gridmode','');
			local.siteCode = arguments.event.getValue('mc_siteInfo.siteCode');
			
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.siteCode);
			local.seminarID = arguments.event.getTrimValue('pid');
			local.formID = arguments.event.getTrimValue('formID');
			local.swType = arguments.event.getTrimValue('swType');
			local.exportType = arguments.event.getTrimValue('exportType');
			if(local.exportType eq "pdf")
				local.reportFileName = "Responses.pdf";
			else
				local.reportFileName = "Responses.csv";
				
			if (local.swType EQ "SWL") {
				local.rAttended = arguments.event.getValue('rAttended','');
				local.rDateFrom = arguments.event.getValue('rDateFrom','');
				local.rDateTo = arguments.event.getValue('rDateTo','');
				local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
				local.rCredits = arguments.event.getValue('frrCredits','');

				local.qryEnrollments = CreateObject("component","seminarWebSWL").getRegistrants(sitecode=local.siteCode, mode="formResponses", seminarID=local.seminarID,
					rCredits=local.rCredits, rAttended=local.rAttended, rDateFrom=local.rDateFrom, rDateTo=local.rDateTo, rHideDeleted=local.rHideDeleted, memberID=0,
					folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, fbExportType=local.exportType, fbFormID=local.formID);

			} else if (local.swType EQ "SWOD") {
				local.rdateFrom = arguments.event.getValue('rDateFrom','');
				local.rdateTo = arguments.event.getValue('rDateTo','');
				local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
				local.cdateFrom = arguments.event.getValue('cDateFrom','');
				local.cdateTo = arguments.event.getValue('cDateTo','');
				local.completed = arguments.event.getTrimValue('fCompleted','');
				
				local.qryEnrollments = CreateObject("component","seminarWebSWOD").getRegistrants(sitecode=local.siteCode, mode="formResponses", seminarID=local.seminarID,
					rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, completed=local.completed, cdateFrom=local.cdateFrom, cdateTo=local.cdateTo, 
					memberID=0, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, fbExportType=local.exportType, fbFormID=local.formID);
			}
		</cfscript>

		<cfif local.exportType eq "pdf">
			<cfset local.formResponsesXML = xmlParse(local.qryEnrollments.xmlResult)>
			<cfset local.arrForm = XmlSearch(local.formResponsesXML,"/data/form")>

			<cfif arrayLen(local.arrForm)>
				<cfset local.formTitle = local.arrForm[1].XmlAttributes.formtitle>
				<cfset local.formIntro = local.arrForm[1].XmlAttributes.formintro>
				<cfset local.formSectionsArr = local.arrForm[1].XmlChildren>
				<cfset local.summaryResponseCount = arrayLen(XMLSearch(local.formResponsesXML,"/data/responsesummary/qrcs/qrc"))>
				<cfset local.assetsURL = replaceNoCase(application.paths.internalPlatform.url,"*SITECODE*",local.siteCode) & "assets/common/images/formbuilder/">

				<cfsavecontent variable="local.summaryData">
					<cfoutput>
						<html>
						<head>
							<style type="text/css">
								<cfinclude template="/assets/admin/css/pdfstylesheet.css">
							</style>
						</head>
						<body>
							<cfinclude template="dsp_registrantsFormReponseSummaryPDF.cfm">
						</body>
						</html>
					</cfoutput>
				</cfsavecontent>

				<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
					<cfdocumentsection>
						#local.summaryData#
					</cfdocumentsection>
					</cfoutput>
				</cfdocument>
			</cfif>
		</cfif>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">downloadSWFormResponses('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelSWProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.swType = arguments.event.getValue('ft','')>
		<cfset local.seminarID = arguments.event.getValue('pid',0)>
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.seminarID)>
		
		<cfset local.hasRights = application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.qrySeminar.publisherOrgCode eq arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfif local.hasRights>
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_cancelSWProgram.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copySWProgramPrompt" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.programType = arguments.event.getValue('ft','')>
		<cfset local.hasCopyProgramRights = hasCopyProgramRights(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), programID=local.programID, programType=local.programType, 
												SWRights=arguments.event.getValue('mc_adminToolInfo.myRights'))>
		
		<cfif local.hasCopyProgramRights>
			<cfset local.arrIncProgramTabs = getCopyProgramTabs(programType=local.programType)>
			<cfset local.formLink = buildCurrentLink(arguments.event,"doCopySWProgram") & "&pid=#local.programID#&ft=#local.programType#&mode=stream">

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_copySWProgram.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="doCopySWProgram" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.programType = arguments.event.getValue('ft','')>
		<cfset local.hasCopyProgramRights = hasCopyProgramRights(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), programID=local.programID, programType=local.programType, 
												SWRights=arguments.event.getValue('mc_adminToolInfo.myRights'))>

		<cfif local.hasCopyProgramRights>
			<cfset local.arrIncProgramTabs = getCopyProgramTabs(programType=local.programType)>
			<cfset local.strArgs = { "programID":local.programID, "siteID":arguments.event.getValue('mc_siteinfo.siteid') }>
			<cfloop array="#local.arrIncProgramTabs#" index="local.thisProgramTab">
				<cfset local.strArgs['#local.thisProgramTab.id#'] = val(arguments.event.getValue('#local.thisProgramTab.id#',0))>
			</cfloop>
			
			<cfswitch expression="#local.programType#">
				<cfcase value="SWL">
					<cfset local.newProgramID = createObject("component","seminarWebSWL").copySWLProgram(argumentCollection=local.strArgs)>
				</cfcase>
				<cfcase value="SWOD">
					<cfset local.newProgramID = createObject("component","seminarWebSWOD").copySWODProgram(argumentCollection=local.strArgs)>
				</cfcase>
				<cfcase value="SWB">
					<cfset local.newProgramID = createObject("component","seminarWebSWB").copySWBProgram(argumentCollection=local.strArgs)>
				</cfcase>
			</cfswitch>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script type="text/javascript">
						top.editSWProgram(#local.newProgramID#,true);
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="hasCopyProgramRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="SWRights" type="struct" required="true">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryProgram">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @programID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
			
			<cfswitch expression="#arguments.programType#">
				<cfcase value="SWL,SWOD" delimiters=",">
					select p.orgcode as publisherOrgCode
					from dbo.tblSeminars as s
					inner join dbo.tblParticipants as p on p.participantID = s.participantID
					where s.seminarID = @programID
					and s.isDeleted = 0;
				</cfcase>
				<cfcase value="SWB">
					select p.orgcode as publisherOrgCode
					from dbo.tblBundles as b
					inner join dbo.tblParticipants as p on p.participantID = b.participantID
					where b.bundleID = @programID;
				</cfcase>
			</cfswitch>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.hasCopyProgramRights = arguments.SWRights.copyProgram is 1 and local.qryProgram.publisherOrgCode eq arguments.sitecode>

		<cfreturn local.hasCopyProgramRights>
	</cffunction>

	<cffunction name="convertSWLToSWODPrompt" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.hasConvertRights = arguments.event.getValue('mc_adminToolInfo.myRights.convertProgramToSWOD') is 1>

		<cfif local.hasConvertRights>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT s.seminarName
				FROM dbo.tblSeminars AS s
				INNER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
				WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
				AND s.isDeleted = 0;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryCreditApprovals">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT COUNT(*) AS approvalCount
				FROM dbo.tblSeminarsAndCredit
				WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
				AND LTRIM(RTRIM(ISNULL(courseApproval, ''))) <> '';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminarSettings">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT 1 FROM dbo.tblSeminarsAndForms as saf
				WHERE saf.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
				AND loadPoint IN ('preTest', 'postTest', 'evaluation')
				UNION 
				SELECT 1 From tblSeminars
				WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
				AND offerCertificate = 1
				UNION 
				SELECT 1 from membercentral.dbo.cf_fieldsGrouping
				WHERE fieldDetailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
				UNION 
				SELECT 1 from membercentral.dbo.cf_fields
				where detailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.seminarName = local.qrySeminar.seminarName>
			<cfset local.hasCreditApprovals = local.qryCreditApprovals.approvalCount gt 0>
			<cfset local.hasSeminarSettings = local.qrySeminarSettings.recordcount gt 0>
			<cfset local.formLink = buildCurrentLink(arguments.event,"doConvertSWLToSWOD") & "&pid=#local.programID#&mode=stream">

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_convertSWLToSWOD.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doConvertSWLToSWOD" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.hasConvertRights = arguments.event.getValue('mc_adminToolInfo.myRights.convertProgramToSWOD') is 1>

		<cfif local.hasConvertRights>
			<cfset local.incWebinarSettings = val(arguments.event.getValue('incWebinarSettings',0))>
			<cfset local.incCreditApprovals = val(arguments.event.getValue('incCreditApprovals',0))>

			<cfset local.convertResult = createObject("component","seminarWebSWL").convertSWLProgramToSWOD(
				mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				seminarID=local.programID,
				incWebinarSettings=local.incWebinarSettings,
				incCreditApprovals=local.incCreditApprovals
			)>

			<cfif local.convertResult.success>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script type="text/javascript">
							top.$('##program-basics.program-section .card-footer ##convertToSWODHolder').html('<b>Seminar successfully converted to OnDemand.</b>');
							top.$('##registerSWLRegForSWODTool').removeClass('d-none');
							top.MCModalUtils.hideModal();
							top.window.open(top.link_editswodprogram + '&pid=' + #local.convertResult.newSeminarID#);
						</script>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script type="text/javascript">
							top.alert('We were unable to convert this seminar to OnDemand. Try again.');
							top.MCModalUtils.hideModal();
						</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getCopyProgramTabs" access="private" output="false" returntype="array">
		<cfargument name="programType" type="string" required="true">

		<cfset var arrIncProgramTabs = []>
		<cfswitch expression="#arguments.programType#">
			<cfcase value="SWL">
				<cfset arrIncProgramTabs = [
					{ "label":"Include Basics", "id":"incBasics" },
					{ "label":"Include Speakers", "id":"incSpeakers" },
					{ "label":"Include Settings", "id":"incSettings" },
					{ "label":"Include Materials", "id":"incMaterials" },
					{ "label":"Include Catalog details", "id":"incCatalog" },
					{ "label":"Include Syndication", "id":"incSyndication" },
					{ "label":"Include Credit", "id":"incCredit" }
				]>
			</cfcase>
			<cfcase value="SWOD">
				<cfset arrIncProgramTabs = [
					{ "label":"Include Basics", "id":"incBasics" },
					{ "label":"Include Speakers", "id":"incSpeakers" },
					{ "label":"Include Settings", "id":"incSettings" },
					{ "label":"Include Titles", "id":"incTitles" },
					{ "label":"Include Catalog details", "id":"incCatalog" },
					{ "label":"Include Syndication", "id":"incSyndication" },
					{ "label":"Include Credit", "id":"incCredit" }
				]>
			</cfcase>
			<cfcase value="SWB">
				<cfset arrIncProgramTabs = [
					{ "label":"Include details", "id":"incDetails" },
					{ "label":"Include rates", "id":"incRates" },
					{ "label":"Include seminars", "id":"incSeminars" }
				]>
			</cfcase>
		</cfswitch>

		<cfreturn arrIncProgramTabs>
	</cffunction>

	<cffunction name="deactivateSWODPrograms" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset local = structNew()>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.listProgramsLink = buildCurrentLink(arguments.event,"listSWOD")>
		
		<cfset local.getSelectedActiveSWODProgramsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSelectedSWODProgramsForDeactivation&mode=stream'>

		<cfset local.participantID = createObject("component","seminarWebSWCommon").getParticipantIDFromSiteCode(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfstoredproc procedure="swod_getProgramsForDeactivation" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
			<cfprocresult name="local.qryActiveSWODPrograms" resultset="1">
			<cfprocresult name="local.qryExpiredSWODPrograms" resultset="2">
		</cfstoredproc>

		<cfset local.expiredSWODProgramIDs = valueList(local.qryExpiredSWODPrograms.seminarID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_deactivateSWODPrograms.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getLearningObjectives" access="public" output="false" returntype="struct">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="isReadOnly" type="numeric" required="false" default="0">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="adminHomeResource" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfset local.programObjectivesListLink = '#arguments.adminHomeResource#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getProgramObjectivesList&programType=#arguments.programType#&programID=#arguments.programID#&isReadOnly=#arguments.isReadOnly#&mode=stream'>
		
		<cfsavecontent variable="local.data.html">
			<cfinclude template="frm_SW_program_objectives.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSWAuditLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.swAuditLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWAuditLogs&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWAuditLogs.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>You do not have rights to perform this operation.</b></cfcase>
							<cfcase value="3"><b>The Program is locked.</b></cfcase>
							<cfdefaultcase><b>#arguments.event.getValue('message')#</b></cfdefaultcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="uploadSWLMaterialDocument" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "">
		
		<cftry>			
			<cfif len(arguments.event.getTrimValue('file','')) AND arguments.event.getValue('pID',0) GT 0>
				<cfset CreateObject("component","seminarWebSWL").uploadSWLMaterialDocument(
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					orgID=arguments.event.getValue('mc_siteinfo.orgID'),
					seminarID=arguments.event.getValue('pID'),
					parentSiteResourceID=this.siteResourceID
				)>
			</cfif>
		<cfcatch type="Any">			
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="removeAllSWLDocsConfirmation" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.removeAllDocumentsLink = buildCurrentLink(arguments.event,"removeAllSWLDocuments") & '&mode=stream&pid=#arguments.event.getValue('pid',0)#'>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<form name="frmRemoveAllDocuments" method="post" action="#local.removeAllDocumentsLink#" onsubmit="$('##btnRemoveAll').attr('disabled',true);return true;">
					<div class="alert alert-warning">
						<b>Confirmation Needed</b><br/>Are you sure you want to remove all the documents uploaded?<br/><br/>
						<button type="submit" id="btnRemoveAll" class="btn btn-sm btn-outline-danger"><i class="fa-light fa-right"></i> Continue</button>
					</div>
				</form>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeAllSWLDocuments" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.data.success = false>
		<cfset local.pid = arguments.event.getValue('pid',0)>
		<cfset local.objSWLAdmin = CreateObject("component","seminarwebSWL")>
		<cfif local.pid gt 0>
			<cfset local.data = local.objSWLAdmin.removeAllSWLDocuments(event = arguments.event)>			
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.data.success>
					<script type="text/javascript">
						top.SWLMaterialDocsTable.draw();
						top.displaySavedResponseForSWLMaterials();
						top.MCModalUtils.hideModal();
					</script>
				<cfelse>
					<div class="alert alert-danger">The documents could not be deleted. Contact MemberCentral for assistance.</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="deleteSWProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.seminarId = int(val(arguments.event.getTrimValue('pid',0)))>
		<cfset local.SWType = arguments.event.getTrimValue('ft','SWL')>
		<cfset local.enrolledCount = int(val(arguments.event.getTrimValue('enrolledCount',0)))>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif local.SWType eq 'SWL'>
			<cfset local.objSWLiveSeminars = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.qrySeminar =  local.objSWLiveSeminars.getSeminarBySeminarID(seminarID=local.seminarID)>
			<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(arguments.event.getValue('mc_siteInfo.sitecode')).qryAssociation>	
			<cfset local.parsedTime = local.objSWLiveSeminars.parseTimesFromWDDX(local.qrySeminar.wddxTimeZones,local.qryAssociation.wddxTimeZones,local.qrySeminar.dateStart,local.qrySeminar.dateEnd)>
			<cfset local.formattedDateStart = left(dayOfWeekAsString(dayOfWeek(local.parsedTime.startDate)), 3) & " " & dateFormat(local.parsedTime.startDate, "M/d/yy")>
			<cfset local.formattedTimeStart = timeFormat(local.parsedTime.startDate, "h:mm") & "-" & timeFormat(local.parsedTime.endDate, "h:mm tt")>
			<cfset local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).defaultTimeZoneID>
			<cfset local.timeZoneAbbr =  CreateObject("component","model.system.platform.tsTimeZone").getTZAllFromTZID(timeZoneID=local.defaultTimeZoneID).timeZoneAbbr>
		<cfelseif local.SWType eq 'SWOD'>
			<cfset local.qrySeminar =  CreateObject("component","model.seminarweb.SWODSeminars").getSeminarBySeminarID(local.seminarID)>
		</cfif>
		<cfif local.tmpRights.deleteProgram>
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_deleteSWProgram.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "You do not have rights to this section.">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>