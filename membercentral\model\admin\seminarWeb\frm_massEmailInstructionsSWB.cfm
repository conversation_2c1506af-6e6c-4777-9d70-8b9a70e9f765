<cfsavecontent variable="local.emailInstrJS">
<cfoutput>
	<script language="javascript">
		function writePreview() {
			var hidFrame = frames['previewFrame'].document;
			hidFrame.open();
			hidFrame.write("#JSStringFormat(local.strEmailContent.templateDisp)#");
			hidFrame.close();
			$('##previewFrame').removeClass('d-none');
		}
		function writeCustom(txt) {
			var hidFrame = frames['previewFrame'].document;
			var isImportantText = $('##isImportantText').is(':checked') || 0;
			var ct = hidFrame.getElementById(isImportantText ? 'customtextimportant' : 'customtext');
			if (ct) ct.innerHTML = txt + (txt.length ? '<br/><br/>' : '');
		}
		function toggleImportantText(){
			var hidFrame = frames['previewFrame'].document;
			$(hidFrame).find('##customtextimportant, ##customtext ').html('');
			writeCustom($('##customtext').val());
		}
		function showLoadingBtn() {
			var massEmailInstructionsLink = '#local.formLink#' + '&'+ top.$('##frmRegistrantFilter').serialize();
			$("##frmInstructions").attr("action",massEmailInstructionsLink);
			$('##divMassEmailInstrcs').addClass("d-none");
			$('##divMassEmailInstrcsLoading').html(mca_getLoadingHTML());			
			top.$('##btnMCModalSave').html('Sending...<i class="fa-solid fa-spinner fa-spin"></i>').prop('disabled',true);
			return true;
		}
		$(function() { writePreview(); });
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.emailInstrJS)#">

<cfoutput>
<div id="divMassEmailInstrcs">
	<form method="POST" name="frmInstructions" id="frmInstructions" action="#local.formLink#" class="px-3 mt-2" onsubmit="return showLoadingBtn();">
		<input type="hidden" name="enrollmentIDListFiltered" value="#valueList(local.qryRegistrants.enrollmentID)#">
		<div class="form-group">
			<label for="customtext" class="col-form-label-sm">Custom Text: (will appear below opening paragraph)</label>
			<textarea name="customtext" id="customtext" class="form-control form-control-sm" rows="4" onkeyup="writeCustom(this.value);"></textarea>
		</div>
		<div class="form-group d-flex align-items-center mt-2">
			<button name="btnSend" id="btnSend" type="submit" class="btn btn-sm btn-primary d-none">Send</button>
			<div class="custom-control custom-switch ml-auto">
				<input type="checkbox" name="isImportantText" id="isImportantText" value="1" class="custom-control-input" onchange="toggleImportantText();">
				<label class="custom-control-label" for="isImportantText">Important Text</label>
			</div>
		</div>
	</form>
	<div class="form-group mt-3">
		<label class="col-form-label-sm">Email Preview:</label>
		<iframe name="previewFrame" id="previewFrame" class="form-control d-none" style="height:400px;"></iframe>
	</div>
</div>
<div id="divMassEmailInstrcsLoading"></div>
</cfoutput>
