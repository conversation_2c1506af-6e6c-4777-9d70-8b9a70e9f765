<cfset local.zoneP1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
    <cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['P'],1)>
        <cfset local.zoneP1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['P'][1].data,"<p>",""),"</p>",""))> 
    </cfif>
</cfif>
<cfset local.zoneQ1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
    <cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
        <cfset local.zoneQ1Content = event.getValue("mc_pageDefinition").pageZones['Q'][1].data> 
    </cfif>
</cfif>
<cfset local.zoneR1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='R',event=event)>
    <cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['R'],1)>
        <cfset local.zoneR1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['R'][1].data,"<p>",""),"</p>",""))> 
    </cfif>
</cfif>
<cfset local.zoneS1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='S',event=event)>
    <cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['S'],1)>
        <cfset local.zoneS1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['S'][1].data,"<p>",""),"</p>",""))> 
    </cfif>
</cfif>
<cfset local.zoneT1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='T',event=event)>
    <cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['T'],1)>
        <cfset local.zoneT1Content = event.getValue("mc_pageDefinition").pageZones['T'][1].data> 
    </cfif>
</cfif>
<cfoutput>
<!--Footer Start-->
<div class="footer">
    <div class="container containerCustom">
    <div class="row d-flex-wrap">
        <span class="quickLinkFooterWrapper hide">#local.zoneP1Content#</span>
        <span class="quickLinkFooterHolder"></span>
    </div>
    <hr class="footer-line">
    <div class="row d-flex-wrap">
        <div class="col5 footer-info">
            <div class="foot-logo">#local.zoneQ1Content#</div>
        </div>
        <div class="col6 footer-links">
            <ul>
                <li>
                <p>#local.zoneR1Content#</p>
                </li>
            </ul>
        </div>
        <div class="col7 footer-links socialBlock">
            #local.zoneS1Content#
        </div>
    </div>
    </div>
    <div class="copyright-block">
    <div class="container containerCustom">
        <div class="row-fluid">
            <div class="copyright clearfix">
                #local.zoneT1Content#
            </div>
        </div>
    </div>
    </div>
</div>
<!--Footer End-->
<!-- Print Footer -->
<div class="printFooter">
    #local.zoneT1Content#
</div>
<!-- Print Footer -->
</cfoutput>