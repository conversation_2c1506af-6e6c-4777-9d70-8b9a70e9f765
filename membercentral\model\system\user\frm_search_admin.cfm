<cfsavecontent variable="local.jsFunc">
	<cfoutput>
	<script type="text/javascript">
		function checkFilledFields(scope) {
			var inputs = scope.find('input[type="text"], select').not('.skipfillcheck');  
			var filled = inputs.filter(function(){
				return $.trim($(this).val()).length > 0;
			});
			return filled.length;
		}

		function fnCreateMember() {
			<cfif arguments.addMemNewTab>
				window.open('#local.addMemberFromTabLink#','_blank');
			<cfelse>
				top.MCModalUtils.showLoading('Add Member');
				top.$('##MCModalFooter').removeClass('d-none');
				top.$('##MCModalFooter').addClass('text-right');
				top.$('##MCModalFooter').html('<button type="button" class="btn btn-sm btn-primary" name="btnMCModalSave" id="btnMCModalSave">Save Information</button>');
				self.location.href = '#local.addMemberLink#';
			</cfif>
		}
		function fnCustomSelectMember() {
			#arguments.onSubmitHandler#();
		}
		
		$(function() {
			$('##frmLocator input').keyup(function(event) {
				if (event.keyCode == 13) #arguments.onSubmitHandler#();
			});
			mcActivateTooltip($('##divSearchFormContainer'));
			if (!top.MCModalUtils.isShown()) {			
				$('.frmLocatorModal').remove();
				$('.frmLocatorPopup').removeClass('d-none');
			}else{
				$('.frmLocatorPopup').remove();
				$('.frmLocatorModal').removeClass('d-none');
			}
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.jsFunc#">

<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
<div id="AL_err_div" class="alert alert-danger" style="display:none"></div>
<cfif arguments.isPopup>
	<div class="px-3 pt-2">
</cfif>
<form name="frmLocator" id="frmLocator" class="frmLocatorPopup d-none" action="#arguments.searchPostURL#" method="post" onsubmit="return false;">
	<input type="hidden" name="fsid_s" id="fsid_s" value="#val(local.qryFieldsetInfo.fieldsetID)#">		
	<cfset local.showMatchingOption = false>
	<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
		<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
			<cfcase value="TEXTBOX">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
					<div class="form-row">
						<div class="col">
							<div class="form-label-group mb-2">
								<select name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" class="custom-select skipfillcheck">
									<cfloop list="5,10,25,50,100" index="local.thisrad">
										<option value="#local.thisrad#" <cfif local.thisrad eq 10>selected</cfif>>within #local.thisrad# miles</option>
									</cfloop>
								</select>
								<label for="#local.thisfield.xmlattributes.fieldCode#_radius">
									#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#
								</label>
							</div>
						</div>
						<div class="col">
							<div class="form-label-group mb-2">
								<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
								<label for="#local.thisfield.xmlattributes.fieldCode#">
									of postal code
								</label>
							</div>
						</div>
					</div>
				<cfelse>
					<div class="form-label-group mb-2">
						<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
						<cfif local.thisfield.xmlAttributes.dataTypeCode eq "STRING">
							<cfset local.showMatchingOption = true>
						</cfif>
						<label for="#local.thisfield.xmlattributes.fieldCode#">
							#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
							<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
							</cfif>
						</label>
					</div>
				</cfif>
			</cfcase>
			<cfcase value="RADIO">
				<div class="form-row mb-2">
					<label class="col-auto mb-0">
						#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
						<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
						</cfif>
					</label>
					<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
						<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
						<cfcase value="STRING">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
						</cfcase>
						<cfcase value="DECIMAL2">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
						</cfcase>
						<cfcase value="INTEGER">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
						</cfcase>
						<cfcase value="DATE">
							<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
						</cfcase>
						<cfcase value="BIT">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
						</cfcase>
						<cfdefaultcase>
							<cfset local.thisOptColValue = "">
						</cfdefaultcase>
						</cfswitch>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#" value="#local.thisOptColValue#">
							<label class="form-check-label" for="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#"><cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif></label>
						</div>
					</cfloop>
				</div>
			</cfcase>
			<cfcase value="SELECT,CHECKBOX">
				<div class="form-label-group mb-2">
					<cfif ReFindNoCase('mat?_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
						<cfset local.qryStates = application.objCommon.getStates()>
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfoutput query="local.qryStates" group="countryID">
								<optgroup label="#local.qryStates.country#">
								<cfoutput>
									<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
								</cfoutput>
								</optgroup>
							</cfoutput>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_recordtypeid">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_membertypeid">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_status">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>													
					<cfelse>
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
								<cfcase value="STRING">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
								</cfcase>
								<cfcase value="DECIMAL2">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
								</cfcase>
								<cfcase value="INTEGER">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
								</cfcase>
								<cfcase value="DATE">
									<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
									<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
								</cfcase>
								<cfcase value="BIT">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
									<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
								</cfcase>
								<cfdefaultcase>
									<cfset local.thisOptColValue = "">
									<cfset local.thisOptColDisplay = "">
								</cfdefaultcase>
								</cfswitch>
								<option value="#local.thisOptColValue#">#local.thisOptColDisplay#</option>
							</cfloop>
						</select>
					</cfif>
					<label for="#local.thisfield.xmlattributes.fieldCode#">
						#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
						<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
						</cfif>
					</label>
				</div>
			</cfcase>
			<cfcase value="DATE">
				<div class="form-label-group mb-2">
					<div class="input-group dateFieldHolder">
						<input type="text"  name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" class="form-control dateControl">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="#local.thisfield.xmlattributes.fieldCode#"><i class="fa-solid fa-calendar"></i></span>
							<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');"><i class="fa-solid fa-circle-xmark"></i></a></span>
						</div>
						<label for="#local.thisfield.xmlattributes.fieldCode#">
							#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
							<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
							</cfif>
						</label>
					</div>
				</div>
				<cfsavecontent variable="local.datejs">
					<script language="javascript">
						$(function() {
							mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
							mca_setupCalendarIcons('frmLocator');
						});
					</script>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
			</cfcase>
		</cfswitch>
		<cfif local.thisfield.xmlattributes.isRequired is 1>
			<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
			<cfcase value="TEXTBOX,DATE">
				<cfsavecontent variable="local.jsValidation">
					#local.jsValidation#
					if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfsavecontent>
			</cfcase>
			<cfcase value="RADIO">
				<cfsavecontent variable="local.jsValidation">
					#local.jsValidation#
					if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "RADIO", false)) locateErr += '<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>';
				</cfsavecontent>
			</cfcase>
			<cfcase value="SELECT,CHECKBOX">
				<cfsavecontent variable="local.jsValidation">
					#local.jsValidation#
					if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].options[_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfsavecontent>
			</cfcase>
			</cfswitch>
			<cfset local.showReqFlag = true>
		</cfif>
	</cfloop>
	<div class="form-group row pt-2">
		<cfif local.showMatchingOption>
			<div class="col-8">
				<label for="fs_match">Find matches
					<select name="fs_match" id="fs_match" class="form-control form-control-sm d-inline w-auto mx-2 skipfillcheck">
						<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
						<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
						<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
					</select>
					the terms I entered.
				</label>
			</div>
		</cfif>
		<div class="<cfif local.showMatchingOption>col-4<cfelse>col-12</cfif> text-right">
			<button name="btn_st_s" id="btn_st_s" type="button" class="btn btn-sm btn-primary" onclick="#arguments.onSubmitHandler#()">#arguments.buttonText#</button>
			<cfif arguments.hasAddMemberRights>
				<span id="addArea">
					<button type="button" class="btn btn-sm btn-primary" onclick="fnCreateMember();">
						<span class="btn-wrapper--icon">
							<i class="fa-light fa-user-plus"></i>
						</span>
						<span class="btn-wrapper--label">Add Member</span>
					</button>
				</span>
			</cfif>
		</div>
	</div>
	<cfif local.showReqFlag>
		<div class="form-group row">
			<div class="col-12"><i>* required field</i></div>
		</div>
	</cfif>
</form>
<form name="frmLocator" class="frmLocatorModal" id="frmLocator" action="#arguments.searchPostURL#" method="post" onsubmit="return false;">
	<input type="hidden" name="fsid_s" id="fsid_s" value="#val(local.qryFieldsetInfo.fieldsetID)#">		
	<cfset local.showMatchingOption = false>
	<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
		<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
			<cfcase value="TEXTBOX">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
					<div class="form-row">
						<div class="col">
							<div class="form-label-group mb-2">
								<select name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" class="custom-select skipfillcheck">
									<cfloop list="5,10,25,50,100" index="local.thisrad">
										<option value="#local.thisrad#" <cfif local.thisrad eq 10>selected</cfif>>within #local.thisrad# miles</option>
									</cfloop>
								</select>
								<label for="#local.thisfield.xmlattributes.fieldCode#_radius">
									#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#
								</label>
							</div>
						</div>
						<div class="col">
							<div class="form-label-group mb-2">
								<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
								<label for="#local.thisfield.xmlattributes.fieldCode#">
									of postal code
								</label>
							</div>
						</div>
					</div>
				<cfelse>
					<div class="form-label-group mb-2">
						<input type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="form-control">
						<cfif local.thisfield.xmlAttributes.dataTypeCode eq "STRING">
							<cfset local.showMatchingOption = true>
						</cfif>
						<label for="#local.thisfield.xmlattributes.fieldCode#">
							#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
							<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
							</cfif>
						</label>
					</div>
				</cfif>
			</cfcase>
			<cfcase value="RADIO">	
				<div class="form-row mb-2">
					<label class="col-auto mb-0">
						#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
						<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
						</cfif>
					</label>
					<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
						<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
						<cfcase value="STRING">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
						</cfcase>
						<cfcase value="DECIMAL2">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
						</cfcase>
						<cfcase value="INTEGER">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
						</cfcase>
						<cfcase value="DATE">
							<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
						</cfcase>
						<cfcase value="BIT">
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
						</cfcase>
						<cfdefaultcase>
							<cfset local.thisOptColValue = "">
						</cfdefaultcase>
						</cfswitch>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#" value="#local.thisOptColValue#">
							<label class="form-check-label" for="#local.thisfield.xmlattributes.fieldCode#_#local.thisOpt.xmlAttributes.valueID#"><cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif></label>
						</div>
					</cfloop>
				</div>
			</cfcase>
			<cfcase value="SELECT,CHECKBOX">
				<div class="form-label-group mb-2">
					<cfif ReFindNoCase('mat?_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
						<cfset local.qryStates = application.objCommon.getStates()>
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfoutput query="local.qryStates" group="countryID">
								<optgroup label="#local.qryStates.country#">
								<cfoutput>
									<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
								</cfoutput>
								</optgroup>
							</cfoutput>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_recordtypeid">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_membertypeid">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>
					<cfelseif local.thisfield.xmlattributes.fieldCode eq "m_status">
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<option value="#local.thisOpt.xmlAttributes.valueID#">#local.thisOpt.xmlAttributes.columnValueString#</option>
							</cfloop>
						</select>														
					<cfelse>
						<select name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" class="custom-select">
							<option value=""></option>
							<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
								<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
								<cfcase value="STRING">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
								</cfcase>
								<cfcase value="DECIMAL2">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
								</cfcase>
								<cfcase value="INTEGER">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
									<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
								</cfcase>
								<cfcase value="DATE">
									<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
									<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
								</cfcase>
								<cfcase value="BIT">
									<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
									<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
								</cfcase>
								<cfdefaultcase>
									<cfset local.thisOptColValue = "">
									<cfset local.thisOptColDisplay = "">
								</cfdefaultcase>
								</cfswitch>
								<option value="#local.thisOptColValue#">#local.thisOptColDisplay#</option>
							</cfloop>
						</select>
					</cfif>
					<label for="#local.thisfield.xmlattributes.fieldCode#">
						#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
						<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
						</cfif>
					</label>
				</div>
			</cfcase>
			<cfcase value="DATE">
				<div class="form-label-group mb-2">
					<div class="input-group dateFieldHolder">
						<input type="text"  name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="" class="form-control dateControl">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="#local.thisfield.xmlattributes.fieldCode#"><i class="fa-solid fa-calendar"></i></span>
							<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');"><i class="fa-solid fa-circle-xmark"></i></a></span>
						</div>
						<label for="#local.thisfield.xmlattributes.fieldCode#">
							#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1> * </cfif>
							<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
								<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#"></i>
							</cfif>
						</label>
					</div>
				</div>
				<cfsavecontent variable="local.datejs">
					<script language="javascript">
						$(function() {
							mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
							mca_setupCalendarIcons('frmLocator');
						});
					</script>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
			</cfcase>
		</cfswitch>
	</cfloop>
	<cfif local.showMatchingOption>
		<div class="form-group row">
			<div class="col-12 py-3">
				<label for="fs_match">Find matches
					<select name="fs_match" id="fs_match" class="form-control form-control-sm d-inline w-auto mx-2 skipfillcheck">
						<option value="s" <cfif local.qrySettings.defaultSearchOption EQ "s">SELECTED</cfif>>beginning with</option>
						<option value="c" <cfif local.qrySettings.defaultSearchOption EQ "c">SELECTED</cfif>>containing</option>
						<option value="e" <cfif local.qrySettings.defaultSearchOption EQ "e">SELECTED</cfif>>exactly matching</option>
					</select>
					the terms I entered.
				</label>
			</div>
		</div>
	</cfif>
	<div class="text-right">
		<button name="btn_st_s" id="btn_st_s" type="button" class="btn btn-sm btn-primary" onclick="#arguments.onSubmitHandler#()">#arguments.buttonText#</button>
		<cfif arguments.hasAddMemberRights>
			<span id="addArea">
				<button type="button" class="btn btn-sm btn-primary" onclick="fnCreateMember();">
					<span class="btn-wrapper--icon">
						<i class="fa-light fa-user-plus"></i>
					</span>
					<span class="btn-wrapper--label">Add Member</span>
				</button>
			</span>
		</cfif>
	</div>
	<cfif local.showReqFlag>
		<div class="small text-dim"><i>* required field</i></div>
	</cfif>
</form>
<cfif arguments.isPopup>
	</div>
</cfif>
</cfoutput>

<cfsavecontent variable="local.jsFunc">
	<cfoutput>
	<script type="text/javascript">
	function validateALSearch() {
		var _CF_this = document.forms['frmLocator'];
		var locateErr = '';
		#local.jsValidation#

		if(locateErr.length == 0 && !checkFilledFields($('##frmLocator'))) 
			locateErr = 'Find Members by selecting one or more filters.';

		if (locateErr.length > 0) {
			showALAlert(locateErr);
			return false;
		} else {
			hideALAlert();
			return true;
		}
	};
	function hideALAlert() {
		$('##AL_err_div').html('').hide();
	};
	function showALAlert(msg) {
		$('##AL_err_div').html(msg).show();
		$("html, body").animate({ scrollTop: 0 }, "slow");
	};
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.jsFunc#">