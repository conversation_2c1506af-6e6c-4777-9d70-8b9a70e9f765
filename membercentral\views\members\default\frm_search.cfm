<cfset local.jsValidation = "">
<cfset local.showReqFlag = false>
<cfset local.xmlFields = attributes.data.xmlFields>
<cfset local.fieldsetInfo = attributes.data.fieldsetInfo>
<cfset local.qrySettings = attributes.data.qrySettings>

<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>

<div class="tsAppHeading">#attributes.data.memberDirectoryInfo.applicationInstanceName#</div>
<br/>
<div class="tsAppBodyText">

	<cfif ArrayLen(local.xmlFields.xmlRoot.xmlChildren) is 0 and arrayLen(attributes.data.arrClassifications) is 0>
		<br/>Searching is not available at this time.
	<cfelse>
		<cfform id="frmMemberDirectory" name="frmMemberDirectory" method="post" action="/?#attributes.data.baseQueryString#&dirAction=SearchResults">
		<div class="tsAppBodyText"><div id="MD_err_div" style="display:none;"></div></div>
		<table cellpadding="4" cellspacing="0">

		<cfset local.showMatchingOption = false>
		<cfloop array="#attributes.data.arrFormFields#" index="local.thisfield">
			<cfoutput>
			<tr valign="top">
				<td class="tsAppBodyText"><cfif local.thisfield.isRequired is 1>*</cfif>&nbsp;</td>
				<td class="tsAppBodyText" nowrap>#encodeForHTML(local.thisfield.fieldLabel)#:</td>
				<td class="tsAppBodyText">
					<cfif local.fieldsetInfo.showHelp AND len(trim(local.thisfield.fieldDescription))>
						<img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" />
					<cfelse>
					&nbsp;
					</cfif>
				</td>
				<td class="tsAppBodyText">
					<cfswitch expression="#local.thisfield.displayTypeCode#">
					<cfcase value="TEXTBOX,TEXTAREA,HTMLCONTENT">
						<cfif StructKeyExists(local.thisfield, "isPostalCode") AND local.thisfield.isPostalCode>
							Within 
							<cfselect class="tsAppBodyText" name="#local.thisfield.fieldCode#_radius" id="#local.thisfield.fieldCode#_radius" >
								<cfloop list="5,10,25,50,100" index="local.thisrad">
									<option value="#local.thisrad#">#local.thisrad#</option>
								</cfloop>
							</cfselect>
							#attributes.data.memberDirectoryInfo.distanceLabel# of #encodeForHTML(local.thisfield.fieldLabel)#
							<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="8">
						<cfelse>
							<cfif local.thisfield.displayTypeCode eq "TEXTBOX">
								<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="24">
							<cfelse>
								<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="48">
							</cfif>
							<cfif local.thisfield.dataTypeCode eq "STRING">
								<cfset local.showMatchingOption = true>
							</cfif>
						</cfif>
					</cfcase>
					<cfcase value="RADIO">
						<cfloop array="#local.thisfield.arrOptions#" index="local.thisOpt"> 
							<cfinput type="radio" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="#local.thisOpt#">#local.thisOpt#<br/>
						</cfloop>
					</cfcase>
					<cfcase value="SELECT,CHECKBOX">
						<cfif StructKeyExists(local.thisfield, 'qryData')>
							<cfselect class="tsAppBodyText" query="local.thisfield.qryData" value="#local.thisfield.optValue#" display="#local.thisfield.optDisplay#" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" queryPosition="below"><option value=""></option></cfselect>
						<cfelse>
							<cfselect name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" >
								<option value=""></option>
								<cfloop array="#local.thisfield.arrOptions#" index="local.thisOpt">
									<option value="#local.thisOpt#"><cfif local.thisfield.dataTypeCode eq "BIT">#YesNoFormat(local.thisOpt)#<cfelse>#local.thisOpt#</cfif></option>
								</cfloop>
							</cfselect>
						</cfif>
					</cfcase>
					<cfcase value="DATE">
						<cfinput type="text" name="#local.thisfield.fieldCode#" id="#local.thisfield.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="12">
						<cfsavecontent variable="local.datejs">
							<cfoutput>
							<script language="javascript">
								$(function() { 
									mca_setupDatePickerField('#local.thisfield.fieldCode#');
									$("##btnClear#local.thisfield.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.fieldCode#');return false; } );
								});
							</script>
							<style type="text/css">
							###local.thisfield.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
							</style>
							</cfoutput>
						</cfsavecontent>
						<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">							
					</cfcase>
					</cfswitch>
				</td>
			</tr>
			</cfoutput>
		</cfloop>

		<cfloop array="#attributes.data.arrFormFields#" index="local.thisfield">
			<cfif local.thisfield.isRequired is 1>
				<cfswitch expression="#local.thisfield.displayTypeCode#">
				<cfcase value="TEXTBOX">
					<cfsavecontent variable="local.jsValidation">
						<cfoutput>
						#local.jsValidation#
						if(!_CF_hasValue(_CF_this['#local.thisfield.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required.<br/>";
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfcase value="RADIO">
					<cfsavecontent variable="local.jsValidation">
						<cfoutput>
						#local.jsValidation#
						if(!_CF_hasValue(_CF_this['#local.thisfield.fieldCode#'], "RADIO", false)) locateErr += '<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required.<br/>';
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfcase value="SELECT,CHECKBOX">
					<cfsavecontent variable="local.jsValidation">
						<cfoutput>
						#local.jsValidation#
						if (_CF_this['#local.thisfield.fieldCode#'].options[_CF_this['#local.thisfield.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required.<br/>";
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfcase value="DATE">
					<cfsavecontent variable="local.jsValidation">
						<cfoutput>
						#local.jsValidation#
						if(!_CF_hasValue(_CF_this['#local.thisfield.fieldCode#'], "DATEFIELD", false) || !_CF_checkdate(_CF_this['#local.thisfield.fieldCode#'].value, true)) locateErr += "<i>#htmlEditFormat(local.thisfield.fieldLabel)#</i> is required and must be a valid date.<br/>";
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				</cfswitch>
				<cfset local.showReqFlag = true>
			</cfif>
		</cfloop>

		<cfloop array="#attributes.data.arrClassifications#" index="local.thisClassification">
			<tr valign="top">
				<td class="tsAppBodyText">&nbsp;</td>
				<td class="tsAppBodyText" nowrap>#encodeForHTML(local.thisClassification.name)#:</td>
				<td class="tsAppBodyText">&nbsp;</td>
				<td class="tsAppBodyText">
					<cfselect class="tsAppBodyText" name="mg_gid" id="mg_gid" query="local.thisClassification.qryClassificationslinks" display="groupName" value="groupId" queryPosition="below">
						<option value=""></option>
					</cfselect>
				</td> 
			</tr> 
		</cfloop>
		<cfif local.showMatchingOption>
			<tr><td>&nbsp;</td>
				<td colspan="3" nowrap class="tsAppBodyText" style="padding-top:14px;">
				<div role="radiogroup" aria-labelledby="fs_match_label">
					<div style="padding-left:25px;padding-top:5px;">
						Find matches
						<select name="fs_match" id="fs_match">
							<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
							<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
							<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
						</select>
						the terms I entered.
					</div>
				</div>
				</td>
			</tr>
		</cfif>

		<tr>
			<td>&nbsp;</td>
			<td colspan="3" class="tsAppBodyText" style="padding-top:14px;">
				<button name="btnSubmit" type="button" class="tsAppBodyButton" onClick="doMDSearch()"><i class="icon-search"></i> &nbsp; Search</button>
			</td>
		</tr>
		<cfif local.showReqFlag>
			<tr><td colspan="4" class="tsAppBodyText"><i>* required field</i></td></tr>
		</cfif>

		</table>
		</cfform> 
	</cfif>
</div>
<br/>
<div class="tsAppBodyText">#attributes.data.searchContentStruct.rawContent#</div>
</cfoutput>

<cfsavecontent variable="local.jsFunc">
	<style type="text/css">
		.alert { background:#fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid #f00; border-bottom:2px solid #f00; }
	</style>
	<script language="javascript">
	hideMDAlert = function() {
		var abox = document.getElementById('MD_err_div');
			abox.innerHTML = '';
			abox.style.display = 'none';
	};
	showMDAlert = function(msg) {
		var abox = document.getElementById('MD_err_div');
			abox.innerHTML = msg;
			abox.className = 'alert';
			abox.style.display = '';
	};
	validateMDSearch = function() {
		var _CF_this = document.forms['frmMemberDirectory'];
		var locateErr = '';
		<cfoutput>#local.jsValidation#</cfoutput>
		if (locateErr.length > 0) {
			showMDAlert(locateErr);
			return false;
		} else {
			hideMDAlert();
			return true;
		}
	};
	doMDSearch = function() {
		if (validateMDSearch()) document.forms['frmMemberDirectory'].submit();
	};
	$(function() {
		$('#frmMemberDirectory input').keyup(function(event) {
			if (event.keyCode == 13)
				doMDSearch();
		});
	});

	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.jsFunc)#">
