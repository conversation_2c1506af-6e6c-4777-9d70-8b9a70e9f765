<cfoutput>
<div class="toolButtonBar">
	<cfif local.hasAddSWBRegistrantsRights>
		<div><a href="javascript:addSWReg(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new registrant."><i class="fa-regular fa-user-plus"></i> Add Registrant</a></div>
	</cfif>
	<div><a href="javascript:filterSWBProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrants."><i class="fa-regular fa-filter"></i> Filter Registrants</a></div>
	<cfif local.hasMassEmailSWBRegistrantsRights>
		<cfif local.qryBundle.isSWOD>
			<div><a href="javascript:massEmailSWBConnectionInstructions();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email connection instructions to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Connection Instructions</a></div>
		<cfelse>
			<div><a href="javascript:massEmailSWBInstructions();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email program components to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Program Components</a></div>
		</cfif>
	</cfif>
</div>

<div id="divFilterForm" class="d-none divSWRegistrantsTool">
	<form name="frmRegistrantFilter" id="frmRegistrantFilter">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Registrant Filters
				</div>
			</div>
			<div class="card-body pb-2">
				<div class="form-row">
					<div class="col-xl-6 col-lg-12">
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="rDateFrom" id="rDateFrom" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="rDateFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="rDateFrom">Registered From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="rDateTo" id="rDateTo" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="rDateTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="rDateTo">Registered To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>						
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnClearRegFilters" class="btn btn-sm btn-secondary" onclick="clearSWBRegFilters();">Clear Filters</button>
				<button type="button" onclick="doFilterSWBProgramRegistrants()" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Filter Registrants</button>
			</div>	
		</div>
	</form>
</div>

<div class="d-flex align-items-end mt-4">
	<h5>Registrants</h5>
</div>

<table id="SWBRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Registrant</th>
			<th>Registered</th>
			<th>Seminars</th>
			<th>Billed</th>
			<th>Due</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
</cfoutput>