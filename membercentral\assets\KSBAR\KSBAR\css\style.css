@charset "utf-8";
body {
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    font-style: normal;
    font-size: 18px;
    line-height: 1.5;
    font-style: normal;
    color: #000000;
    font-family: 'activ-grotesk', sans-serif;
    font-weight: 400;
}
*, input[type="search"] {
    -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; 
}
input {
    outline: none; 
}
img {
    max-width: 100%; 
}
a {
    color: #a72823;
    text-decoration: none;
}
a:hover, a:focus {
    color: #0c2c48;
    text-decoration: none;
}

/* Typography */
.HeaderText, h1 {
    font-size: 36px;
    font-family: "proxima-nova";
    color: #00529b;
    font-weight: 700;
    margin: 0 0 20px;
}
.HeaderTextSmall, h3 {
    font-size: 28px;
    color: #00529b;
    line-height: 1.2;
    font-family: "proxima-nova";
    font-weight: 700;
    margin: 0 0 15px;
}

.SectionHeader {
    font-weight: bold;
    font-size: 40px;
    display: block;
    color: #00529b;
    margin-bottom: 30px;
    margin-top: 0;
}
.SectionHeader:after {
    display: block;
    content: "";
    width: 100%;
    background: #00519b;
    height: 4px;
    margin-top: 8px;
}
.SectionHeader.darkblue {
    color: #0c2c48;
}
.SectionHeader.darkblue:after {
    border-color: #0b2b47;
}
.text-white {
    color: #ffffff !important;
}
.SectionHeader.text-white:after {
    background: #0b2b47;
}
.SubHeading, h4 {
    font-size: 22px;
    color: #00529b;
    font-family: "proxima-nova";
}   
.ColumnHeader {
    color: #00529b;
    font-size: 32px;
    margin: 0;
    line-height: 1.3;
    font-family: "activ-grotesk";
}
.DateStyle {
    font-size: 18px;
    display: block;
    color: #707070;
    font-weight: 700;
    margin-top: 5px;
    font-family: "proxima-nova";
}
h1, h2, h3, h4, h5, h6 {
    font-family: "proxima-nova";
    color: #00529b;
    font-weight: 700;
    line-height: 1.2;
}
p, .BodyText {
    font-size: 18px;
    line-height: 1.5;
    color: #000000;
    font-family: 'activ-grotesk', sans-serif;
    font-weight: 400;
}
p.BodyTextLarge, .BodyTextLarge {
    font-size: 20px; 
}
p.InfoText, .InfoText {
    font-size: 14px; 
}
.GothamBook {
    font-family: 'activ-grotesk', sans-serif !important;
}
.GothamBold {
    font-family: 'activ-grotesk', sans-serif !important;
    font-weight: 700 !important;
}
.GothamMedium {
    font-family: 'activ-grotesk', sans-serif !important;
    font-weight: 500 !important;
}

.TitleText {
    font-size: 65px;
    font-weight: bold;
    line-height: normal;
    margin-bottom: 15px;
    border: 0;
    text-shadow: none;
    text-align: left;
    color: #00529b;
}
.bannerInner .TitleText {
    color: #ffffff;
}
.color-y {
    color: #eab11e;
}
.KBAButton {
    background: #e9b11d;
    border: 1px solid #e9b11d;
    color: #0c2c48;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.10em;
    text-decoration: none;
    display: inline-block;
    padding: 10px 30px;
    border-radius: 0;
}
.KBAButton:after {
    content: "";
    width: 24px;
    height: 16px;
    display: inline-block;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAANBAMAAAC5okgUAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACpQTFRFAAAA////////////////////////////////////////////////////hrvKLwAAAA50Uk5TAApoYAjwMODv//4Q3y3559shAAAAOklEQVR4nGNgAAMBBjhQRjBdDeBMtmCEcHoCQriNgWEmFKzYABc9DFeQATeCrRguaI4wIAxh7AUgBgAJbQwMHmPBcAAAAABJRU5ErkJggg==');
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    background-size: contain;
    margin-top: -2px;
    margin-left: 10px;
}
.KBAButton:hover {
    background: #00519b;
    border-color: #ffffff;
    color: #ffffff;
    text-decoration: none;
}

.NavyButton {
    color: #ffffff;
    border: 1px solid #0b2b47;
    background: #0b2b47;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    text-decoration: none;
    display: inline-block;
    padding: 7px 15px;
    font-weight: 600;
}
.NavyButton:after {
    content: "";
    width: 24px;
    height: 16px;
    display: inline-block;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAANBAMAAAC5okgUAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACpQTFRFAAAA////////////////////////////////////////////////////hrvKLwAAAA50Uk5TAApoYAjwMODv//4Q3y3559shAAAAOklEQVR4nGNgAAMBBjhQRjBdDeBMtmCEcHoCQriNgWEmFKzYABc9DFeQATeCrRguaI4wIAxh7AUgBgAJbQwMHmPBcAAAAABJRU5ErkJggg==');
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    background-size: contain;
    margin-top: -2px;
    margin-left: 10px;
}
.NavyButton:hover {
    background: #00529b;
    border-color: #00529b;
    color: #ffffff;
    text-decoration: none;
}
.NavyButton:hover:after {
    filter: contrast(0)brightness(100);
}

.LightBlueButton {
    color: #ffffff;
    border: 1px solid #438cca;
    background: #438cca;
    font-size: 14px;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 0.150em;
    text-decoration: none;
    display: inline-block;
    padding: 12px 20px;
    font-weight: 600;
}
.LightBlueButton:after {
    content: "";
    width: 24px;
    height: 16px;
    display: inline-block;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAANBAMAAAC5okgUAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACpQTFRFAAAA////////////////////////////////////////////////////hrvKLwAAAA50Uk5TAApoYAjwMODv//4Q3y3559shAAAAOklEQVR4nGNgAAMBBjhQRjBdDeBMtmCEcHoCQriNgWEmFKzYABc9DFeQATeCrRguaI4wIAxh7AUgBgAJbQwMHmPBcAAAAABJRU5ErkJggg==');
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    background-size: contain;
    margin-top: -2px;
    margin-left: 10px;
}
.LightBlueButton:hover {
    background: #00529b;
    border-color: #00529b;
    color: #ffffff;
    text-decoration: none;
}
.LightBlueButton:hover:after {
    filter: contrast(0)brightness(100);
}
/* End Typography */


.pd_40 {
    padding: 40px 0px; 
}
.pd_50 {
    padding: 50px 0px; 
}
.pd_60 {
    padding: 60px 0; 
}
.pd_70 {
    padding: 70px 0; 
}
.pd_30 {
    padding: 30px 0;
}
.mb-30 {
    margin-bottom: 30px;
}
.gray-bg {
    background: #DDD8D3; 
}
.clearfix::before, .clearfix::after {
    content: ""; display: table; width: 100%; clear: both; 
}
.container.containerCustom {
    padding: 0 15px; 
}
.Highlight h2 {
    color: #ffffff;
}
.btns-wrap a:not(:last-child) {
    margin-right: 15px;
}
font {
    font-size: 24px; font-weight: 700; color: #313131; 
}
.YellowUnderline {
    position: relative; 
}
.YellowUnderline:after {
    content: ""; position: absolute; top: 100%; left: 0; width: 100%; height: 3px; background: #e1b658; 
}
.WhiteUnderline {
    position: relative; 
}
.WhiteUnderline:after {
    content: ""; position: absolute; top: 100%; left: 0; width: 100%; height: 3px; background: #fff; 
}
.WhiteBorder {

   border: 2px solid #fff;   font-size: 14px;   font-weight: 700;   height: 40px;   min-width: 162px;   text-transform: uppercase;   border-radius: 50px;   
   color: #FFFFFF;   display: inline-block; 
   vertical-align: middle;   line-height: 36px;   text-align: center;
}
.WhiteBorder:hover, .WhiteBorder:focus {
    background: #F1B828; color: #ffffff; border-color: #F1B828;
}/*.Barlow {


  }*/
*::-webkit-input-placeholder {


  }
*::-moz-placeholder {


  }
*:-ms-input-placeholder {


  }
*:-moz-placeholder {


  }
.xs979 {
    display: none !important; 
}
.xs767, .xsVisible {
    display: none !important; 
}
.xsHidden979 {
    display: block !important; 
}
.xsHidden767, .xsHidden {
    display: block !important; 
}
.textUnderline {
    text-decoration: underline; 
}
/***Header***/
.printHeader, .printFooter {
    display: none; 
}
.header {
    background: #fff; min-height: 90px; position: fixed; width: 100%; top: 0; z-index: 999; 
}
.headerSpace {
    width: 100%;
    min-height: 90px;
    background-color: transparent;
}
.header .navbar {
    margin-bottom: 0; 
}
.header .navbar-inner {
    border: none;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0;
    min-height: inherit;
    background: #ffffff;
}
.header .navbar-brand {
    margin-left: 0px;
    float: left;
    max-height: 100%;
    display: inline-flex;
    align-items: center;
    height: 90px;
    padding-left: 40px;
}
.header .navbar .nav>li.dropdown>a:after {width: 6px;height: 6px;content: "";position: relative;top: -3px;z-index: 1;left: 0;margin-left: 5px;bottom: 0px;border: 1px solid #5b8ab5;border-style: none solid solid none;display: inline-block;transform: rotate(45deg);}
.header .navbar .nav > li.searchBtnFn > a:after {
        display: none;
    }
.header .navbar .nav > li.searchBtnFn > a{display:grid}
.header .navbar .nav li.dropdown .memberSection li, .header .navbar .nav li.dropdown .memberSection li p, .header .navbar .nav li.dropdown .memberSection li a {
    color: #fff; display: inline-block; padding: 0; font-weight: 300; font-size: 16px;  }
.header .navbar .nav li.dropdown .memberSection li p {
    margin-bottom: 20px; 
}
.header .navbar .nav li.dropdown .memberSection li a {
    text-decoration: underline; 
}
.header .navbar .nav li.dropdown .memberSection li label {
    font-weight: 300; font-size: 16px;  }
.header .navbar .nav li.dropdown .memberSection li input {
    background-color: #fff;border: 0;height: 45px;border-radius: 0;width: 100%;margin-bottom: 15px;color: #2d2d2d;padding: 0 10px;
}
.header .navbar .nav li.dropdown .memberSection li input:focus {
    box-shadow: none;
}
.header .navbar .nav li.dropdown .memberSection li form a.btn {
    color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; display: inline-block; width: auto; text-decoration: none; 
}
.header .navbar .nav li.dropdown .memberSection li form a.btn:hover {
    background: #fff; color: #2d3e55; 
}
.header .navbar .nav li.dropdown .memberSection li form a {
    width: 50%; float: left; 
}
font .navbar .nav li.dropdown .memberSection li form a.WhiteBorder {
    border: 2px solid; font-size: 18px; font-weight: 700; height: 52px; min-width: 120px; text-transform: capitalize; border-radius: 0px;   display: inline-block; vertical-align: middle; line-height: 46px; margin: 0;  box-shadow: none; text-shadow: none; padding: 0 25px; text-align: center; width: auto;  text-decoration: none; 
}
.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:hover, .header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:focus {
    background: #BA0C2F; color: #ffffff; border-color: #BA0C2F; 
}
.header .navbar .nav li.dropdown .memberSection li form a:last-child {
      font-size: 14px; text-align: left; padding: 0px; text-decoration: none; margin-left: 15px; margin-top: 5px; text-transform: inherit; 
    }
.header .navbar .nav li.dropdown li a:hover, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover, .header .navbar .nav li.dropdown li a:focus, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:focus, .header .navbar .nav li .dropdown-menu>li:hover a {
    background: transparent; 
}
.dropdown li {
    padding: 0 0px; 
}
.dropdown li:not(:last-child) {
    border-bottom:1px solid #aeaeae;
}
.header .navbar .nav li.active a {
    color: #0BBA97; background-color: #ffffff; border-color: #eeeeee; box-shadow: none; 
}
.header .navbar .nav>li {
    display: grid;
    padding: 0;
    position: static;
    align-items: center;
}
.header .navbar .nav li a:hover, .header .navbar .nav li a:focus {
    background: transparent; color: #2d2d2d; box-shadow: none;
}
.header .nav-collapse.collapse {
    margin: 0; 
}
.header .nav-collapse .nav {
    margin: 0; float: right; width: auto; position: static; display: inline-flex;align-items: center; height: 90px;
}
.header .navbar .nav>li>.dropdown-menu::after, .header .navbar .nav>li>.dropdown-menu::before {
    display: none; 
}
.dropdown-menu>li>a {
    color: #3b3b3c; 
}
.header .navbar .nav li .dropdown-menu>li>a {
    padding: 7px 10px; font-size: 11px; line-height: 16px; border-right: none; text-align: left; white-space: normal; 
}
.header .dropdown-menu {
    -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; 
}
.header .navbar .nav li .dropdown-menu>li:last-child a {
    border-bottom: none; 
}
.dropdown-menu {
    width: 215px; 
}
.header .navbar .nav>li>a {
    position: relative; 
    background: transparent; 
    z-index: 9; 
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: #30313C;
    text-shadow: none;
    font-weight: bold;
    padding: 35px 10px;
}
.header .navbar .nav>li.headerlogin>a {
   padding: 10px 15px 10px;
}
.dropdown-submenu>.dropdown-menu {
    border: none; padding: 0; margin: 0; 
}
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a {
    border: none; background: rgba(0, 0, 0, 0.1); 
}
.header .navbar .nav li.dropdown.open>.dropdown-toggle, .header .navbar .nav li.dropdown.active>.dropdown-toggle, .header .navbar .nav li.dropdown.open.active>.dropdown-toggle, .header .navbar .nav li.active>.dropdown-toggle{ color: #2d2d2d; background-color: #ffffff;text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);font-weight: 600; border-color: #eeeeee; box-shadow: none; 
}
 [data-toggle="dropdown"] {

 display: none;
}
.header .navbar .nav li.dropdown .megaMenuSection li a.active {
    color: #254290;
}
.dropdown-menu {
    border-radius: 0; background: #006bb6; 
}
.header .navbar .nav li.dropdown li a {
    padding: 3px 20px; border: none; margin-bottom: 0px; color: #3b3b3c; line-height: 1.42857; font-size: 14px; font-weight: 700; 
}
.header .navbar .nav li.dropdown .megaMenuSection .heading {
    max-width: 215px; margin: 0; top: 50%; transform: translateY(-50%); position: absolute; 
}
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading {
    text-transform: uppercase; font-weight: 500; width: 100%; max-width: 308px; text-align: right; 
}
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading p.TitleText {
    color: #fff;border: 0;text-shadow: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText {
    line-height: normal; color: #fff; line-height: normal; font-size: 38px; font-weight: 500; margin: 0; text-transform: capitalize; border:0; box-shadow: none; 
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe {
    width: 100%;padding: 0;border-radius: 5px;padding-right: 110px;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input {
    float: left;background: #DDD8D3 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFmSURBVHgBjVI7TsNAEJ3ZJYoRjTtERbjBigQBnXMDSjrgBk46qsAFgnMCUkLJCeIuIH7mBqYBSlcoKNkdZjc/yzESI+1vdt6bL8JMKipUUsorAlII4CNgQkS90Uu3DwVBu3n11glfVz5n0h89d8/yCumpsAZCxFMG6gHSuRbUEwYfCCFg1oO1rUOcfA7jhSev3r7m87SM0WuEAZAYEEC2rs1OlkSZ1QtegbPQ5rIY1+gpYnaMbY7fUgZzvQXVnEESpVAiRObNGRL5C5B1bS++Cv0ykEDctqdBzJY6Lq3zJERYBNgiMemRM9Q6WYaHepoLQqe62w5zgACkGLgQge7y4U/71Ghf8E9nkQeHbJPPv40wzfFj5LxJu00+hjH34p2viml4GsAjYiDCDQNSfiskPK5s7t9Ovu4zLOZR2QuVPTfGkM77whPT56B4aiDl1jRXQH9Jtd565aJZwlT8F/SjqckFSWyCv0wrhb9anqj3AAAAAElFTkSuQmCC');border: 0;color: #33383A;background-position: left 20px center;width: calc(100% - 140px);background-repeat: no-repeat;font-size: 15px;height: auto;display: inline-block;margin: 0;height: 50px;box-shadow: none;outline: none;padding: 0 15px 0 50px;font-weight: 300;font-style: italic;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-webkit-input-placeholder {
    color: #33383A; 
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-moz-placeholder {
    color: #33383A;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-ms-input-placeholder {
    color: #33383A; 
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-moz-placeholder {
    color: #33383A;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a {
    float: right;
    color: #fff;
    background: #0b2b47;
    border: 2px solid #0b2b47;
    font-size: 14px;
    font-weight: 900;
    height: 50px;
    min-width: auto;
    text-transform: uppercase;
    line-height: 46px;
    margin: 0;
    box-shadow: none;
    text-shadow: none;
    padding: 0 25px;
    display: inline-block;
    width: auto;
    border-radius: 50px;
    letter-spacing: 0.1em;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a:hover {
    background: #00529b;
    color: #fff;
    border: 2px solid #00529b;
    text-decoration: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn {
    color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; margin-top: 20px; display: inline-block; width: auto; 
}
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn:hover {
    background: #fff; color: #2d3e55; border-color: #fff; 
}
.header .navbar .nav li.dropdown .megaMenuSection li a {
    color: #33383A;
    text-decoration: none;
    text-align: left;
    font-weight: bold;
    font-size: 16px;
    text-transform: capitalize;
    display: block;
    padding: 8px 0;
}
.header .navbar .nav li.dropdown .megaMenuSection li a:hover,
.header .navbar .nav li.dropdown .megaMenuSection li a:focus {
    text-decoration: none;
    color: #00529b;
}
.header .navbar .nav li.dropdown .megaMenuSection li .subMenu {
    padding-left: 20px; 
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection .HeaderText {
    color: #fff;font-size: 30px;font-weight: 600;display: block;width: 100%;
}
.header .navbar .nav li.dropdown .megaMenuSection h2 {
    font-weight: 700;color: #254290;font-size: 22px;line-height: normal;vertical-align: middle;margin: 0 0 10px;
}
header .navbar .nav li form p {
    font-size: 14px;color: #ffffff;text-transform: uppercase;width: 100%;font-weight: 700;margin: 0 0 2px;
}
header .navbar .nav li form .pwd-input,
header .navbar .nav li form .login-input {
    border: 1px solid #aeaeae;height: 33px;flex: 0 0 calc(50% - 50px);max-width: calc(50% - 50px);margin: 0;background-color: transparent;border-radius: 0;border-color: #ffffff;color: #ffffff;box-shadow: none;
}
font .navbar .nav li.dropdown .megaMenuSection .HeaderText a.seeAll {
    position: absolute; top: 10px; right: 0; font-size: 12px; font-weight: 700; color: #4d4d4d; 
}
font .navbar .nav li.dropdown .megaMenuSection .BodyText {
      font-size: 13px; text-transform: uppercase; font-weight: 700; color: #fff; margin-top: 20px; 
    }
.searchBtnFn .default {
    display: block; 
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection {
    display: inherit;width: 370px;margin: 0 !important;padding: 0;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
    display: inherit;
    width: 100%;
    margin: 0;
    position: relative;
    display: grid;
    align-items: center;
    height: 90px;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone {
    width: 20%;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo {
    display: inherit;width: 49%;text-align: center; margin-top: 60px;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree {
    display: inherit;width: 25%;
}
.header .navbar .nav li.dropdown .memberSection li.member-boxtwo a.SANButton {
    margin: 20px 15px 0 15px;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection.member-boxthree .HeaderText {
    position: relative;
}
.header .navbar .nav li.dropdown .memberSection .megaMenuSection.member-boxthree .HeaderText::before {
    content: '';position: absolute;background: #fff;width: 197px;height: 1px;bottom: -8px;left: 0;
}
.header .navbar .nav li.dropdown .memberSection li.member-boxthree a.SANButton {
    width: auto;
}
.social-mobile {
    display: none; 
}
.header .navbar .memberSection .member-boxthree .DiamondBullets ul li {
    display: block;padding-left: 35px;margin-bottom: 10px;
    }
.header .navbar .nav li form a {
    padding: 0;color: #ffffff;
}
header .navbar .nav li form a.KBAButton:hover {
    background: #ffffff;border-color: #ffffff;
}
.header .navbar .memberSection .member-boxthree .DiamondBullets ul li a {
    padding: 0;
}
.header .navbar .memberSection .member-boxthree .DiamondBullets ul li a:before {
    top:4px;
}
.header .navbar .memberSection .member-boxthree .DiamondBullets ul li a:hover {
    color: #0BBA97;
}
header .navbar .nav li form {
    display: flex;flex-wrap: wrap;justify-content: space-between;align-items: center;margin: 0;
}
header .navbar .nav li form a.KBAButton {
    background: #472103;padding: 9px 10px;color: #ffffff;height: auto;width: 80px;text-align: center;font-size: 14px;
}
header .navbar .nav li.headerlogin {
    background: #e9b11d;
    width: 235px;
    max-width: 235px;
    padding: 0;
    min-height: 90px;
    display: grid;
    align-items: center;
}
header .navbar .nav li.headerlogin.no-background{
    background: #FFFFFF;
}
header .navbar .nav li.headerlogin.no-background a{
    background: #e9b11d;
}
header .navbar .nav li form input.login-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAVCAYAAABLy77vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEbSURBVHgBpZQBEcIwDEVTDgGTUAdMAhImAQk4AAdIYA44FAwHDAUFBeCgJPDLhW7rVvh3vdyW9PWTlBlKyHtfcCjx2BpjHpQjBlheje9qD/hkyB0bJZ4j6HkSjIscNtR6Aw4Iue0YZIlCl3AbnH65mkW1S8RjH4ibfeXQ8hKITYGCUtMJueE+sd0VrDcDeauangQVamKbntwhXAMak3L1ciYT4rVTBzhxRlMEmPNdNaMQjH6vizlWcLSWvHrXoNbGkE108ipx4CGqrUJCT0JOL0ecW7ivseceEuvJk+hCHfaWciEt3l8oXy2iFdACD1fK102Dij9An7/LnN4/SRos0zhRnqqPCf/9nflFtZBMQPv3hbOUJ/mOvxr+BDf719dvV9PeAAAAAElFTkSuQmCC');background-repeat: no-repeat;background-position: center left 8px;padding-left: 35px;
}
header .navbar .nav li form input.pwd-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBnZIBEYMwDEXTKaiESaiDgYM5KA42B+BgErgpQMIkgIPhgDnIwvi79VivDfy7f21peE3aECnEzFZci5/800NchXFGAXIydGIrvosHzOfvXjyKS2PMmAMdkU0/Z5faJ0VWLYJt5kBelxwLzActcd18h6kAB5ijPKwSTwfKayKdbAo2Yiwpr5N4SMEuGF+JmM8DyFDQ0jbRgFpz+WjmHi9+VINkfcZFO/gKyGaQ539N6MM8CKf7ANSgrAJ78UaOZYQyvmpII01GpNX6B5nfNme0glXB2u8CBbCWl2evd4MCGO8uLdAb5j6QM8wR6hAAAAAASUVORK5CYII=');background-repeat: no-repeat;background-position: center left 8px;padding-left: 35px;
}
.header-drop-title {
    display: inline-grid;
    vertical-align: top;
    padding-right: 40px;
    text-align: center;
}
.mainMenuMob {
    display: table-cell;
    vertical-align: top;
    text-align: center;
}
.mainMenuMob .mainMenuMob-col {
    display: inline-block;
    min-width: 20%;
    padding: 0 15px;
}
a.ArrowButton, .ArrowButton {
    background: transparent;color: #1B365D;border: 0;padding: 15px 30px;border-radius: 30px;font-size: 14px;text-transform: uppercase;font-weight: 700;text-decoration: none; line-height: inherit;
}
a.ArrowButton:hover, .ArrowButton:hover,
a.ArrowButton:focus, .ArrowButton:focus {
    background:#F1B828;color: #fff;
}
.mainMenuOnclickBtn {
    cursor: pointer; 
}
/*-------Slider-----***/
.slider {
    position: relative; min-height: 400px; 
}
.slider .owl-carousel .item {
    background-repeat: no-repeat;background-size: cover;background-position: center;min-height: 400px;
}
.slider .owl-carousel .item:before {
    position: absolute;
    width: 100%;
    content: "";
    height: 100%;
    z-index: 0;    
    opacity: 0.70;
    left: auto;
    right: 0;
    /* background: rgb(0,82,155);
    background: -moz-linear-gradient(left, rgba(0,82,155,1) 0%, rgba(1,81,153,1) 37%, rgba(2,74,137,1) 45%, rgba(10,51,87,1) 66%, rgba(12,44,72,1) 75%, rgba(12,44,72,1) 100%);
    background: -webkit-linear-gradient(left, rgba(0,82,155,1) 0%,rgba(1,81,153,1) 37%,rgba(2,74,137,1) 45%,rgba(10,51,87,1) 66%,rgba(12,44,72,1) 75%,rgba(12,44,72,1) 100%);
    background: linear-gradient(to right, rgba(0,82,155,1) 0%,rgba(1,81,153,1) 37%,rgba(2,74,137,1) 45%,rgba(10,51,87,1) 66%,rgba(12,44,72,1) 75%,rgba(12,44,72,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00529b', endColorstr='#0c2c48',GradientType=1 ); */
}
.slider .owl-carousel .item .carousel-caption {
    height: 670px;
}
.captionFrame ul li:nth-child(1):before {
    background: #0b2b47;
    content: "";
    display: block;
    position: absolute;
    height: 100%;
    width: 50vw;
    z-index: -1;
    left: auto;
    right: 0;
}

.slider .owl-carousel .captionFrame ul li:nth-child(1) img {
    object-fit: cover;
    opacity: 0.2;
}
.slider .owl-carousel .owl-dots, .slider .owl-carousel .owl-nav {
    position: absolute;
    left: 0;
    margin: 0;
    bottom: auto;
    width: 460px !important;
    left: 0;
    display: inline-flex !important;
    top: auto;
    bottom: 110px;
    justify-content: center;
    margin: 0;
}
.slider .owl-carousel .owl-nav .owl-prev {
	 opacity: 0 !important;
    pointer-events: none !important;
}

.slider .owl-carousel .owl-nav {
    justify-content: center;
}
.slider .owl-carousel .owl-nav button.owl-next:before {
    content: "Next";
}
.slider .owl-carousel .owl-nav button.owl-prev:after {
    content: "Prev";
}
.owl-theme .owl-dots .owl-dot {
    outline: none;
    background: transparent;
    margin: 5px 0;
}
.owl-theme .owl-dots .owl-dot span {
    background: #dcdcdc;
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
    background: #ffffff;
}
.carousel-caption {
    max-width: 1920px;
    left: 0;
    right: 0;
    padding: 0;
    margin: 0 auto;
    top: 0;
    bottom: auto;
    color: #fff;
    background: transparent;
    height: 100%;
    z-index: 1;
    position: relative;
}
.captionFrame {
    margin-left: 0;
    max-width: 570px;
    padding: 20px 40px 120px 40px;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
}
.captionFrame ul li:nth-child(2) {
    font-size: 65px;
    color: #fff;
    font-weight: bold;
    line-height: 1.1;
    margin-bottom: 15px;
    border: 0;
    text-shadow: none;
    text-align: left;
}
.captionFrame ul li:nth-child(3) {
    font-size: 20px;
    line-height: 1.5;
    margin-bottom: 30px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    width: 100%;
}
.captionFrame ul li:nth-child(1) {position: absolute;width: 100%;height: 100%;z-index: -1;overflow: hidden;top: 0;left: 0;bottom: 0;right: 0;}
.captionBtnBox {
    position: absolute;
    right: 0;
    width: 100%;
    max-width: 1920px;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0 auto;
    right: 0;
    text-align: right;
}
.captionBtnBox:before {
     position: absolute;
    width: 100%;
    content: "";
    height: 100%;
    z-index: 0;    
    opacity: 0.70;
    left: auto;
    right: 0;
    background: rgb(0,82,155);
    background: -moz-linear-gradient(left, rgba(0,82,155,1) 0%, rgba(1,81,153,1) 37%, rgba(2,74,137,1) 45%, rgba(10,51,87,1) 66%, rgba(12,44,72,1) 75%, rgba(12,44,72,1) 100%);
    background: -webkit-linear-gradient(left, rgba(0,82,155,1) 0%,rgba(1,81,153,1) 37%,rgba(2,74,137,1) 45%,rgba(10,51,87,1) 66%,rgba(12,44,72,1) 75%,rgba(12,44,72,1) 100%);
    background: linear-gradient(to right, rgba(0,82,155,1) 0%,rgba(1,81,153,1) 37%,rgba(2,74,137,1) 45%,rgba(10,51,87,1) 66%,rgba(12,44,72,1) 75%,rgba(12,44,72,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00529b', endColorstr='#0c2c48',GradientType=1 );
}
.captionBtnBox .captionBtnFrame ul {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    margin: auto;
    width: 215px;
}
.captionBtnFrame {
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 1;
    flex: 0 0 25%;
    max-width: 25%;
}
.captionBtnBox ul li {
    width: 100%;
    list-style: n;
    overflow: hidden;
    position: relative;
    list-style: none;
    margin: 15px 0;
}
.captionBtnBox ul li:before {
    display: none;
}
.captionBtnBox ul li:last-child {
    margin-bottom: 0px; 
}
.captionBtnBox ul li a {
    padding: 15px 10px 15px 10px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    width: 100%;
    background: rgba(255, 255, 255, 0.0);
    border: 8px solid #ffffff;
    border-style: none none solid;
    min-height: 98px;
}
.captionBtnBox ul li a:hover {
    color: #ffffff;
    text-decoration: none;
}
.captionBtnBox ul li a .iconBox {
    width: 100%;
    float: left;
    margin: 0px 0px;
    text-align: center;
}
.captionBtnBox ul li a .iconBox img {
    margin: 0 auto;
    padding-top: 2px;
    filter: contrast(0)brightness(100);
    width: 63px;
    height: 63px;
    object-fit: contain;
}
.captionBtnBox ul li a .iconBox svg path {
    fill: #ffffff;
}
.captionBtnBox ul li a .iconBox img.default {
    display: block; 
}
.captionBtnBox ul li a .textBox {
    position: relative;
    left: 0;
    overflow: hidden;
    text-align: center;
    margin-top: 15px;
    color: #e9b11d;
}
.captionBtnBox ul li a .textBox h2 {
    margin: 0;
    padding: 0;
    color: #e9b11d;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
}
.captionBtnBox ul li a .arrow {
    float: right;padding: 19px 0;position: absolute;right: 30px;top: 50%;transform: translateY(-50%);color: #ffffff;font-size: 20px; 
}
.captionBtnBox ul li a:hover .iconBox svg path {
    fill: #254290;;
}
.captionBtnBox.captionBtnBox-mb {
    display: none; 
}/*-------FriendsLogoBox Css----------*/
.section-HeaderText {
    color: #08173A;font-size: 30px;font-weight: 700;margin-bottom: 60px;
}
.BlackLine, .WhiteLine, .GreenLine {
    position: relative; 
}
.BlackLine:before, .WhiteLine:before {
    content: '';position: absolute;bottom: -15px;left:0;right:0;margin:0 auto;width: 200px;height: 3px;background: #0C1F4F;
}
.WhiteLine:before {
    background: #ffffff; 
}
.friendsSliderBox .HeaderText {
    background: #fff;padding: 8px 14px;display: inline-block;font-size: 14px;text-transform: uppercase;color: #6C6C6C;margin-bottom: 40px;
}
.sponsors-sec .friendsSliderBox ul>li a img {
    width: auto;
    margin: 0 auto;
    display: inline-block;
    max-width: 100%;
    object-fit: contain;
}
.friendsSliderBox ul li {
    display: inline-block;
    vertical-align: middle;
    min-width: 19%;
    text-align: center;
    padding: 15px 45px;
}
.friendsSliderBox .owl-carousel ul li a {
    display: block;
    text-align: center;
}
.inner-pg .friendsSliderBox ul li:nth-child(4), .inner-pg .friendsSliderBox ul li:nth-child(5) {
    width: 35%;
}
.owl-carousel .owl-nav button.owl-prev span, .owl-carousel .owl-nav button.owl-next span {
    display: none; 
}
.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-prev:hover {
    position: absolute;
    top:50%;
    margin: -14px 0 0 -100px;
    left: 50%;
    background: url(../images/prev.png) no-repeat;
    width: auto;
    height: auto;
    z-index: 9;
    background-size: contain;
    background-position: center left;
    transform: translate(-50%, -50%);
    padding-left: 35px !important;
    color: #ffffff;
    line-height: 1;
}
.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-next:hover {
    position: absolute;
    top:50%;
    margin: -14px 0 0 100px;
    left: 50%;
    background: url(../images/next.png) no-repeat;
    width: auto;
    height: auto;
    z-index: 9;
    background-size: contain;
    background-position: center right;
    transform: translate(-50%, -50%);
    padding-right: 35px !important;
    color: #ffffff;
    line-height: 1;
}
.friendsSliderBox.friendsSliderBox-mobile {
    display: none;
}/*--------Become A Member---------***/
.member-boxleft {
    display: inline-block;vertical-align: middle;width: 67%;
}
.member-boxright {
    display: inline-block;vertical-align: top;padding-left: 18px;margin-top: 70px;
}
.member-right {
    border-left: 1px solid #0C1F4F;padding-left: 50px;
}
.member-boxleft h3, .member-boxright h3 {
    margin: 0 0 15px 0;
}
/**--------Footer---------***/
.footer-info p {
    font-size: 16px;
    line-height: 25px;
    margin: 25px 0 0;
    color: #ffffff;
}
ul.follow-us li {
    display: inline-block; vertical-align: middle; font-size: 19px; font-weight: 500;
text-transform: uppercase; margin:0 4px; 
}
ul.follow-us li:first-child {
    margin-right: 20px; 
}
ul.follow-us li a {
    display: block;width: 40px; height: 40px; border-radius: 50%;border:2px solid transparent; line-height: 40px; color: #0BBA97; font-size: 20px; text-align: center;
}
ul.follow-us li a:hover, ul.follow-us li a:focus {
    border-color: #BA0C2F; color: #BA0C2F;
}
.contact-links ul li {
    margin-bottom: 18px; 
}
.contact-links ul li i {
    color: #BA0C2F;font-size: 20px;width: 30px;
}
.contact-links ul li span {
    display: inline-block; vertical-align: top; width: calc(100% - 50px);
}
.copyright {
    text-align: center;
}
.copyright p, 
.copyright p a {
    font-size: 14px;
    color: #ffffff;
    margin: 0;
    opacity: 0.5;
}
.copyright p a {
    color: #619EED; 
}
.copyright p a:first-child {
    margin-left: 15px; 
}
.copyright p>span {
    margin: 0 10px;
}
img.footlogo {
    filter: brightness(100);
}
.col1.footer-info {
    position: relative;
}
.col1.footer-info:before {
    /* position: absolute; */
    /* content: ""; */
    width: 50vw;
    height: 100%;
    display: inline-block;
    /* background: #254290; */
    left: auto;
    right: 0;
    top: 0;
    z-index: -1;
}
.footer-links h3, .contact-links h3 {
    font-size: 24px;
    font-weight: bold;
    color: #e9b11d;
    margin: 0 0 20px 0;
    line-height: 1.3;
    font-style: normal;
}
.footer-links ul li {
    position: relative;padding-left: 0; margin-bottom: 5px;
}
.footer-links ul li a, .contact-links ul li a, .contact-links ul li span {
    font-size: 18px;
    color: #ffffff;
    line-height: 1.5;
    letter-spacing: 0.025em;
}
.footer-links ul li a:hover, .footer-links ul li a:focus,
.contact-links ul li a:hover, .contact-links ul li a:focus {
    text-decoration: underline;
}
.d-flex-wrap {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.footer {
    background: #00498b;
    position: relative;
    z-index: 2;
}
#redTemplate .bannerInner{background:#b5121b;}
#redTemplate .breadcrumd-list ul,#redTemplate .breadcrumd-list ul:before{background:#777777;}
#redTemplate header .navbar .nav li.headerlogin.no-background a:first-child{background:#b5121b;}
#redTemplate header .navbar .nav li.headerlogin.no-background a:last-child{background:#777777;}
#redTemplate header .navbar .nav li.headerlogin{background:#b5121b;}
#redTemplate .header .navbar .nav>li>a, #redTemplate .header .navbar .nav li.dropdown .megaMenuSection li a{color:#b5121b;}
#redTemplate a.member-center-btn{color:#FFF!important;}
#redTemplate .DiamondBullets ul li a, #redTemplate .eventbox-list .event-box h3,#redTemplate .eventbox-list .event-box .e-date{color:#b5121b;}
#redTemplate .DiamondBullets ul li a:after{
    background: url(../images/Arrow-red.png);
    background-position: right center;
    background-repeat: no-repeat;}
#redTemplate .ColumnHeader,#redTemplate .HeaderText,#redTemplate .HeaderTextSmall,#redTemplate .SubHeading,#redTemplate h1,#redTemplate h2,#redTemplate h3,#redTemplate h4,#redTemplate h5,#redTemplate h6,#redTemplate h1,#redTemplate h3,#redTemplate h4,#redTemplate .TitleText{color:#b5121b}
#redTemplate .SectionHeader,#redTemplate .SectionHeader:after{color:#b5121b;}
#redTemplate .side-title-center .ColumnHeader,#redTemplate .banner-content .TitleText{color:#FFF;}
#redTemplate .Highlight{background:#b5121b;border-left: 22px solid #94070e;color:#FFF;}
#redTemplate .KBAButton:first-child{background:#777777;}
#redTemplate .KBAButton:last-child{background:#b5121b;}
#redTemplate .KBAButton {color:#FFF;}
#redTemplate .header .navbar .nav>li.dropdown>a:after{border: 1px solid #b92143;border-style: none solid solid none;}
#redTemplate .footer-links h3,#redTemplate .contact-links h3{color:#e9b11d;}
.footer .row.d-flex-wrap>div {
    padding-left: 15px;padding-right: 15px;
}
.footer .row.d-flex-wrap>div.col1{-webkit-flex: 0 0 25%;flex: 0 0 25%;max-width: 25%;}
.footer .row.d-flex-wrap>div.col2 {
    margin-left: 0;
}
.footer .row.d-flex-wrap>div.col2, .footer .row.d-flex-wrap>div.col3, .footer .row.d-flex-wrap>div.col4, .footer .row.d-flex-wrap>div.col5 {
    -webkit-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
}
.footer .row.d-flex-wrap>div.col6 {
    -webkit-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}
.footer .row.d-flex-wrap>div.col7 {
    -webkit-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
    text-align: right;
    align-self: center;
}
.footer .row.d-flex-wrap>div.footer-info h3 {
    font-size: 25px;color:#DDDDDD;margin: 0 0 15px;
}
.footer-line {
    border-color: rgb(255 255 255 / 25%);
    margin: 0;
}
.footer .footer-links, .footer .footer-info {
    padding: 50px 0 45px;
}
.footer .row.d-flex-wrap>div.col7 ul.social-list {
    justify-content: end;
    margin: 0 -4px;
}
.footer-links ul.social-list {
    display: flex;flex-wrap: wrap;
}
.footer-links ul.social-list li {
    margin: 0 4px;
}
.footer-links ul.social-list a {
    font-size: 18px;
    color: #ffffff;
    background: #153551;
    width: 36px;
    line-height: 36px;
    height: 36px;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
}

.footer:before {
    background: #153551;
}
.footer:before, .footer:after {
    content: "";
    height: 145px;
    width: 100%;
    display: block;
    top: -15px;
    position: absolute;
    transform: translateY(-100%);
    z-index: 2;
    clip-path: polygon(0 0, 100% 70%, 100% 100%, 0% 100%);
}
.footer:after {
  background: #00498b;
  z-index: 2;
  top: 5px;
 }
.copyright-block {
    background: #003b70;
    padding: 15px 0;
}
.footer-links ul li p {
    /* text-transform: uppercase; */
    color: #ffffff;
    font-weight: 400;
    font-size: 15px;
}
.footer .containerCustom{margin-top:90px!important;}
/*-----------------------------Inner Page CSS----------------------------***/
/*--- Banner Inner --**/
.bannerInner {
    position: relative;
    background: #0b2b47;
    min-height: 250px;
}
.bannerInner img {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.1;
}
.banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 1;
    color: #fff;
    width: 100%;    
    padding-bottom: 44px;
}
/********************/
.bannerInner .breadcrumd-list {
    position: absolute;
    bottom: 0;
    left: 0;
    top: auto;
    width: 100%;
}
.inner-page-content {
    position: relative;
    padding-bottom: 130px;
}
.inner-page-content>div>.row-fluid {
    display: flex;
    flex-direction: row-reverse;
}
.inner-page-content>div>.row-fluid:before, .inner-page-content>div>.row-fluid:after {
    display: none;
}
.inner-page-content .inner-content-area {
    padding: 40px 0px 70px 30px;
    max-width: 100%;
}
.inner-page-content .inner-content-area.smallScreen {
    padding: 40px 0px 70px 30px;
    width: calc(100% - 435px);
    max-width: 100%;
}
.inner-page-content .sidebar {
    width: 435px;
    padding: 40px 25px 50px 0px;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    bottom: 0;
}
.side-title-center .ColumnHeader {
    color: #ffffff;
    margin-top: 10px;
}
.quicklink-mobile {
    display: none; 
}
.event-list ul,.DiamondBullets ul {
    margin: 0px;padding: 0;list-style: none;
}
.DiamondBullets ul li {
    position: relative;padding-left: 0;margin-bottom: 8px;
}
.DiamondBullets ul li a {
    font-size: 16px;
    color: #00529b;
    display: block;
    padding: 15px 20px;
    border: 1px solid #ffffff;
    font-weight: bold;
    letter-spacing: 0.15em;
    text-transform: uppercase;
}
.DiamondBullets ul li a:after {
    content: "";
    background: url(../images/Arrow-blue.png);
    display: inline-block;
    background-repeat: no-repeat;
    height: 20px;
    background-position: right center;
    width: 30px;
    position: absolute;
    top: 50%;
    right: 15px;
    line-height: 1;
    transform: translateY(-50%);
    font-size: 30px;
}
.DiamondBullets ul li a:hover {
    text-decoration: underline;
}
.DiamondBullets ul li a:hover:before {
    background-position: 0 -18px;
}
.events {
}
.sponsors-box {
    background: #CFD2D9;text-align: center;padding: 23px 20px;
}
.sponsors-box span {
    background: #fff;padding: 4px 12px;color: #6C6C6C;font-size: 14px;font-weight: 600;text-transform: uppercase;position: relative;margin-top: 28px;display: inline-block;
}
.sponsors-box span small {
    font-size: 24px;color: #000;font-weight: 500;position: absolute;bottom: -22px;left: 0;right: 0;margin: 0 auto;
}
.sponsors-boxtwo {
    display: block;text-align: center;margin: 50px 0 30px 0;padding: 0 25px;
}
.sponsors-boxthree {
    background: #E9E9E9;display: block;text-align: center;padding: 38px 20px;margin:0 25px;
}
.events .friendsLogoBox {
    display: none;
}
/*Left content*/
.inner-content-area > p {
    margin: 15px 0 20px 0;
}
.content-info p {
    margin: 20px 0;
}
.Highlight {
    background: #00519b;
    padding: 40px;
    margin: 30px 0 40px;
    border-left: 22px solid #004b8e;
}
.Highlight h3 {
    margin:0 0 10px 0;
}
.Highlight p {
    margin-bottom: 40px;
    color: #ffffff;
}
.BulletList ul {
    margin-bottom: 50px;margin-left: 0;list-style: none;
}
.BulletList ul li {
    padding-left: 30px;
    margin-bottom: 15px;
    font-size: 18px;
    line-height: 1.5;
    position: relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFiSURBVHgBnVNBTgJBEOzuWRKP+4T9gYsKiTf4gb5AORnCQXgCL9ALIZ7AF8gPxJMJiswT9gfu0QjT7cxGCNmdXYx12GTSXdU1tT0IHqwmcbj5gpgAIndmgKTR1XNfL+aJvKFbYezbY5jrTRFh9m3M8Lyrk4LAYhTHFKgn+J1agcQOuWz23vRO4HUcRzVSK8/UMqRrNnXnhNzJkp/zZBSYusYSgbCm1CTrW45ProVw4puguBZSwAXxLWxWbRKFV94J1pWhdWrv2y51gtInEIjBj+iQCCEcE1QHF7m7usTFyDBfFFvH94fTz3IRSdbMbvo26CjXkJL9kfqfZGdBk7X24qMfJFuwyCMFR3wPxYDSQOiiiuwcNrsfU6p3tCN3ctUQFd5B5VrjwH2zTTy7Wc7sUgzhj2CmjuNkMvuF7E2gXVGElpcpMGdjBs2e3gWPvr7FqBEjmhYSRhmPJRFR8+0L3McPboCcJBIS7s8AAAAASUVORK5CYII=);
    background-position: left center;
    background-size: contain;
    background-repeat: no-repeat;
    color: #33383A;
    background-size: 17px;
    background-position: top 6px left;
}
.BulletList ul li::before {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 14px;
    height: 14px;
    border: 2px solid #CC9933;
    border-radius: 50%;
    background: #CC9933;
    opacity: 0;
}

/* sbm Clases */
.sponsors-img-list ul {
    list-style: none;display: flex;flex-wrap: wrap;align-items: center;justify-content: center;padding: 0;margin: 0;
}
.sponsors-img-list ul li {
    padding: 15px;
}
.infoicon-sec {
    width: 100%;
}
.flex-row {
    display: flex;flex-wrap: wrap;
}
.flex-row:before,.flex-row:after {
    display: none;margin: 0;
}
.infoicon-sec .flex-row .col-3 {
    flex: 0 0 25%;-webkit-flex: 0 0 25%;max-width: 25%;padding: 0 12px;
}
.pt-0 {
    padding-top: 0px !important;
}
.pb-0 {
    padding-bottom: 0px !important;
}
.recent-blog-sec .flex-row {
    justify-content: space-between;margin-left: -15px;margin-right: -15px;
}
.img-card .img-holder {
    position: relative;
}
.img-card .img-holder img {
    width: 100%;
}
.img-card .img-holder span {
    font-size: 35px;color: #ffffff;display: inline-block;background: #254290;padding: 12px 10px;position: absolute;width: 95px;bottom: 0;left: 0;line-height: 1;text-align: center;
}
.img-card .img-holder span small {
    display: block;font-size: 22px;
}
.img-card h2 {
    font-size: 28px;
    line-height: 1.22;
    margin: 0px 0 5px;
    color: #00529b;
    font-weight: 800;
    font-family: 'proxima-nova';
}
.GoldUnderLine {
    font-size: 18px;
    display: block;
    color: #9ea2a6;
    font-weight: 700;
    margin-top: 5px;
    font-family: 'proxima-nova';
    text-transform: uppercase;
}
.img-card .img-card-content p {
    color: #0c2c48;
    margin-bottom: 15px;
}

.GoldUnderLine:after {
    display: block;
    height: 4px;
    background: #e9b11d;
    content: "";
    width: 50px;
    margin: 10px 0 12px;
}
.reg-here-link {
    font-size: 12px;
    font-weight: 700;
    color: #254290;
    text-transform: uppercase;
    position: relative;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFiSURBVHgBnVNBTgJBEOzuWRKP+4T9gYsKiTf4gb5AORnCQXgCL9ALIZ7AF8gPxJMJiswT9gfu0QjT7cxGCNmdXYx12GTSXdU1tT0IHqwmcbj5gpgAIndmgKTR1XNfL+aJvKFbYezbY5jrTRFh9m3M8Lyrk4LAYhTHFKgn+J1agcQOuWz23vRO4HUcRzVSK8/UMqRrNnXnhNzJkp/zZBSYusYSgbCm1CTrW45ProVw4puguBZSwAXxLWxWbRKFV94J1pWhdWrv2y51gtInEIjBj+iQCCEcE1QHF7m7usTFyDBfFFvH94fTz3IRSdbMbvo26CjXkJL9kfqfZGdBk7X24qMfJFuwyCMFR3wPxYDSQOiiiuwcNrsfU6p3tCN3ctUQFd5B5VrjwH2zTTy7Wc7sUgzhj2CmjuNkMvuF7E2gXVGElpcpMGdjBs2e3gWPvr7FqBEjmhYSRhmPJRFR8+0L3McPboCcJBIS7s8AAAAASUVORK5CYII=');
    background-position: left center;
    background-size: contain;
    background-repeat: no-repeat;
    padding: 1px 0 1px 22px;
}
.mt-40 {
    margin-top: 40px !important;
}
.magazine-block {
    text-align: center;
}
.magazine-block h2 {
    font-size: 30px;font-weight: 700;margin: 50px 0 10px;text-transform: uppercase;
}
.newscard {
    display: block;position: relative;min-height: 225px;background: #ffffff;border: 1px solid #BCBCBC;padding-left: 95px;
}
.newscard .newstag {
    position: absolute;top: 0;left: 0;width: 95px;height: 100%;background-color: #254290;color: #ffffff;
}
.newscard .newstag>span {
    -webkit-transform: rotate(-90deg) translate(-50%, -50%);transform: rotate(-90deg) translate(-50%, -50%);-webkit-transform-origin: top left;transform-origin: top left;position: absolute;top: 50%;left: 50%;display: block;text-align: center;width: auto;font-weight: 700;
}
.newscard .news-inner-wrap {
    position: relative;left: 0;padding: 30px 30px 30px 190px;min-height: 200px;
}
.newscard .news-inner-wrap img {
    position: absolute;left: 70px;top: 65px;z-index: 1;
}
.newscard .news-inner-wrap h2 {
    font-size: 30px;font-weight: 700;text-transform: uppercase;
}
.newscard:not(:first-child) {
    margin-top: 70px;
}
.gray-bg2 {
    background: #F7F5F4;
}
.newscard .news-inner-wrap p {
    color: #33383A;
}
body .header .navbar .nav>li.searchBtnFn:hover .dropdown-menu {
    display: none !important;
}
.header .navbar .nav>li.searchBtnFn:hover .dropdown-toggle:after {
    display: none !important;    
}body .header .navbar .nav>li.searchBtnFn.dropdown.show-search-bar>ul.dropdown-menu {
    display: block !important;visibility: visible !important;opacity: 1 !important;z-index: 999999;
}body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
      top:0;
      margin: 0;
      background: #ffffff;
      max-width: 60%;
      left: auto;
      right: 235px;
      padding: 0;
      height: 90px;
      border-style: none;
    }
    .nav-member-center {
        background: #e9b11d;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        height: 90px;
        width: 215px;
        margin-left: auto;
        margin-right: 0;
    }
.searchnav-logo {
    padding: 24px 40px 14px 40px;
}
.nav-member-center p {
    color: #ffffff;margin: 10px 0 0;
}
.nav-member-center img {
    width: 35px;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.member-center-wrap {
    width: 215px;
} 
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
    display: grid;
    align-items: center;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a.searchclose {
    background: transparent !important;color: #9A8D83;padding: 0;border: none;display: inline-flex;align-items: center;position: absolute;right: 0;top: 50%;right: 30px;-webkit-transform: translateY(-50%);transform: translateY(-50%);z-index: 1;text-transform: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a.searchclose svg {
    margin-left: 8px;
}
.footer .row.d-flex-wrap>div.col1 .footstar {
    position: absolute;display: inline-block;left: auto;right: 30px;bottom: 0;top: auto;z-index: 1;display: inline-block;opacity: 0.05;width: 70%;
}
.breadcrumd-list ul:before {
    content: "";
    display: block;
    position: absolute;
    left: 10px;
    transform: translateX(-100%);
    background: #00519b;
    height: 100%;
    width: 50vw;
    z-index: -1;
}

.breadcrumd-list ul li:first-child {
    padding-left: 0;
}
.breadcrumd-list ul {
    display: inline-flex;
    flex-wrap: wrap;
    padding: 0px 10px 0px 0px;
    margin: 0;
    background: #00519b;
    position: relative;
    z-index: 2;
    box-shadow: none;
    outline: none;
    backface-visibility: hidden;
}
.breadcrumd-list ul li {
    list-style: none;
    color: #ffffff;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0.15em;
}
.breadcrumd-list ul li a {
    padding:  10px 0;
    font-size: inherit;
    font-family: inherit;
    font-weight: inherit;
    margin-right: 10px;
}
.breadcrumd-list ul li a {
    color: #FFFFFF;
}
.breadcrumd-list ul li:not(:last-child):after {
    margin: 0 0 0 10px;
    color: #ffffff;
    content: "";
    width: 23px;
    height: 16px;
    display: inline-block;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAANBAMAAAC5okgUAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAACpQTFRFAAAA////////////////////////////////////////////////////hrvKLwAAAA50Uk5TAApoYAjwMODv//4Q3y3559shAAAAOklEQVR4nGNgAAMBBjhQRjBdDeBMtmCEcHoCQriNgWEmFKzYABc9DFeQATeCrRguaI4wIAxh7AUgBgAJbQwMHmPBcAAAAABJRU5ErkJggg==);
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    background-size: contain;
    margin-top: -2px;
    margin-left: 10px;
}
.event-list .sbm-event .sbm-e-head span:first-child:after {
    display: inline-block;content: "|";color: #33383A;opacity: 0.3;position: absolute;right: 0;top: 0;
}
.event-list .sbm-event .sbm-e-head {
    display: flex;justify-content: space-between;color: #e9b11d;font-weight: 700;text-align: center;
}
.event-list .sbm-event .sbm-e-head span {
    min-width: 45%;text-align: center;position: relative;
}
.sbm-event {
    padding: 15px;border-radius: 6px;margin-bottom: 20px;
}
.view-calender {
    color: #6A1721;font-size: 14px;text-transform: uppercase;font-weight: 700;display: inline-block;
}
.event-list hr {
    border-color: #33383A;border-bottom: none;opacity: 0.3;
}
.sbm-event:hover {
    background: #ffffff;
}
.mr-10 {
    margin-right: 10px;
}
.mb-20 {
    margin-bottom: 20px;
}
.quicklink-desktop, .events, .recent-blogposts {
    background: #e1e1e1;
    margin-bottom: 10px;
}

.side-title-center .bg-fixed.texture {
    opacity: 1;
}

.quicklink-desktop .DiamondBullets {
    padding: 25px 20px;
}

.side-title-center {
    text-align: center;
    padding: 20px 20px 15px;
    border-bottom: 1px solid rgba(87, 96, 102, 0.30);
    position: relative;
    z-index: 1;
}
.sbm-event h4 {
    font-size: 19px;text-align: center;color: #33383A;
}
.owl-theme .owl-dots .owl-dot span {
    box-shadow: 0 0 0 2px #ffffff;background-color: transparent;
}
.d-inline-block {
    display: inline-block   ;
}
blockquote,blockquote.pull-right {
    padding: 45px 65px 45px 65px;   
    font-size: 18px;
    color: #00529b;
    border: 1px solid #dae3eb;
    border-style: solid none;
    font-family: "activ-grotesk";
    font-weight: 700;
    position: relative;
}
blockquote p {
    outline: none;
    box-shadow: none;
    opacity: 1;
    font-size: inherit;
    font-weight: inherit;
    font-style: inherit;
    font-family: "activ-grotesk";
    color: inherit;
}
/* blockquote:after {
    content: "";font-size: 43px;line-height: 0;position: relative;left: 5px;top: 20px;background: url('../images/icon-blockquote.png');background-repeat: no-repeat;background-size: contain;display: inline-block;width: 28px;height: 28px;
} */
blockquote:before, blockquote:after {
    position: absolute;
    width: 20px;
    height: 34px;
    display: inline-block;
    background-size: contain !important;
    z-index: 9;
    background-repeat: no-repeat !important;
}

blockquote:before {
    top: -2px;
    left: 0;
    background: #fff url('../images/bq-left.png');
}
blockquote:after {
    bottom: -2px;
    right: 0;
    background: #fff url('../images/bq-right.png');
    background-position: bottom right;
}
blockquote.pull-right:after {
    
}
blockquote.pull-right {
    border-style: solid none;
}
.PullQuote {
    border-left: 4px solid #dae3eb;
    padding-left: 40px;
}
.PullQuote:not(:last-child) {
    padding-bottom: 30px;
}
.PullQuote h4 {
    margin: 5px 0;
}
.PullQuote p {
    margin: 0;
}
.fs22 {
    font-size: 22px;
}
.BulletList-row {
    display: flex;flex-wrap: wrap;
}
.BulletList-row .BulletList {
    -webkit-flex: 0 0 50%;flex: 0 0 50%;max-width: 50%;
}
.mr-10 {
    margin-right: 10px;
}
.Highlight .btns-wrap .WhiteButton {
    margin-right: 10px;
}
.my-10 {
    margin-top: 10px;margin-bottom: 10px;
}
.GrayTheme .bannerInner  {
    background: #9A8D83;z-index: 5;
}
.GrayTheme .bannerInner::before,
.GrayTheme .bannerInner::after {
    position: absolute;left: 0;top: 0;height: 100%;content: "";background: rgba(255, 255, 255, 0.2);z-index: -1;
}
.GrayTheme .bannerInner::before {
    width: 10.5%;
}
.GrayTheme .bannerInner::after {
    width: 9%;
}
.GrayTheme .bannerInner  .breadcrumd-list ul li {
    color: #472103;
}
.home2 .slider .owl-carousel .item:before {
    background: -moz-linear-gradient(top, rgba(154,141,131,1) 0%, rgba(221,216,211,1) 45%, rgba(0,48,95,0.48) 100%); background: -webkit-linear-gradient(top, rgba(154,141,131,1) 0%,rgba(221,216,211,1) 45%,rgba(0,48,95,0.48) 100%); background: linear-gradient(to bottom, rgba(154,141,131,1) 0%,rgba(221,216,211,1) 45%,rgba(0,48,95,0.48) 100%); filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#9a8d83', endColorstr='#7a00305f',GradientType=0 ); 
}
.home3 .slider .owl-carousel .item:before {
    background: linear-gradient(180deg, #709ED1 0%, rgba(0, 48, 95, 0.48) 0.01%, rgba(2, 2, 2, 0.484767) 0.02%, #e9b11d 55.73%, #472103 100%);transform: rotate(-180deg);
}
.home2 .carousel-caption,
.home2 .captionFrame ul li:nth-child(2) {
    color: #472103;
}
.GoldTheme .bannerInner  {
    background: #CC9933;z-index: 5;
}
.GoldTheme .bannerInner::before,
.GoldTheme .bannerInner::after {
    position: absolute;left: 0;top: 0;height: 100%;content: "";background: rgba(255, 255, 255, 0.2);z-index: -1;
}
.GoldTheme .bannerInner::before {
    width: 10.5%;
}
.GoldTheme .bannerInner::after {
    width: 9%;
}
.GoldTheme .bannerInner  .breadcrumd-list ul li {
    color: #472103;
}
.home3 header .navbar .nav li.headerlogin {
    background: #254290;
}
.home3  header .navbar .nav li form a.KBAButton {
    background: #e9b11d;border-color: #e9b11d;
}
.home3 header .navbar .nav li form a.KBAButton:hover {
    color: #ffffff;
}
.home2 header .navbar .nav li.headerlogin {
    background: #472103;
}
.home2 header .navbar .nav li form a.KBAButton {
    background: #e9b11d;border-color: #e9b11d;
}
.home2 header .navbar .nav li form a.KBAButton:hover {
    color: #ffffff;
}
.textLine-sec p {
    text-align: center;color: #33383A;
}
.img-card {
}
.img-card .img-card-content {padding: 30px 0 0;}
.anouncebanner {
    display: none;background: #17432F;padding: 50px 20px;
}
.anouncebanner p {
    color: #ffffff;text-align: center;margin-bottom: 25px;font-size: 22px;
}
.anouncebanner .button-wrap {
    display: flex;justify-content: space-between;
}
.anouncebanner .button-wrap a {
    display: inline-block;padding: 10px 10px;width: 48%;color: #ffffff;border: 1px solid #ffffff;border-radius: 50px;text-align: center;
}
.header .navbar .nav li.open-droupdown a {
    font-weight: 700;color: #083372;
}
.recent-blog-sec .flex-row>div {
    margin: 0 !important;
    width: 370px;
    padding: 0 15px;
}
.recent-blog-sec .row>.span-12 {
    padding-left: 15px;width: 100%;margin-left: 0px;
}
.footer img.bg-img {
    position: absolute;width: 100%;height: 100%;z-index: -1;left: 0;top: 0;
}
.footer .for-mobile {
    position: relative;z-index: 2;padding: 30px 15px 20px;margin-top: 55px;
}
.footer .for-mobile h2 {
    color: #ffffff;font-size: 22px;margin: 0 0 15px;line-height: 1.3;
}
.footer .for-mobile  .row-flex {
    display: flex;flex-wrap: wrap;margin: 0 -15px;
}
.footer .for-mobile .row-flex .col12 h2 {
    text-align: center;
}
.footer .for-mobile .row-flex .col6 {
    flex: 0 0 50%;max-width: 50%;padding-left: 15px;padding-right: 15px;
}
.footer .for-mobile .row-flex .col12 {
    flex: 0 0 100%;max-width: 100%;margin-top: 30px;
}
.footer .for-mobile .row-flex .sbmrow {
    display: flex;flex-wrap: wrap;justify-content: space-between;width: 100%;padding: 0 15px;
}
.footer .for-mobile .row-flex .sbmrow ul {
    flex: 0 0 calc(50% - 15px);max-width: calc(50% - 15px);
}
.footer .for-mobile ul li , .footer .for-mobile ul li a {
    color: #ffffff;font-size: 15px;font-weight: 700;
}
.footer .for-mobile ul li {
    border: 1px solid rgb(255 255 255 / 30%);border-style: solid none;margin: -1px 0 0;padding: 4px 0;display: flex;align-items: center;min-height: 40px;
}
.footer .for-mobile ul li a:hover {
    color: #E1C783;text-decoration: none;
}
.footer .for-mobile ul li:before {
    content: "\f101";display: inline-block;font: normal normal normal 14px/1 FontAwesome;font-size: inherit;text-rendering: auto;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;margin-right: 10px;color: #A2D5ED;
}
.footer .for-mobile .copyright a {
    color: #ffffff;text-decoration: underline;
}
.footer .for-mobile .copyright p {
    font-weight: 900;padding: 0 015px;
}
.footer .for-mobile .copyright p>span {
    margin: 0 5px;
}
.footsocial-list {
    background: #0B2239;padding: 8px 15px;width: 100%;position: absolute;top: -55px;z-index: 1;
}
.footsocial-list ul {
    display: flex;flex-wrap: wrap;list-style: none;padding: 0;
}
.footsocial-list ul li:first-child {
    color: #ffffff;flex: 1 1 auto;font-size: 13px;font-weight: 900;
}
.footer .for-mobile .footsocial-list>ul li a {
    display: inline-block;font-size: 18px;color: #ffffff;text-decoration: none;text-align: center;
}
.footer .for-mobile .footsocial-list>ul li:before {
    display: none;
}
.footer .for-mobile .footsocial-list li {
    border-style: none;padding: 0;text-transform: uppercase;
}
.footer .for-mobile .footsocial-list li:not(:first-child) {
    margin-left: 25px;
}
.footer .for-mobile .row-flex .col6:first-child ul>li:before {
    display: none;
}
.footer .for-mobile ul li:hover:before {
    color: #E1C783;
}
.sponsors-img-list ul li {
    min-width: auto;
}
.whats-new-sec .flex-row {
    margin-left: -15px;margin-right: -15px;
}
.whats-new-sec .flex-row>div {
    margin: 0;padding: 0 15px;
}
.whats-new-sec .flex-row>div.sbm8 {
    padding-left: 60px;
}
.whats-new-sec .flex-row>div.span4 {
    flex: 0 0 35%;max-width: 35%;
}
.whats-new-sec .flex-row>div.span8 {
    flex: 0 0 65%;max-width: 65%;
}
.foot-logo:after {
    width: 126px;
}
.header .navbar .nav > li > a.member-center-btn {
    /* display: none; */
    font-size: 14px;
    letter-spacing: 0.15em;
    color: #163652;
}
    .header .navbar .nav > li > a.member-center-btn i {
        font-size: 20px;
        color: #ffffff;
        vertical-align: middle;
        margin-right: 5px;
    }

    
.loggedinBox {
    display: flex;justify-content: space-between;align-items: center;height: 100%;margin: 0 auto;max-width: 250px;padding: 5px 0;
}
.header .navbar .nav li .loggedinBox a.KBAButton {
    padding: 12px 23px;min-height: auto;color: #ffffff;background: #472103;border-color: #472103;font-size: 18px;
}
.header .navbar .nav li .loggedinBox>span p {
    color: #ffffff;margin: 8px 0 0;font-weight: 500;font-size: 20px;
}
.header .navbar .nav li .loggedinBox>span {
    display: block;text-align: center;
}
textarea, 
input[type="text"], 
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"],
.uneditable-input {
    min-height: 30px;
}
.footer .for-mobile ul, .captionFrame ul, .friendsSliderBox ul, .footer .footer-links ul, .footer .footer-info ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.event-mobile {
    display: none;
}



/* Event News */
.upcoming-news-event-sec .unes-left {
    flex: 0 0 75%;
    max-width: 75%;
    display: flex;
    background: #00519b;
    padding-left: 80px;
    border-top: 20px solid #004685;
    margin-top: -50px;
    position: relative;
    z-index: 2;
}

.upcoming-news-event-sec .container.containerCustom:before, .upcoming-news-event-sec .container.containerCustom:after {display: none;}

.upcoming-news-event-sec .container.containerCustom {
    display: flex;
    margin: 0 auto;
    flex-wrap: wrap;
    width: 100%;
    max-width: 1920px;
    padding: 0px;
}

.upcoming-news-event-sec .unes-right {
    flex: 0 0 25%;
    max-width: 25%;
}

.upcoming-news-event-sec .unes-left .all-news-event-box {
    background: #448ccb;
    flex: 0 0 40%;
    max-width: 40%;
    padding: 70px 15px 15px;
    position: relative;z-index: 1;
}
.upcoming-news-event-sec .unes-left .all-news-event-box .bg-fixed.texture{ 
    opacity: 1;
}

.upcoming-news-event-sec .unes-left .upcoming-events-box {
    flex: 0 0 60%;
    max-width: 60%;
    padding-top: 60px;
}
.upcoming-news-event-sec .unes-left .SectionHeader {
    margin-bottom: 40px;
}

.event-box {
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding-right: 30px;
    clear: both;
}

.event-box .e-date {
    width: 58px;
    height: 58px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    font-size: 16px;
    color: #ffffff;
    display: inline-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    flex-direction: column;
    float: left;
    margin: 0 15px 10px 0;
    line-height: 1;
}

.event-box .e-date small {
    font-size: 24px;
    line-height: 1;
    font-weight: 700;
}

.event-box p {color: #ffffff;}

.event-box p.event-tag {
    color: #eab11e;
    letter-spacing: 0.15em;
    margin: 0;
    font-family: 'proxima-nova';
}

.event-box h3 {
    color: #ffffff;
    margin: 0 0 10px;
    font-family: 'proxima-nova';
    font-weight: 800;
    font-size: 28px;
    line-height: 1.22;
}

.event-box:last-child {
    border-bottom: none;
}

.upcoming-news-event-sec .unes-left .all-news-event-box h2, .upcoming-news-event-sec .unes-left .all-news-event-box h2 a {
    text-align: center;
    font-size: 16px;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    margin-bottom: 55px;
    margin-top: 0;
    font-weight: bold;
}
.new-box {
    padding: 0 30px;
    margin-bottom: 30px;
}
.new-box .n-tag {
    color: #eab11e;
    letter-spacing: 0.15em;
    margin: 0;
    font-family: 'proxima-nova';
}

.new-box h3 {
    font-size: 24px;
    letter-spacing: 0.025em;
    color: #0c2c48;
    margin: 0 0 2px;
    font-family: 'proxima-nova';
    font-weight: 700;
    line-height: 1.22;
}

.new-box p {
    color: #0c2c48;
}

/* End Event News */

.adv-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 40px;
}

.adv-wrapper .adv-box {
    display: inline-block;
    margin: 25px 0;
}
.bg-fixed {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    top: 0;
    left: 0;
    object-fit: cover;
}
.bg-fixed.texture {background-size: auto;object-fit: fill;}

.adv-wrapper .bg-fixed {
    opacity: 0.1;
}
.sponsors-sec {
    position: relative;
    z-index: 2;
    padding-top: 60px;
    padding-bottom: 70px;
}
.sponsors-wrap ul {
    background: #ffffff;padding: 20px 20px 50px;border-top: 20px solid #f7f7f7;
}
.inner-pg .sponsors-wrap ul {
    max-width: 850px;margin: 0 auto;
}
.sponsors-sec .LightBlueButton {
    position: relative;
    top: -26px;
}
.mb-50 {
    margin-bottom: 50px;
}
.bg-fixed.texture {background-size: auto;object-fit: fill;object-position: top center;}

a.bloglink {
    font-weight: bold;
    color: #9da1a5;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    float: right;
    clear: left;
    margin-top: -75px;
}

.bloglink-wrap {
    display: table;
    clear: both;
    content: "";
    width: 100%;
    min-height: 1px;
}
.bloglink img {
    margin-left: 10px;
}
.my-30 {
    margin-top: 30px !important;
    margin-bottom: 30px !important;
}
.inner-content-area .img-left-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}
.inner-content-area .img-left-row:before, .inner-content-area .img-left-row:after {
    display: none;
}

.inner-content-area .img-left-row .span4 {
    flex: 0 0 330px;
    max-width: 330px;
}

.inner-content-area .img-left-row .span8 {
    flex: 0 0 calc(100% - 330px);
    max-width: calc(100% - 330px);
}
.inner-content-area .img-left-row>div {
    padding: 0 15px;
    margin: 0;
}
.events .side-title-center {
    background: #e9b11d;
}

.eventbox-list {
    padding: 25px 20px;
}

.eventbox-list .event-box {
    padding: 0;
    border-style: none;
    margin-bottom: 10px;
}

.eventbox-list .event-box h3 {
    font-size: 22px;
    color: #00529b;
    margin: 0;
}

.eventbox-list .event-box p {
    color: #0c2c48;
}

.eventbox-list .event-box .e-date {border-color: #ffffff;color: #00529b;}

.view-link {
    color: #85898e;
    text-transform: uppercase;
    letter-spacing: 0.15em;
}
.events .btn-wrap, .recent-blogposts .btn-wrap {
    padding: 0px 20px 25px;
}

.recent-blogposts .side-title-center {
    background: #438bcb;
}
.Services {
    margin: 0 -15px;
    display: flex;
    flex-wrap: wrap;
}
.Services>div {
    padding: 0 15px;
    -webkit-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    align-items: flex-start;
    margin-bottom: 30px;
}
.img-left-card {
    display: flex;
    flex-wrap: wrap;
}
.img-left-card img {
    -webkit-flex: 0 0 140px;
    flex: 0 0 140px;
    max-width: 140px;
}
.img-left-card .ilc-right {flex: 0 0 calc(100% - 140px);width: calc(100% - 140px);padding-left: 15px;}

.img-left-card  h2 {
    margin: 0;
    font-size: 22px;
    color: #00529b;
}

.img-left-card p {
    margin: 0 0 15px;
    color: #0c2c48;
}
.bg-gray-1 {
}
.friendsSliderBox.sponsors-wrap ul {
    border-color: #f7f7f7;
    background: #ffffff;
}
.btns-wrap a {
    margin-bottom: 5px;
}
.captionBtnBox-bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    position: relative;
    z-index: -2;
}
.mob-ttl {
    font-weight: 500;
}
.header .nav-collapse .nav .dropdown .dropdown-menu {
    top: 90px;
}
.bannerInner .banner-content .TitleText{
    font-family: "proxima-nova"!important;
    margin: 0!important;
}
.SectionHeader a{
    color:white;
}
#zoneToolBar form,#zoneToolBar select{margin:0;}

/*Default Red*/
.defaultRed .header .navbar .nav li.headerlogin, .defaultRed .breadcrumd-list ul, .defaultRed .breadcrumd-list ul:before{
    background: #787878 !important;
}
.defaultRed .bannerInner, .defaultRed .side-title-center,
.defaultRed .BulletList ul li::before {
    background: #b5121b !important;
}
.defaultRed .side-title-center img.bg-fixed.texture{
    visibility: hidden !important;
}
.defaultRed .header .navbar .nav > li > a.member-center-btn{
    color: #fff !important;
}
.defaultRed .DiamondBullets ul li a, .defaultRed .eventbox-list .event-box .e-date{
    color: #a72823 !important;
}
.defaultRed .eventbox-list .event-box h3{
    color: #b5121b !important;
}
.defaultRed .events.eventBlock .side-title-center, .defaultRed  .recent-blogposts.blogBlock .side-title-center{
    background: #787878 !important;
}
.defaultRed .DiamondBullets ul li a:after{
    background: url(../images/Arrow-red.png);
    background-repeat: no-repeat;
    background-position: right center;
}
/* Typography */
.defaultRed .HeaderText,.defaultRed .HeaderTextSmall, .defaultRed .SectionHeader,.defaultRed.SectionHeader:after,
.defaultRed .SectionHeader.darkblue, .defaultRed .SectionHeader.darkblue:after, .defaultRed .SubHeading,
.defaultRed h1, .defaultRed h2, .defaultRed h3, .defaultRed h4, .defaultRed h5, .defaultRed h6, .defaultRed .TitleText,
.defaultRed .ColumnHeader
{
    color: #b5121b !important;
}
.defaultRed .SectionHeader.darkblue:after{
    border-color: #b5121b !important;  
}
.defaultRed .SectionHeader.text-white:after {
    background: #787878;
}
.defaultRed .side-title-center .ColumnHeader,
.defaultRed .bannerInner .banner-content .TitleText{
    color: #fff !important;
}
.defaultRed .footer-links h3{
    color: #e9b11d !important;
} 
.defaultRed .header .navbar .nav li.headerlogin.no-background a,
.defaultRed .SectionHeader:after{
    background: #b5121b !important;
}

.defaultRed .header .navbar .nav li.headerlogin a.mClogon{
    background: #777777 !important;
}
.defaultRed .BulletList ul li {
    background: url(../images/redArrowRight.png);
    background-repeat: no-repeat;
    background-size: 19px;
    background-position: left 5px; 
}
.defaultRed .Highlight{
    background: #b5121b;
    border-left: 22px solid #777777;
}
.defaultRed .Highlight h2 {
    color: #ffffff !important;
}
.defaultRed .Highlight .KBAButton,
.defaultRed .KBAButton{
    background: #777777 !important;
    color:#fff !important;
    border: 1px solid #777777;
}
.defaultRed .Highlight .KBAButton:hover,
.defaultRed .KBAButton:hover {
    background: #b5121b !important;
    color:#fff !important;
    border: 1px solid #777777;
}
.defaultRed blockquote, .defaultRed blockquote.pull-right{
    color: #b5121b !important; 
}
.mobSearch{
    position: absolute;
    top: -11px;
    padding: 0;
    cursor: pointer;
    right: -10px;
    font: normal 18px/29px 'UniversNextPro-BoldCond';
    padding: 0px 5px 0px 8px !important;
    width: 50px !important;
    border-radius: 2px !important;
    font-size: 19px !important;
    height: 130% !important;
}
#searchbox1{
    position: relative;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe button.searchBtn {
    float: right;
    color: #fff;
    background: #0b2b47;
    border: 2px solid #0b2b47;
    font-size: 14px;
    font-weight: 900;
    height: 50px;
    min-width: auto;
    text-transform: uppercase;
    line-height: 46px;
    margin: 0;
    box-shadow: none;
    text-shadow: none;
    padding: 0 25px;
    display: inline-block;
    width: auto;
    border-radius: 50px;
    letter-spacing: 0.1em;
}
.leftAdsSliderCnt img{
    width:250px !important;
    height:244px;
    margin: 0 auto;
}
.leftAdsSliderCnt .owl-dots{
    display:none;
}
.adsBlock {
    border: 1px solid #005099;
    margin-bottom: 10px;
}
.leftAdsSliderCnt  .owl-item{
    margin-top: 10px !important;
}
.adsBlock .side-title-center{
    background: #153551;
}