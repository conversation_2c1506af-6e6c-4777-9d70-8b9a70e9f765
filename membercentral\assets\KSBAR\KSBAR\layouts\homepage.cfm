<cfset local.zoneB1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='B',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['B'],1)>
		<cfset local.zoneB1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['B'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneB2Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='B',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['B'],2)>
		<cfset local.zoneB2Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['B'][2].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneC1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='C',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['C'],1)>
		<cfset local.zoneC1Content = trim(event.getValue("mc_pageDefinition").pageZones['C'][1].data)/>
	</cfif>
</cfif>
<cfset local.zoneD1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='D',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['D'],1)>
		<cfset local.zoneD1Content = trim(event.getValue("mc_pageDefinition").pageZones['D'][1].data)/>
	</cfif>
</cfif>
<cfset local.zoneF1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='F',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['F'],1)>
		<cfset local.zoneF1Content = trim(event.getValue("mc_pageDefinition").pageZones['F'][1].data)/>
	</cfif>
</cfif>
<cfset local.zoneE1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='E',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['E'],1)>
		<cfset local.zoneE1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['E'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneE2Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='E',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['E'],2)>
		<cfset local.zoneE2Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['E'][2].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfset local.zoneG1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='G',event=event)>			
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['G'],1)>
		<cfset local.zoneG1Content = trim(event.getValue("mc_pageDefinition").pageZones['G'][1].data)/>
	</cfif>
</cfif>
<cfoutput>
<!doctype html>
<html>
	<head>
		<cfinclude template="head.cfm">
	</head>
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
	<body>
		<!-- wrapper start -->
		<div class="wrapper">
			<!--Header Start-->
			<cfinclude template="header.cfm">
			<!--Header End-->

			<!-- slider start -->
			<div class="slider">
				<div class="owl-carousel owl-theme">
					<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
					<cfset local.mainContent = event.getValue("mc_pageDefinition").pageZones['Main']>
					<cfloop from="1" to="#arrayLen(local.mainContent)#" index="local.thisItem" >
						<cfif lcase(trim(local.mainContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.mainContent[local.thisItem].data))>
						<div class="item">
							<div class="carousel-caption">
								<div class="captionFrame">
								#trim(REReplace(REReplace(local.mainContent[local.thisItem].data,"<p>",""),"</p>",""))#
								</div>
							</div>
						</div>
						</cfif>
					</cfloop>
					</cfif>
				</div>
				<div class="captionBtnBox">
					#local.zoneB1Content#
					<span class="captionQuickLinkWrapper hide">#local.zoneB2Content#</span>
					<span class="captionQuickLinkHolder"></span>
				</div>
			</div>
			<!-- slider End -->      
			
			<span class="eventHomeWrapper hide">#local.zoneC1Content#</span>
			<span class="newsHomeWrapper hide">#local.zoneD1Content#</span>
			<span class="blogHomeWrapper hide">#local.zoneF1Content#</span>
			<cfif len(trim(local.zoneC1Content)) OR len(trim(local.zoneD1Content))>
				<div class="upcoming-news-event-sec">
					<div class="container containerCustom">
						<div class="unes-left">
						<cfif len(trim(local.zoneC1Content))>
							<span class="eventHomeHolder"></span>
						</cfif>
						<cfif len(trim(local.zoneD1Content))>
							<span class="newsHomeHolder"></span>
						</cfif>
						</div>
						<div class="unes-right">
							<div class="adv-wrapper">
								#local.zoneE1Content#
								#local.zoneE2Content#
							</div>
						</div>
					</div>
				</div>
			</cfif>
			<cfif len(trim(local.zoneF1Content))>
				<span class="blogHomeHolder"></span>
			</cfif>
			<cfif len(trim(local.zoneG1Content))>
				<span class="sponsorHomeWrapper hide">#local.zoneG1Content#</span>
				<span class="sponsorHomeDeskHolder"></span>
				<span class="sponsorHomeMobHolder"></span> 
			</cfif>
			<!--Footer Start-->
			<cfinclude template="footer.cfm">
			<!--Footer End--> 
			<div class="menuTempWrap hide"></div>
		</div>
	</body>	
	<cfinclude template="toolBar.cfm">
	<cfelse>
	<body class="innerPage-content">
		<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
		#application.objCMS.renderZone(zone='Main',event=event)#
		</cfif>
	</body>
	</cfif>
	<cfinclude template="foot.cfm">
</html>
</cfoutput>