<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.programTabsJS">
<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/seminarweb.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/admin/javascript/featuredImages.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/ext/dhtmlxgrid_drag.js"></script>
	<style type="text/css">
		div##issuePanel ul { margin-top:5px; margin-bottom:0; }
		div##issuePanel ul li { padding:3px 0; }
		div##statementPanel ul { margin-top:5px; margin-bottom:0; }
		div##statementPanel ul li { padding:3px 0; }
		div##issueInactivePanel ul { margin-top:5px; margin-bottom:0; }
		div##issueInactivePanel ul li { padding:3px 0; }
		div##statementInactivePanel ul { margin-top:5px; margin-bottom:0; }
		div##statementInactivePanel ul li { padding:3px 0; }
	</style>
	<script language="javascript">
		var #ToScript(local.bundleID,"sw_bundleid")#
		var #ToScript(arguments.event.getValue('mc_siteInfo.sitecode'),"sw_sitecode")#
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteid'),"sw_siteid")#
		var #ToScript(local.editSWBProgram,'link_editswbprogram')#
		var #ToScript(this.link.editMember,'link_editmember')#		
		var sw_itemtype = 'SWB';
		var #ToScript(local.qryAssociation.participantID,"sw_participantid")#
		var #ToScript(local.hasLockSWProgramRights,'sw_haslockprogramrights')#
		var #ToScript(local.hasIncludedSeminarsCount,'hasIncludedSeminarsCount')#
		var sw_lockprogramsettings = #local.isSWProgramLocked#;
		var hasInactiveSeminarsIncluded = #local.hasInactiveSeminarsIncluded#;
		var #ToScript(local.programAdded,'programAdded')#;
		<cfif local.hasAddSWBRegistrantsRights>
			var #ToScript(local.addSWRegLink,'link_addSWReg')#
			var #ToScript(encodeForHTML(local.qryBundle.bundleName),'seminarName')#
		</cfif>
		programAdded = programAdded === "true";
		<cfif local.qryBundle.isSWOD>
			var bundleType = 'OnDemand';
		<cfelse>
			var bundleType = 'Live';
		</cfif>
		var #ToScript(local.timeZoneAbbr,'timeZoneAbbr')#

		<cfif local.hasEditRights>
			let SWBListItemsTable;
			var #ToScript(local.editSWLProgram,'link_editswlprogram')#
			var #ToScript(local.editSWODProgram,'link_editswodprogram')#
		</cfif>
		<cfif local.hasManageSWBRatesRights>
			var swProgramRatesTable;
			var #ToScript(local.includedItemsLink,'link_listincswbitems')#
			var #toScript(local.ratesListLink,'link_listSWRates')#
			var #toScript(local.permissionsGridAddLink,'mca_permsadd_link')#
			<cfif local.hasSWBRateChangeRights>
				var #toScript(local.editSWBRateLink,'link_editSWBRate')#
				var #ToScript(local.manageCopyRatesLink,'link_manageCopyRates')#
			</cfif>
		</cfif>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			var swBillingLogsTable;
			var #ToScript(local.swBillingLogsLink,'link_swBillingLogs')#;
		</cfif>
		<cfif local.hasAddSWBRegistrantsRights>
			var SWBRegistrantsListTable;
			var #ToScript(local.SWBRegistrantsLink,'link_swbregistrantslist')#;
		</cfif>
		<cfif hasManageSWBRegistrantsRights>
			var #ToScript(local.removeSWBOrderLink,'link_removeswbenrollment')#
			var #ToScript(local.changeRegistrantPriceLink,'link_changeregprice')#
		</cfif>
		var gridInitArray = new Array();
		gridInitArray["programTab"] = false;
		gridInitArray["registrantsTab"] = false;
		gridInitArray["billingTab"] = false;
	
		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "programTab":
						initProgramTab(); break
					case "registrantsTab":
						initSWBRegistrants(); break;
					case "billingTab":
						initSWBillingLogsTable();
						break;
				}
			}
		}
		function closeBox() { MCModalUtils.hideModal(); }
		<cfif NOT local.programAdded>
			function showHideSWIssuesPanel() {
				$('##issuePanel, ##showIssue, ##hideIssue').toggle();
			}
			function showHideSWStatementPanel() {
				$('##statementPanel, ##showStatement, ##hideStatement').toggle();
			}
		</cfif>
		<cfif !local.isPublisher>
			function onOptOutOfSyndicatedProgram() {
				self.location.href = '#local.listProgramsLink#';
			}
		</cfif>
		$(function() {
			manageSWProgramSettings('swb');
			mca_initNavPills('SWBProgramTabs', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
		});
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.programTabsJS#">

<cfoutput>
<cfif local.programAdded>
	<div class="progress mb-3" style="height: 20px;">
		<div id="progressBarTop" class="progress-bar bg-success" role="progressbar" style="text-indent: 10px; width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
	</div>
</cfif>
<button class="btn btn-sm btn-warning float-right <cfif NOT local.showSyndicationWarning>d-none</cfif>" id="syndicationWarning" type="button" data-container="body" data-trigger="hover" data-toggle="popover" data-placement="left" data-content="This bundle has enabled syndication, but no associations are opted in. We'll apply the syndication fee schedule to this bundle."><i class="fa-regular fa-triangle-exclamation"></i> Syndication Warning</button>
<cfif local.canOptOutOfSyndicateSite AND local.enrollmentCount EQ 0>
	<button class="btn btn-sm btn-primary float-right" <cfif local.isSWProgramLocked>disabled</cfif> id="optOutOfProgram" onclick="this.disabled=true; removeSWBProgramPartner('#encodeForHTML(local.qryBundle.bundleName)#');" type="button" data-container="body" data-trigger="hover" data-toggle="popover" data-placement="left" >Opt-out of Syndicated Program</button>
</cfif>
<h4 id="dispBundleTitle" class="<cfif len(local.qryBundle.bundleSubTitle)>mb-1<cfelse>mb-2</cfif>">#encodeForHTML(local.qryBundle.bundleName)#</h4>
<h5 id="dispBundleSubTitle" class="mb-3<cfif not len(local.qryBundle.bundleSubTitle)> d-none</cfif>">#encodeForHTML(local.qryBundle.bundleSubTitle)#</h5>
<cfif NOT local.programAdded>
	<div class="alert alert-warning mb-3 mt-3 seminarSetupIssues <cfif NOT arrayLen(local.arrSeminarSetupIssues)>d-none</cfif> ">
		This bundle is not accepting registrations at this time. &nbsp; 
		<a href="##" onclick="showHideSWIssuesPanel();return false;">
			<span id="showIssue">Why not?</span>
			<span id="hideIssue" style="display:none;">Hide details.</span>
		</a>
		<div id="issuePanel" style="display:none;">
			<ul>
			<cfloop array="#local.arrSeminarSetupIssues#" index="local.thisIssue">	
				<li>#local.thisIssue#</li>
			</cfloop>
			</ul>
		</div>
	</div>
	<div class="alert alert-success mb-3 mt-3 seminarSetupStatements <cfif arrayLen(local.arrSeminarSetupIssues)>d-none</cfif> ">
		This bundle is accepting registrations at this time. &nbsp; &nbsp;
		<a href="##" onclick="showHideSWStatementPanel();return false;">
			<span id="showStatement">Learn more.</span>
			<span id="hideStatement" style="display:none;">Hide details.</span>
		</a>
		<div id="statementPanel" style="display:none;">
			<ul>
			<cfloop array="#local.arrSeminarSetupSuccess#" index="local.thisStatement">	
				<li>#local.thisStatement#</li>
			</cfloop>
			</ul>
		</div>
	</div>
</cfif>
<div id="lockedProgramSetting" class= "<cfif local.isSWProgramLocked is not 1>d-none</cfif>">
	<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert">
		<span class="font-size-lg d-block d-40 mr-2 text-center">
			<i class="fa-regular fa-lock"></i>
		</span>
		<span>
			<strong class="d-block">This Bundle's Settings are Locked</strong> 
			When locked, changes cannot be made to this bundle's settings. Contact your Client Administrator to unlock.
		</span>
	</div>
</div>

<ul class="nav nav-pills nav-pills-dotted" id="SWBProgramTabs">
	<cfset local.thisTabName = "program">
	<cfset local.thisTabID = "programTab">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
			aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Program</a>
	</li>
	<cfif local.hasManageSWBRegistrantsRights AND NOT local.programAdded>
		<cfset local.thisTabName = "registrants">
		<cfset local.thisTabID = "registrantsTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Registrants</a>
		</li>
	</cfif>

	<cfif local.showBillingTab AND NOT local.programAdded>
		<cfset local.thisTabName = "billing">
		<cfset local.thisTabID = "billingTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Billing <span class="superuser small"></span></a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="divSWBTabContent">
	<div class="tab-pane fade" id="pills-programTab" role="tabpanel" aria-labelledby="programTab">
		<cfinclude template="frm_SWB_program_overall.cfm">
	</div>
	<cfif local.hasManageSWBRegistrantsRights AND NOT local.programAdded>
		<div class="tab-pane fade" id="pills-registrantsTab" role="tabpanel" aria-labelledby="registrantsTab">
			<cfinclude template="frm_SWB_program_registrants.cfm">
		</div>
	</cfif>
	<cfif local.showBillingTab AND NOT local.programAdded>
		<div class="tab-pane fade" id="pills-billingTab" role="tabpanel" aria-labelledby="billingTab">
			<cfinclude template="frm_SWB_program_billing.cfm">
		</div>
	</cfif>
</div>
</cfoutput>