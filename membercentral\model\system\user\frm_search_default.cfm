<cfform name="frmLocator" id="frmLocator" action="#arguments.searchPostURL#" method="post" onsubmit="return false;">
<cfinput type="hidden" name="fsid_s" id="fsid_s" value="#val(local.qryFieldsetInfo.fieldsetID)#">
<div class="tsAppBodyText"><div id="AL_err_div" style="display:none;"></div></div>
<table>
<cfset local.showSearchOption = false>
<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
	<tr valign="top">
		<td class="tsAppBodyText"><cfif local.thisfield.xmlattributes.isRequired is 1>*</cfif>&nbsp;</td>
		<td class="tsAppBodyText"><cfoutput>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:</cfoutput></td>
		<td class="tsAppBodyText">
			<cfif local.qryFieldsetInfo.showHelp AND len(trim(local.thisfield.xmlattributes.fieldDescription))>
				<cfoutput><img src="/assets/common/images/help-icon.jpg" onMouseOver="Tip('#JSStringFormat(replace(trim(local.thisfield.xmlattributes.fieldDescription),chr(34),"'","ALL"))#');" onMouseOut="UnTip();" /></cfoutput>
			<cfelse>
			&nbsp;
			</cfif>
		</td>
		<td class="tsAppBodyText">
			<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
			<cfcase value="TEXTBOX">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
					Within 
					<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" >
						<cfloop list="5,10,25,50,100" index="local.thisrad">
							<cfoutput><option value="#local.thisrad#">#local.thisrad#</option></cfoutput>
						</cfloop>
					</cfselect>
					miles of ZIP code 
					<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="8">
				<cfelse>
					<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="24">
					<cfset local.showSearchOption = true>
				</cfif>
			</cfcase>
			<cfcase value="RADIO">
				<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
					<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
					<cfcase value="STRING">
						<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
					</cfcase>
					<cfcase value="DECIMAL2">
						<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
					</cfcase>
					<cfcase value="INTEGER">
						<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
					</cfcase>
					<cfcase value="DATE">
						<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
					</cfcase>
					<cfcase value="BIT">
						<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
					</cfcase>
					<cfdefaultcase>
						<cfset local.thisOptColValue = "">
					</cfdefaultcase>
					</cfswitch>
					<cfinput type="radio" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisOptColValue#"><cfoutput><cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif><br/></cfoutput>
				</cfloop>
			</cfcase>
			<cfcase value="SELECT,CHECKBOX">
				<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
					<cfset local.qryStates = application.objCommon.getStates()>
					<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
						<option value=""></option>
						<cfoutput query="local.qryStates" group="countryID">
							<optgroup label="#local.qryStates.country#">
							<cfoutput>
								<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
							</cfoutput>
							</optgroup>
						</cfoutput>
					</cfselect>
				<cfelse>
					<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
						<option value=""></option>
						<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
							<cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
							<cfcase value="STRING">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
							</cfcase>
							<cfcase value="DECIMAL2">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
							</cfcase>
							<cfcase value="INTEGER">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
							</cfcase>
							<cfcase value="DATE">
								<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
							</cfcase>
							<cfcase value="BIT">
								<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
							</cfcase>
							<cfdefaultcase>
								<cfset local.thisOptColValue = "">
							</cfdefaultcase>
							</cfswitch>
							<cfoutput><option value="#local.thisOptColValue#"><cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif></option></cfoutput>
						</cfloop>
					</cfselect>
				</cfif>
			</cfcase>
			<cfcase value="DATE">
				<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#" id="search_#local.thisfield.xmlattributes.fieldCode#" value="" autocomplete="off" class="tsAppBodyText" size="12">
				<cfoutput><a href="javascript:mca_clearDateRangeField('search_#local.thisfield.xmlattributes.fieldCode#');" title="Clear Date"><i class="icon-remove-circle" style="vertical-align:text-bottom; margin: 0 0 -1px 3px;"></i></a></cfoutput>
				<cfsavecontent variable="local.datejs">
					<cfoutput>											
					<script type="text/javascript">
						setTimeout(function(){ loadCalendar('search_#local.thisfield.xmlattributes.fieldCode#')() },300);
					</script>
					<style type="text/css">
					###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
					</style>
					</cfoutput>
				</cfsavecontent>
				<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">								
			</cfcase>
			</cfswitch>
		</td>
	</tr>
	<cfif local.thisfield.xmlattributes.isRequired is 1>
		<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
		<cfcase value="TEXTBOX">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "TEXT", false)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="RADIO">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "RADIO", false)) locateErr += '<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>';
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="SELECT,CHECKBOX">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if (_CF_this['#local.thisfield.xmlattributes.fieldCode#'].options[_CF_this['#local.thisfield.xmlattributes.fieldCode#'].selectedIndex].value.length == 0) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		<cfcase value="DATE">
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				#local.jsValidation#
				if(!_CF_hasValue(_CF_this['#local.thisfield.xmlattributes.fieldCode#'], "DATEFIELD", false) || !_CF_checkdate(_CF_this['#local.thisfield.xmlattributes.fieldCode#'].value, true)) locateErr += "<i>#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#</i> is required and must be a valid date.<br/>";
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		</cfswitch>
		<cfset local.showReqFlag = true>
	</cfif>
</cfloop>
<cfif local.showSearchOption>
	<tr><td>&nbsp;</td>
		<td colspan="3" nowrap class="tsAppBodyText" style="padding-top:14px;">
			<div role="radiogroup" aria-labelledby="fs_match_label">
				<div style="padding-left:25px;padding-top:5px;">
				Find matches
				<select name="fs_match" id="fs_match">
					<option value="s" <cfif local.qrySettings.defaultSearchOption EQ 's'>selected</cfif>>beginning with</option>
					<option value="c" <cfif local.qrySettings.defaultSearchOption EQ 'c'>selected</cfif>>containing</option>
					<option value="e" <cfif local.qrySettings.defaultSearchOption EQ 'e'>selected</cfif>>exactly matching</option>
				</select>
				the terms I entered.</div>
			</div>
		</td>
	</tr>
</cfif>
<tr><td colspan="4">&nbsp;</td></tr>
<tr><td colspan="3"></td><td class="tsAppBodyText">
	<button name="btn_st_s" id="btn_st_s" type="button" class="tsAppBodyButton" onClick="doALSearch()"><cfoutput>#arguments.buttonText#</cfoutput></button>
</td></tr>
<cfif local.showReqFlag>
	<tr><td colspan="4" class="tsAppBodyText"><i>* required field</i></td></tr>
</cfif>
</table>
</cfform>