<cfparam name="local.msgText" default="" />

<cfsavecontent variable="local.clientFormJS">
	<cfoutput>
        <style type="text/css"> 
            .saveButton { padding: 0 35px 0 0; }
            .fr { float:right; }
            .dspTabTrue { visibility:visible; }
            .dspTabFalse { visibility:hidden; }
            ##tsAppVisible {.tsApp;}
            ##requiredLabel { padding-left:10px; }
            ##reqPhoneText {padding-left:170px; font-style:italic;}
			##frmClient .iti  input{
				padding-top: 14px !important;
				padding-bottom: 14px !important;
			}
			.smsReferralRep .ui-icon-triangle-2-n-s ,
			.smsReferralClient .ui-icon-triangle-2-n-s {
				background-position: -129px -12px;
			}
        </style>
		<script type="text/javascript" src="/assets/common/javascript/resourceFields.js"></script>
        <script language="JavaScript">
			hasRep = 1;
			$(window).on('resize', function(){
				$('button.ui-multiselect').css('width',$('##subpanelid1').width());											
				$('div.ui-multiselect-menu').css('width',$('##subpanelid1').width());
				$('button.ui-multiselect').css('max-width','100%');											
				$('div.ui-multiselect-menu').css('max-width','100%');
			});
			function doFormProcess(){
				var errorMsg = "";
				
				$('.saveClientBtn').attr('disabled','disabled');
				$("##frmClient").attr('action', '#attributes.data.mainurl#&ra=search&saveClientBtn=1');
				var changeInd = checkFormChanged();

				errorMsg += validateClientReferral();

				if (errorMsg.length > 0) {
					if (errorMsg != 'cancel') {
						alert("There were errors with your submission.\n\n" + errorMsg);
					}
				} else {
					$('##frmClient').hide();
					$('##formSubmitting').show();
					$('html,body').animate({scrollTop: $('##formSubmitting').offset().top},100);
					return true;
				}
				$('.saveClientBtn').attr('disabled',false);
				return false;
			}
			
            $(document).ready(function(){
			
				if(localStorage.resultViewed != undefined && localStorage.resultViewed == 1){
					$("##frmClient").hide();
					localStorage.resultViewed = 0;
					window.location.href = '#attributes.data.mainurl#';
				}
                var subCheckHasRun1 = false;

                formOriginalData = $("##frmClient").serialize();		
				
                $('##isRep').change(function(){
                    $("##repFormTbl").toggle();
                });

                $("##subpanelid1").multiselect({
                    multiple: true,
                    header: false
                });

				$('button.ui-multiselect').css('width',$('##subpanelid1').width());											
				$('div.ui-multiselect-menu').css('width',$('##subpanelid1').width());
				$('button.ui-multiselect').css('max-width','100%');											
				$('div.ui-multiselect-menu').css('max-width','100%');

                function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,format){
                    var strURL = "/?event=cms.showResource&resID=#attributes.data.siteResourceID#&mode=stream&ra=subPanelData&panelid="+ selected + "&isActive=1";
                    $.ajax({
                        url: strURL,
                        dataType: 'json',
                        success: function(response){				
                            $('##' + elemIdName).empty();
                            for (var i = 0; i < response.DATA.length; i++) {
                                if(response.DATA[i][1]) {//show panel in front end
                                    var o = new Option(response.DATA[i][1], response.DATA[i][0]);
                                    /* jquerify the DOM object 'o' so we can use the html method */
                                    $(o).html(response.DATA[i][1]);
                                    $('##' + elemIdName).append(o);
                                    $('##' + elemIdName).multiselect('refresh');
                                }
                            }
                        },
                        error: function(ErrorMsg){
                            /*console.log(ErrorMsg);*/
                        }
                    })
                }	
                
                function callChainedSelect(panel,subpanel){
                    var strSelected = $("##" + panel).val();
                    chainedSelect(
                        subpanel,        /* select box id  */
                        0,                  /* select box default value */
                        'Select Options',   /* select box default text */
                        strSelected,        /* value of the select */
                        'json'              /* return format */
                    );
                }

                $('body').on('change', '##panelid1', function(e) {
                    var panelsArr = []; 
                    <cfloop query="attributes.data.qryGetPanelsFilter">
                    panelsArr["#attributes.data.qryGetPanelsFilter.panelID#"] = "#jsstringformat(rereplace(rereplace(attributes.data.qryGetPanelsFilter.longDesc,'#chr(10)#','','ALL'),'#chr(13)#','<br>','ALL'))#";
                    </cfloop>
                    var divPanelDesc = "";				
                    if($('##panelid1').val() > 0) {
                        divPanelDesc = panelsArr[$('##panelid1').val()];
                        if ($.trim(divPanelDesc).length > 0) {
                            $(".trPanelDesc").show();
                            $(".divPanelDesc").html(divPanelDesc);	
                        }		
                        else{
                            $(".trPanelDesc").show();
                            $(".divPanelDesc").html("");					
                        }
                    } else {
                        $(".trPanelDesc").show();
                        $(".divPanelDesc").html("");			
                    }
                    callChainedSelect("panelid1","subpanelid1");
                });

                <cfif not attributes.data.isRep>
                    $("##repFormTbl").hide();
                </cfif>	

                $(".trPanelDesc").hide();
            });

            function chkSubPanel1Select(subPanel,subID){
                
                if (!subCheckHasRun1){
                    var dd = document.getElementById('subpanelid1');
                    <cfloop list="#attributes.data.subpanelid1#" index="local.thisItem">			
                    for (var i=0; i < dd.length; i++){
                        if (dd.options[i].value == #local.thisItem#) {
                            dd.options[i].selected = true;
                            break;
                        }
                    }
                    </cfloop>
                    subCheckHasRun1 = true;
                    $("##subpanelid1").multiselect('refresh');
                }					
            }

            function refreshDropDown(panelField){
                setTimeout(function() {
                    $("##" + panelField).multiselect('refresh');
                }, 1500);
            }

            function checkFormChanged(){
            var changeInd = false;
                if (($('##referralCanEditClient').val() == 1) && ($("##frmClient").serialize() != formOriginalData)) {
                    $("##formChanged").val('1');
                    changeInd = true;
                }
                return changeInd;
            }

            function validateClientFrm(){
                var nameRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{1,})$/;
				var initialRegex = /^([a-zA-Z]{1,})+([a-zA-Z0-9'\.\-\s]{0,1})+([a-zA-Z]{0,1})$/;
                var emailRegex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/;
                var phoneRegex = /^(1\s*[-\/\.]?)?(\((\d{3})\)|(\d{3}))\s*([\s-./\\])?([0-9]*)([\s-./\\])?([0-9]*)$/;
                var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
                var errorMsg = "";
                var typeLabel = $('##typeID option:selected').text();
                var sourceOption = $.trim($('##sourceID option:selected').text());

				<cfif attributes.data.isFrontEndDisplay['First Name']>
                if( <cfif attributes.data.isRequired['First Name']>($.trim($('##firstName').val()).length == 0) ||</cfif> (($.trim($('##firstName').val()).length > 0) && (!nameRegex.test($.trim($('##firstName').val())))) ) {
                    errorMsg += '- Enter a valid client first name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Middle Name']>
                if( <cfif attributes.data.isRequired['Middle Name']>($.trim($('##middleName').val()).length == 0) || </cfif>(($.trim($('##middleName').val()).length > 0) && (!initialRegex.test($.trim($('##middleName').val()))))) {
                    errorMsg += '- Enter a valid client middle name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Last Name']>
                if( <cfif attributes.data.isRequired['Last Name']>($.trim($('##lastName').val()).length == 0) || </cfif>(($.trim($('##lastName').val()).length > 0) && (!nameRegex.test($.trim($('##lastName').val()))))) {
                    errorMsg += '- Enter a valid client last name. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Business']>
                if( <cfif attributes.data.isRequired['Business']>($.trim($('##businessName').val()).length == 0) || </cfif>(($.trim($('##businessName').val()).length > 0) && (!nameRegex.test($.trim($('##businessName').val()))))) {
                    errorMsg += '- Enter a valid business. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 1'] AND  attributes.data.isRequired['Address 1']>
                if($.trim($('##address1').val()).length == 0) {
                    errorMsg += '- Enter a valid address 1.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Address 2'] AND  attributes.data.isRequired['Address 2']>
                if($.trim($('##address2').val()).length == 0) {
                    errorMsg += '- Enter a valid address 2.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['City'] AND attributes.data.isRequired['City']>
                if( $.trim($('##city').val()).length == 0) {
                    errorMsg += '- Enter a valid city.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['State'] AND attributes.data.isRequired['State']>
                if($.trim($('##state').val()).length == 0) {
                    errorMsg += '- Select a state.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Zip Code'] AND attributes.data.isRequired['Zip Code']>
                if( $.trim($('##postalCode').val()).length == 0) {
                    errorMsg += '- Enter a valid Zip Code.\n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Email']>
				if( <cfif attributes.data.isRequired['Email']>( $.trim($('##email').val()).length == 0) ||</cfif> ($.trim($('##email').val()).length > 0 && (!emailRegex.test($.trim($('##email').val())))) ) {
                    errorMsg += '- Enter a valid client e-mail. \n';
                }	
				if( <cfif attributes.data.isRequired['Email']>( $.trim($('##verifyEmail').val()).length == 0) ||</cfif> ( $.trim($('##email').val()).length > 0 && $.trim($('##email').val()) != $.trim($('##verifyEmail').val()))) {
                    errorMsg += '- Please verify your e-mail. \n';
                }
				</cfif>
				<cfif attributes.data.isFrontEndDisplay['Home Phone ##']>
				indexHome = arrPhoneNumbersIds.indexOf("homePhone");
                if (<cfif attributes.data.isRequired['Home Phone ##']>( $.trim($('##homePhone').val()).length == 0) || </cfif>($.trim($('##homePhone').val()).length > 0) && indexHome >= 0) {
                    if(!MFAPhNoInput[indexHome].isValidNumber()) {
						errorMsg += '- Enter the client home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
                	}else{
						setNumberFormats(indexHome,'validation');
					}
				}
				</cfif>
				indexCellPhone = arrPhoneNumbersIds.indexOf("cellPhone");
				<cfif attributes.data.isFrontEndDisplay['Cell Phone ##']>
				if( <cfif attributes.data.isRequired['Cell Phone ##']>( $.trim($('##cellPhone').val()).length == 0) ||</cfif>($.trim($('##cellPhone').val()).length > 0) && indexCellPhone >= 0) {
					if(!MFAPhNoInput[indexCellPhone].isValidNumber()) {
						errorMsg += '- Enter the client cell number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
					}else{
						setNumberFormats(indexCellPhone,'validation');
					}
				}
				</cfif>

				indexAlternatePhone = arrPhoneNumbersIds.indexOf("alternatePhone");
				<cfif attributes.data.isFrontEndDisplay['Alternate Phone ##']>
                if( <cfif attributes.data.isRequired['Alternate Phone ##']>( $.trim($('##alternatePhone').val()).length == 0) ||</cfif>($.trim($('##alternatePhone').val()).length > 0) && indexAlternatePhone >= 0) {
                    if(!MFAPhNoInput[indexAlternatePhone].isValidNumber()) {
						errorMsg += '- Enter the client alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
					}else{
						setNumberFormats(indexAlternatePhone,'validation');
					}
				}	
                </cfif>

                if ($("##isRep").is(':checked')){		
                    if( ($.trim($('##repFirstName').val()).length == 0) || (($.trim($('##repFirstName').val()).length > 0) && (!nameRegex.test($.trim($('##repFirstName').val())))) ) {
                        errorMsg += '- Enter a valid representative first name. \n';
                    }
                    if( ($.trim($('##repLastName').val()).length == 0) || (($.trim($('##repLastName').val()).length > 0) && (!nameRegex.test($.trim($('##repLastName').val()))))) {
                        errorMsg += '- Enter a valid representative last name. \n';
                    }			
                    indexRepHomePhone = arrPhoneNumbersIds.indexOf("repHomePhone");
					if($.trim($('##repHomePhone').val()).length > 0 && indexRepHomePhone >= 0) {
						if(!MFAPhNoInput[indexRepHomePhone].isValidNumber()){
							errorMsg += '- Enter the representative home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepHomePhone,'validation');
						}		
					}

					indexRepCellPhone = arrPhoneNumbersIds.indexOf("repCellPhone");
					if($.trim($('##repCellPhone').val()).length > 0 && indexRepCellPhone >= 0) {
						if(!MFAPhNoInput[indexRepCellPhone].isValidNumber()){
							errorMsg += '- Enter the representative cell telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepCellPhone,'validation');
						}		
					}

					indexRepAlternatePhone = arrPhoneNumbersIds.indexOf("repAlternatePhone");
					if($.trim($('##repAlternatePhone').val()).length > 0 && indexRepAlternatePhone >= 0) {
						if(!MFAPhNoInput[indexRepAlternatePhone].isValidNumber()){
							errorMsg += '- Enter the representative alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).\n';
						}else{
							setNumberFormats(indexRepAlternatePhone,'validation');
						}		
					}
	
                    if( (($.trim($('##repEmail').val()).length > 0) && (!emailRegex.test($.trim($('##repEmail').val())))) ) {
                        errorMsg += '- Enter a valid representative e-mail. \n';
                    }			
                    if( ($.trim($('##repEmail').val()).length > 0) && ($.trim($('##repEmail').val()) != $.trim($('##repVerifyEmail').val()))) {
                        errorMsg += '- Please verify representative e-mail. \n';
                    }
                }

				<cfif attributes.data.dspLegalDescription>
					if( ($.trim($('##issueDesc').val()).length == 0)) {
						errorMsg += '- Enter the legal issue description. \n';
					}

					<cfif val(attributes.data.feLegalDescLimitWords)>
						updateLegalDescWordCount();
						var legalDescWordLimit = #int(val( attributes.data.feLegalDescLimitWordCount))#;
						/* Check if word count exceeds limit */
						if (parseInt($('##legalDescWordcount').text()) > legalDescWordLimit) errorMsg += '- ' + $('##legalDescWordLimitExceedAlert').text() + ' \n';
					</cfif>
				</cfif>
				
                var panelLabel = $('##panelid1 option:selected').text();
                    
                if($('##panelid1').val() == 0) {
                    errorMsg += '- Select a primary panel.\n';
                }

				var errorMsgArray = [];
				<cfif attributes.data.extraInformation.hasFields>
					#attributes.data.extraInformation.JSVALIDATION#
				</cfif>		

				/*drop empty elements*/
				var finalErrors = $.map(errorMsgArray, function(thisError){
					if (thisError.length) return "- "+thisError;
					else return null;
				});
				errorMsg += finalErrors.join('\n');	
                                    
                return errorMsg;
            }

            function validateClientReferral(){		
                var errorMsg = "";	
                errorMsg = validateClientFrm();
                return errorMsg;
            }
            
            function closeBox() { $.colorbox.close(); }
            
            function selectCaseStatus(){
                $.colorbox( {innerWidth:650,innerHeight:350, href:'#attributes.data.mainurl#&ra=selectCase&clientReferralID=#attributes.data.clientReferralID#&mode=direct', iframe:true, overlayClose:false} );
            }

            function formatCurrency(num) {
                num = num.toString().replace(/\$|\,/g,'');
                if(isNaN(num)) num = "0";
                sign = (num == (num = Math.abs(num)));
                num = Math.floor(num*100+0.50000000001);
                cents = num%100;
                num = Math.floor(num/100).toString();
                if(cents<10) cents = "0" + cents;
                for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
                return (((sign)?'':'-') + num + '.' + cents);
            }
			<cfif attributes.data.dspLegalDescription AND val(attributes.data.feLegalDescLimitWords)>
				function updateLegalDescWordCount() {
					const legalDesc = $('##issueDesc').val().trim();
					const legalDescWordLimit = #int(val(attributes.data.feLegalDescLimitWordCount))#;
					const legalDescWords = legalDesc.split(/\s+/).filter(Boolean); /* Filter out empty strings*/
					const legalDescWordCount = legalDescWords.length;
					$('##legalDescWordcount').text(legalDescWordCount);

					/* Check if word count exceeds limit */
					if (legalDescWordCount > legalDescWordLimit) {
						$('##legalDescWordLimitExceedAlert').show();
						$('.saveClientBtn').prop('disabled',true);
						return false;
					} else {
						$('##legalDescWordLimitExceedAlert').hide();
						$('.saveClientBtn').prop('disabled',false);
					}
				}

				$(function () {
					$('##issueDesc').on("input", updateLegalDescWordCount);
					updateLegalDescWordCount();
				});
			</cfif>
        </script>
		<cfif attributes.data.extraInformation.hasFields>
			#attributes.data.extraInformation.head#
		</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.clientFormJS#" />
<cfoutput>
<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
<cfform name="frmClient" id="frmClient" method="POST" class="form-horizontal" onsubmit="return doFormProcess();">
	<cfinput type="hidden" name="repParentID" id="repParentID" value="#attributes.data.repParentID#" />
	
	<div class="container-fluid">
		<h4>#attributes.data.referralPageTitle#</h4>
		<cfif structKeyExists(attributes.data,"saleError") AND  val(attributes.data.saleError)>
			<div class="row-fluid" id="saleError">
				<div class="alert alert-danger" role="alert"><span>Oops! Something went wrong. Please try after sometime.</span><button type="button" class="close hide" data-dismiss="alert" aria-label="Close" >&times;</button></div>
			</div>
		</cfif>
		<cfif len(trim(attributes.data.feFormInstructionsContent))>
			<div class="row-fluid control-group">				
				#attributes.data.feFormInstructionsContent#							
			</div>	
		</cfif>	
		<div class="well well-small"><b>Contact Information</b></div>
		<div class="well well-small">
			<div class="row-fluid control-group">
				<div class="controls">
					* indicates a required field
				</div>
			</div>

			<cfif attributes.data.isFrontEndDisplay['First Name']>
				<div class="row-fluid control-group">
					<label for="firstName" class="control-label ">First Name:<cfif attributes.data.isRequired['First Name']>*</cfif></label>
					<div class="controls">
						<cfinput name="firstName"  id="firstName" type="text" class="input-large"  maxlength="75" value="#attributes.data.firstName#" />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['Middle Name']>
				<div class="row-fluid control-group">
					<label for="middleName" class="control-label ">Middle Name:<cfif attributes.data.isRequired['Middle Name']>*</cfif></label>
					<div class="controls">
						<cfinput name="middleName"  id="middleName" type="text" class="input-large" maxlength="25" value="#attributes.data.middleName#" />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['Last Name']>
				<div class="row-fluid control-group">
					<label for="lastName" class="control-label ">Last Name:<cfif attributes.data.isRequired['Last Name']>*</cfif></label>
					<div class="controls">
						<cfinput name="lastName"  id="lastName" type="text" class="input-large" maxlength="75" value="#attributes.data.lastName#"  />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['Business']>
				<div class="row-fluid control-group">
					<label for="businessName" class="control-label ">Business:<cfif attributes.data.isRequired['Business']>*</cfif></label>
					<div class="controls">
						<cfinput name="businessName"  id="businessName" type="text" class="input-large" maxlength="100" value="#attributes.data.businessName#"  />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['Address 1']>
				<div class="row-fluid control-group">
					<label for="address1" class="control-label ">Address 1:<cfif attributes.data.isRequired['Address 1']>*</cfif></label>
					<div class="controls">
						<cfinput name="address1"  id="address1" type="text" class="input-large" maxlength="100" value="#attributes.data.address1#"  />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['Address 2']>
				<div class="row-fluid control-group">
					<label for="address2" class="control-label ">Address 2:<cfif attributes.data.isRequired['Address 2']>*</cfif></label>
					<div class="controls">
						<cfinput name="address2"  id="address2" type="text" class="input-large" maxlength="100" value="#attributes.data.address2#"  />
					</div>
				</div>
			</cfif>
			<cfif attributes.data.isFrontEndDisplay['City']>
				<div class="row-fluid control-group">
					<label for="city" class="control-label ">City:<cfif attributes.data.isRequired['City']>*</cfif></label>
					<div class="controls">
						<cfinput name="city"  id="city" type="text" class="input-large" maxlength="100" value="#attributes.data.city#"  />
					</div>
				</div>
			</cfif>
		
			<cfif attributes.data.isFrontEndDisplay['State']>
				<div class="row-fluid control-group">
					<label for="State" class="control-label ">State:<cfif attributes.data.isRequired['State']>*</cfif></label>
					<div class="controls">					
						<select name="state" id="state" class=" input-large">
							<option value=""></option>
													
							<cfoutput query="attributes.data.qryStates" group="countryID">
								<optgroup label="#attributes.data.qryStates.country#">
									<cfoutput>
										<option value="#attributes.data.qryStates.stateid#" <cfif val(attributes.data.qryStates.stateid) eq val(attributes.data.state)>selected</cfif>>#attributes.data.qryStates.stateName# &nbsp;</option>
									</cfoutput>
								</optgroup>
							</cfoutput>
							
						</select>
					</div>
				</div>
			</cfif>

			<cfif attributes.data.isFrontEndDisplay['Zip Code']>
				<div class="row-fluid control-group">
					<label for="postalCode" class="control-label ">Zip Code:<cfif attributes.data.isRequired['Zip Code']>*</cfif></label>
					<div class="controls">
						<cfinput name="postalCode"  id="postalCode" type="text" class="input-large" maxlength="25" value="#attributes.data.postalCode#"  />
					</div>
				</div>
			</cfif>

			<cfif attributes.data.isFrontEndDisplay['Email']>
				<div class="row-fluid control-group">
					<label for="email" class="control-label ">E-mail:<cfif attributes.data.isRequired['Email']>*</cfif></label>
					<div class="controls">
						<cfinput name="email"  id="email" type="text" class="input-large" maxlength="255" value="#attributes.data.email#"  />
					</div>
				</div>
				<div class="row-fluid control-group">
					<label for="verifyEmail" class="control-label ">Verify Your Email:<cfif attributes.data.isRequired['Email']>*</cfif></label>
					<div class="controls">
						<cfinput name="verifyEmail" id="verifyEmail" type="text" class="input-large" maxlength="255" value="" />
					</div>
				</div>
			</cfif>

			<cfif attributes.data.isFrontEndDisplay['Home Phone ##']>
				<div class="row-fluid control-group">
					<label for="homePhone" class="control-label ">Home Phone ##:<cfif attributes.data.isRequired['Home Phone ##']>*</cfif></label>
					<div class="controls">
						<cfinput name="homePhone"  id="homePhone" type="text" class="input-large" maxlength="40" value="#attributes.data.homePhone#"  />
						<input type="hidden" name="homePhoneE164" id="homePhoneE164" value="#attributes.data.homePhoneE164#">
						<input type="hidden" name="homePhoneNational" id="homePhoneNational" value="#attributes.data.homePhone#">
					</div>
				</div>
			</cfif>

			<cfif attributes.data.isFrontEndDisplay['Cell Phone ##']>
				<div class="row-fluid control-group">
					<label for="cellPhone" class="control-label ">Cell Phone ##:<cfif attributes.data.isRequired['Cell Phone ##']>*</cfif></label>
					<div class="controls">
						<cfinput name="cellPhone"  id="cellPhone" type="text" class="input-large" maxlength="40" value="#attributes.data.cellPhone#"  />
						<input type="hidden" name="cellPhoneE164" id="cellPhoneE164" value="#attributes.data.cellPhoneE164#">
						<input type="hidden" name="cellPhoneNational" id="cellPhoneNational" value="#attributes.data.cellPhone#">
					</div>
				</div>
			</cfif>

			<cfif attributes.data.isFrontEndDisplay['Alternate Phone ##']>
				<div class="row-fluid control-group">
					<label for="alternatePhone" class="control-label ">Alternate Phone ##:<cfif attributes.data.isRequired['Alternate Phone ##']>*</cfif></label>
					<div class="controls">
						<cfinput name="alternatePhone"  id="alternatePhone" type="text" class="input-large" maxlength="40"  value="#attributes.data.alternatePhone#"  />
						<input type="hidden" name="alternatePhoneE164" id="alternatePhoneE164" value="#attributes.data.alternatePhoneE164#">
						<input type="hidden" name="alternatePhoneNational" id="alternatePhoneNational" value="#attributes.data.alternatePhone#">
					</div>
				</div>
			</cfif>
			
			<div class="row-fluid control-group">
				<div class="controls">
					<i>At least one phone number or e-mail is required.</i>
				</div>				
			</div>
			<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
				<div class="row-fluid control-group smsReferralClient">
					<label for="smsReferralClient" class="control-label ">Text Me My Referrals:</label>
					<div class="controls">
						<i>In addition to email, would you also like to receive your referral information via text? 
						Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
						</i>
						<br/><br/>
						<select class="input-large" name="smsClientNumbers" id="smsClientNumbers" multiple="multiple" >
							
						</select>
					</div>
				</div>
			</cfif>
		</div>

		<div id="refRepInfo"<cfif attributes.data.hideRepFieldsFE> style="display:none;"</cfif>>
			<div class="well well-small"><b>Representative</b></div>
			<div class="well well-small">
		
				<div class="row-fluid control-group">
					<label for="isRep" class="control-label ">Are you filling out this form on behalf of another party?:</label>
					<div class="controls">
						<label for="isRep"><input type="checkbox" name="isRep" id="isRep" class="input-large" value="1" <cfif attributes.data.isRep AND NOT attributes.data.hideRepFieldsFE>checked</cfif>> Yes</label>
					</div>
				</div>
				<div id="repFormTbl">
					<div class="row-fluid control-group">
						<div class="controls">
							* indicates a required field
						</div>
					</div>
					<div class="row-fluid control-group">
						<label for="repFirstName" class="control-label ">First Name:*</label>
						<div class="controls">
							<cfinput name="repFirstName"  id="repFirstName" class="input-large" type="text" maxlength="75" value="#attributes.data.repFirstName#"/>
						</div>
					</div>
					<div class="row-fluid control-group">
						<label for="repLastName" class="control-label ">Last Name:*</label>
						<div class="controls">
							<cfinput name="repLastName"  id="repLastName" class="input-large" type="text" maxlength="75" value="#attributes.data.repLastName#"/>
						</div>
					</div>
					<div class="row-fluid control-group">
						<label for="relationToClient" class="control-label ">Relationship to Contact:*</label>
						<div class="controls">
							<cfinput name="relationToClient"  id="relationToClient" class="input-large" type="text" maxlength="100" value="#attributes.data.relationToClient#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repAddress1" class="control-label ">Address 1:</label>
						<div class="controls">
							<cfinput name="repAddress1"  id="repAddress1" type="text" class="input-large" maxlength="100" value="#attributes.data.repAddress1#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repAddress2" class="control-label ">Address 2:</label>
						<div class="controls">
							<cfinput name="repAddress2"  id="repAddress2" type="text" class="input-large"  maxlength="100" value="#attributes.data.repAddress2#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repCity" class="control-label ">City:</label>
						<div class="controls">
							<cfinput name="repCity"  id="repCity" type="text" maxlength="100" class="input-large" value="#attributes.data.repCity#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repState" class="control-label ">State:</label>
						<div class="controls">
							<select name="repState" id="repState" class="input-large">
								<option value=""></option>
								<cfoutput query="attributes.data.qryStates" group="countryID">
									<optgroup label="#attributes.data.qryStates.country#">
										<cfoutput>
											<option value="#attributes.data.qryStates.stateid#" <cfif attributes.data.qryStates.stateid eq attributes.data.state>selected</cfif>>#attributes.data.qryStates.stateName# &nbsp;</option>
										</cfoutput>
									</optgroup>
								</cfoutput>
							</select>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repPostalCode" class="control-label ">Zip Code:</label>
						<div class="controls">
							<cfinput name="repPostalCode"  id="repPostalCode" type="text" maxlength="25" class="input-large" value="#attributes.data.repPostalCode#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repEmail" class="control-label ">E-mail:</label>
						<div class="controls">
							<cfinput name="repEmail"  id="repEmail" type="text" maxlength="255" class="input-large" value="#attributes.data.repEmail#"/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repVerifyEmail" class="control-label ">Verify E-mail:</label>
						<div class="controls">
							<cfinput name="repVerifyEmail"  id="repVerifyEmail" type="text" maxlength="255" class="input-large" value=""/>
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repHomePhone" class="control-label ">Home Phone ##:*</label>
						<div class="controls">
							<cfinput name="repHomePhone"  id="repHomePhone" type="text" maxlength="40" class="input-large" value="#attributes.data.repHomePhone#"/>
							<input type="hidden" name="repHomePhoneE164" id="repHomePhoneE164" value="#attributes.data.repHomePhoneE164#">
							<input type="hidden" name="repHomePhoneNational" id="repHomePhoneNational" value="#attributes.data.repHomePhone#">
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repCellPhone" class="control-label ">Cell Phone ##:</label>
						<div class="controls">
							<cfinput name="repCellPhone"  id="repCellPhone" type="text" maxlength="40" class="input-large" value="#attributes.data.repCellPhone#"/>
							<input type="hidden" name="repCellPhoneE164" id="repCellPhoneE164" value="#attributes.data.repCellPhoneE164#">
							<input type="hidden" name="repCellPhoneNational" id="repCellPhoneNational" value="#attributes.data.repCellPhone#">
						</div>
					</div>

					<div class="row-fluid control-group">
						<label for="repAlternatePhone" class="control-label ">Alternate Phone ##:</label>
						<div class="controls">
							<cfinput name="repAlternatePhone"  id="repAlternatePhone" type="text" maxlength="40" class="input-large" value="#attributes.data.repAlternatePhone#"/>
							<input type="hidden" name="repAlternatePhoneE164" id="repAlternatePhoneE164" value="#attributes.data.repAlternatePhoneE164#">
							<input type="hidden" name="repAlternatePhoneNational" id="repAlternatePhoneNational" value="#attributes.data.repAlternatePhone#">
						</div>
					</div>

					<div class="row-fluid control-group">
						<div class="controls">
							<i>At least one phone number or a valid email is required if Representative information is provided.</i>
						</div>				
					</div>
					<cfif attributes.data.qryActiveSMSTemplate.recordcount and attributes.data.qryActiveSMSTemplate.status eq 'A'>
						<div class="row-fluid control-group smsReferralRep">
							<label for="smsReferralRep" class="control-label ">Text Me My Referrals:</label>
							<div class="controls">
								<i>
								In addition to email, would you also like to receive your referral information via text? 
								Selecting phone numbers below indicates that you agree to receive your referral text messages from [[organizationName]]. You may reply STOP at any time to cancel.
								</i><br/><br/>
								<select class="input-large" name="smsRepNumbers" id="smsRepNumbers" multiple="multiple" >
									
								</select>
							</div>
						</div>
					</cfif>
				</div>
			</div>
		</div>

		<cfif attributes.data.qryGetLanguages.recordCount GT 1>
			<div class="well well-small"><b>Language</b></div>
			<div class="well well-small">
				<div class="row-fluid control-group">
					<label for="communicateLanguageID" class="control-label ">What is your preferred language?:</label>
					<div class="controls">
						<select name="communicateLanguageID" id="communicateLanguageID" class="input-large">
							<option value="">Select Language</option>
							<cfloop query="attributes.data.qryGetLanguages">
								<option value="#attributes.data.qryGetLanguages.languageID#" <cfif (attributes.data.qryGetLanguages.languageID eq attributes.data.communicateLanguageID) OR (not val(attributes.data.communicateLanguageID) and  val(attributes.data.qryGetLanguages.isDefault))>selected</cfif>>#attributes.data.qryGetLanguages.languageName#</option>
							</cfloop>
						</select>
					</div>
				</div>
			</div>	
		<cfelse>
			<input type="hidden" name="communicateLanguageID" id="communicateLanguageID" value="#attributes.data.qryGetLanguages.languageID#">
		</cfif>
		
		<div <cfif NOT attributes.data.dspLegalDescription>style="display:none" </cfif>>
			<div class="well well-small"><b>Legal Issue</b></div>
			<div class="well well-small">
				<cfif len(trim(attributes.data.feLegalDescInstructContent))>
					<div class="row-fluid control-group">				
						<div class="controls">
							#attributes.data.feLegalDescInstructContent#
						</div>				
					</div>
				</cfif>
				<div class="row-fluid control-group">
					<label for="issueDesc" class="control-label ">Description:</label>
					<div class="controls">
						<textarea name="issueDesc" id="issueDesc" style="width:90%;" rows="5" class="input-large">#ReReplace(attributes.data.issueDesc, "<[^<|>]+?>", "","ALL")#</textarea>
						<cfif val(attributes.data.feLegalDescLimitWords)>
							<div style="width:90%;">
								<div style="font-size:small;text-align:right;">Word Count:<span id="legalDescWordcount">0</span></div>
								<div id="legalDescWordLimitExceedAlert" class="alert alert-danger" style="display:none">#htmlEditFormat(attributes.data.feLegalDescLimitExceedMsg)#</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>

		<cfif attributes.data.feDspSurveyOption>
			<div class="well well-small"><b>Survey</b></div>
			<div class="well well-small">			
				<div class="row-fluid control-group">
					<label for="sendSurvey" class="control-label ">Would you like to receive surveys regarding the case?:</label>
					<div class="controls">
						<label for="sendSurvey"><input type="checkbox" name="sendSurvey" id="sendSurvey" value="1" class="input-large" <cfif attributes.data.sendSurvey OR attributes.data.feSurveyOptionDefaultYes>checked</cfif>> Yes</label>
					</div>
				</div>
			</div>
		</cfif>

		<cfif attributes.data.feDspBlogOption>
			<div class="well well-small"><b>Newsletters / Blogs</b></div>
			<div class="well well-small">			
				<div class="row-fluid control-group">
					<label for="sendNewsBlog" class="control-label ">Would you like to receive e-mails regarding Newsletters and/or Blog updates?:</label>
					<div class="controls">
						<label for="sendNewsBlog"><input type="checkbox" name="sendNewsBlog" id="sendNewsBlog" value="1"  class="input-large" <cfif attributes.data.sendNewsBlog>checked</cfif>> Yes</label>
					</div>
				</div>
			</div>	
		</cfif>		

		<div class="well well-small"><b>Filters</b></div>
		<div class="well well-small">		
		
			<cfif len(trim(attributes.data.fePanelInfoContent))>
				<div class="row-fluid control-group">				
					#attributes.data.fePanelInfoContent#							
				</div>	
			</cfif>
			<div class="row-fluid control-group">
				<div class="controls">
					* indicates a required field
				</div>
			</div>
			<div class="row-fluid control-group">
				<label for="panelid1" class="control-label ">Primary Panel:*</label>
				<div class="controls">
					<cfselect name="panelid1" id="panelid1" onclick="refreshDropDown('subpanelid1');"  class="input-large" >
						<option value="0">Select a Panel</option>
						<cfloop query="attributes.data.qryGetPanelsFilter">
							<cfif val(attributes.data.qryGetPanelsFilter.feDspClientReferral)>
								<option value="#attributes.data.qryGetPanelsFilter.panelID#" <cfif attributes.data.panelid1 is attributes.data.qryGetPanelsFilter.panelID>selected</cfif>>#attributes.data.qryGetPanelsFilter.name#</option>
							</cfif>
						</cfloop>
					</cfselect>
				</div>
			</div>

			<div class="row-fluid control-group">
				<label for="subpanelid1" class="control-label ">Sub-Panel</label>
				<div class="controls">
					<cfselect name="subpanelid1" id="subpanelid1" multiple="true"  class="input-large">
						<cfif val(attributes.data.panelid1)>
							<cfloop query="attributes.data.qryGetSubPanelsFilter1">
								<cfif val(attributes.data.qryGetSubPanelsFilter1.feDspClientReferral)>
									<option value="#attributes.data.qryGetSubPanelsFilter1.panelID#" <cfif listFind(attributes.data.subpanelid1,attributes.data.qryGetSubPanelsFilter1.panelID)>selected</cfif>>#attributes.data.qryGetSubPanelsFilter1.name#</option>
								</cfif>
							</cfloop>
						</cfif>				
					</cfselect>
				</div>
			</div>

			<div class="row-fluid control-group trPanelDesc">
				<div class="controls divPanelDesc">
					
				</div>
			</div>				
			
		</div>	
		<cfif ArrayLen(attributes.data.xmlFields.xmlRoot.xmlChildren) GT 0 || attributes.data.qryGetClassifications.recordCount GT 0>
			<div class="well well-small">	
				<cfif ArrayLen(attributes.data.xmlFields.xmlRoot.xmlChildren)>
					<div class="row-fluid control-group">
						<div class="controls">
							* indicates a required field
						</div>
					</div>		
					<cfloop array="#attributes.data.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
					
						<cfset local.thisFieldValue = evaluate("attributes.data.#local.thisfield.xmlattributes.fieldCode#") />
						<div class="row-fluid control-group">
							<label for="#local.thisfield.xmlattributes.fieldCode#" class="control-label ">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:<cfif local.thisfield.xmlattributes.isRequired is 1>*</cfif></label>
							<div class="controls">
								<cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
									<cfcase value="TEXTBOX">
										<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
											<cfset local.thisRadiusValue = evaluate("attributes.data.#local.thisfield.xmlattributes.fieldCode#_radius") />	
											Within 
											<cfselect class="tsAppBodyText" name="#local.thisfield.xmlattributes.fieldCode#_radius" id="#local.thisfield.xmlattributes.fieldCode#_radius" >
												<cfloop list="5,10,25,50,100" index="local.thisrad">
													<option value="#local.thisrad#" <cfif listFindNoCase(local.thisRadiusValue,local.thisrad)>selected="selected"</cfif>>#local.thisrad#</option>
												</cfloop>
											</cfselect>
											miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#
											<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off" class="input-large">
										<cfelse>
											<cfinput type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisFieldValue#" autocomplete="off" class="input-large">
										</cfif>
									</cfcase>
									<cfcase value="RADIO">
										<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
											<cfswitch expression="#local.thisfield.xmlattributes.dataTypeCode#">
											<cfcase value="STRING">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
											</cfcase>
											<cfcase value="DECIMAL2">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
											</cfcase>
											<cfcase value="INTEGER">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
											</cfcase>
											<cfcase value="DATE">
												<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
											</cfcase>
											<cfcase value="BIT">
												<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
											</cfcase>
											<cfdefaultcase>
												<cfset local.thisOptColValue = "">
											</cfdefaultcase>
											</cfswitch>
											<cfinput type="radio" name="#local.thisfield.xmlattributes.fieldCode#" class="input-large" id="#local.thisfield.xmlattributes.fieldCode#" value="#local.thisOptColValue#"><cfif local.thisfield.xmlattributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif><br/>
										</cfloop>
									</cfcase>
									<cfcase value="SELECT,CHECKBOX">
										<cfif local.thisfield.xmlattributes.allowMultiple>
											<cfsavecontent variable="variables.jQueryMultiselect">
												<cfoutput>
												<script type="text/javascript">
													$(function(){
														$("###local.thisfield.xmlattributes.fieldCode#").multiselect({
															header: "Choose options below",
															selectedList: 10,
															minWidth: 200
														});

														$('button.ui-multiselect').css('width',$('###local.thisfield.xmlattributes.fieldCode#').width());											
														$('div.ui-multiselect-menu').css('width',$('###local.thisfield.xmlattributes.fieldCode#').width());
														$('button.ui-multiselect').css('max-width','100%');											
														$('div.ui-multiselect-menu').css('max-width','100%');
													});
												</script>	
												</cfoutput>
											</cfsavecontent>
											<cfhtmlhead text="#application.objCommon.minText(variables.jQueryMultiselect)#">
										</cfif>
										<cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
											<cfset local.qryStates = application.objMember.getStates(attributes.data.orgID)>
											<cfselect class="input-large" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
												<option value=""></option>
												<cfoutput query="local.qryStates" group="countryID" >
													<optgroup label="#local.qryStates.country#">
													<cfoutput>
														<option value="#local.qryStates.statecode#">#local.qryStates.stateName# &nbsp;</option>
													</cfoutput>
													</optgroup>
												</cfoutput>
											</cfselect>
										<cfelseif listFindNoCase("m_recordtypeid,m_membertypeid,m_status", local.thisfield.xmlattributes.fieldCode)>
											<cfselect class="input-large" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" >
												<option value=""></option>
												<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
													<option value="#local.thisOpt.xmlAttributes.valueID#" <cfif listFindNoCase(local.thisFieldValue,local.thisOpt.xmlAttributes.valueID)>selected="selected"</cfif>>#local.thisOpt.xmlAttributes.columnValueString#</option>
												</cfloop>
											</cfselect>
										<cfelse>
											<cfselect class="input-large" name="#local.thisfield.xmlattributes.fieldCode#" id="#local.thisfield.xmlattributes.fieldCode#" multiple="#local.thisfield.xmlattributes.allowMultiple#" >
												<cfif not local.thisfield.xmlattributes.allowMultiple><option value=""></option></cfif>
												<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
													<cfswitch expression="#local.thisfield.xmlattributes.dataTypeCode#">
													<cfcase value="STRING">
														<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
														<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
													</cfcase>
													<cfcase value="DECIMAL2">
														<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
														<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
													</cfcase>
													<cfcase value="INTEGER">
														<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
														<cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
													</cfcase>
													<cfcase value="DATE">
														<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
														<cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
													</cfcase>
													<cfcase value="BIT">
														<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
														<cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
													</cfcase>
													<cfdefaultcase>
														<cfset local.thisOptColValue = "">
														<cfset local.thisOptColDisplay = "">
													</cfdefaultcase>
													</cfswitch>
													<option value="#local.thisOptColValue#" <cfif listFindNoCase(local.thisFieldValue,local.thisOptColValue)>selected="selected"</cfif>>#local.thisOptColDisplay#</option>
												</cfloop>
											</cfselect>
										</cfif>
									</cfcase>
									<cfcase value="DATE">
										<nobr>
										<cfinput class="input-large" type="text" name="#local.thisfield.xmlattributes.fieldCode#"  id="#local.thisfield.xmlattributes.fieldCode#" value="" >
										<cfinput type="button" name="btnClear#local.thisfield.xmlattributes.fieldCode#" class="btn btn-default" id="btnClear#local.thisfield.xmlattributes.fieldCode#" value="clear">
										</nobr>
										<cfsavecontent variable="local.datejs">
											<cfoutput>
												<script language="javascript">
													$(function() { 
														mca_setupDatePickerField('#local.thisfield.xmlattributes.fieldCode#');
														$("##btnClear#local.thisfield.xmlattributes.fieldCode#").click( function(e) { mca_clearDateRangeField('#local.thisfield.xmlattributes.fieldCode#');return false; } );
													});
												</script>
												<style type="text/css">
													###local.thisfield.xmlattributes.fieldCode# { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
												</style>
											</cfoutput>
										</cfsavecontent>
										<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
									</cfcase>
								</cfswitch>				
							</div>
						</div>
					</cfloop>
				</cfif>
				<cfif attributes.data.qryGetClassifications.recordCount>
					<cfloop query="attributes.data.qryGetClassifications">
						<cfif val(attributes.data.qryGetClassifications.allowSearch)>
							<div class="row-fluid control-group">
								<label for="mg_gid_#attributes.data.qryGetClassifications.groupSetID#" class="control-label "><cfif len(trim(attributes.data.qryGetClassifications.name))>#attributes.data.qryGetClassifications.name#<cfelse>#attributes.data.qryGetClassifications.groupSetName#</cfif>:</label>
								<div class="controls">
							
									<cfset local.qryGetGroupSetGroup = attributes.data.objAdminReferrals.getGroupSetGroup(attributes.data.qryGetClassifications.groupSetID) />
									<cfset local.thisGroupIDValue = evaluate("attributes.data.mg_gid_#attributes.data.qryGetClassifications.groupSetID#") />

									<select class="input-large" name="mg_gid_#attributes.data.qryGetClassifications.groupSetID#" id="mg_gid_#attributes.data.qryGetClassifications.groupSetID#" multiple="multiple" >
										<cfloop query="local.qryGetGroupSetGroup">
											<option value="#local.qryGetGroupSetGroup.groupsetGroupID#" <cfif listFind(local.thisGroupIDValue,local.qryGetGroupSetGroup.groupsetGroupID)>selected="selected"</cfif>>#local.qryGetGroupSetGroup.labelOverride#</option>
										</cfloop>
									</select>
								</div>
							</div>			
					
							<cfsavecontent variable="variables.jQueryMultiselectGroup">
								<cfoutput>
									<script type="text/javascript">
										$(function(){
											$("##mg_gid_#attributes.data.qryGetClassifications.groupSetID#").multiselect({
												header: "Choose options below",
												selectedList: 10,
												minWidth: 200
											});

											$('button.ui-multiselect').css('width',$('##mg_gid_#attributes.data.qryGetClassifications.groupSetID#').width());											
											$('div.ui-multiselect-menu').css('width',$('##mg_gid_#attributes.data.qryGetClassifications.groupSetID#').width());
											$('button.ui-multiselect').css('max-width','100%');											
											$('div.ui-multiselect-menu').css('max-width','100%');
										});
									</script>	
								</cfoutput>
							</cfsavecontent>
							<cfhtmlhead text="#application.objCommon.minText(variables.jQueryMultiselectGroup)#">			
						</cfif>
					</cfloop>
				</cfif>
			</div>
		</cfif>	

		<cfif attributes.data.extraInformation.hasFields>
			<input type="hidden" name="hasClientFields" id="hasClientFields" value="1">
			<div class="well well-small"><b>Extra Information</b></div>
			<div class="well well-small">			
				<div class="row-fluid control-group">
					#attributes.data.extraInformation.HTML#	
				</div>
			</div>	
		</cfif>
		<div class="row-fluid">
			<button type="submit" id="saveClientBtn2" class="saveClientBtn btn btn-default" >Submit Form</button>
			<button type="button" id="cancelBtn2" class="btn btn-default" onClick="parent.location.href='#attributes.data.mainurl#';">Cancel</button>
		</div>
	</div>
</cfform>

<div id="formSubmitting" style="display:none;text-align:center;margin:50px;">
	<i class="icon-spin icon-spinner icon-3x"></i> <b>Please wait while we process your application.</b>
</div>
</cfoutput>
<cfinclude template="/views/clientReferrals/commonTelJS.cfm">