<cfcomponent output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			this.siteCode = arguments.event.getValue('mc_siteInfo.siteCode');
			this.siteID = arguments.event.getValue('mc_siteInfo.siteid');
			this.orgID = arguments.event.getValue('mc_siteInfo.orgID');

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getSWBillingLogs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.searchValue = form['search[value]'] ?: '';
			local.programType = arguments.event.getValue('ft','');
			local.logType = arguments.event.getValue('logType','');
			local.showAssociation = val(arguments.event.getValue('showAssoc',0));
			local.fParticipantID = val(arguments.event.getValue('fParticipantID',0));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"billAmount #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"description #arguments.event.getValue('orderDir')#")>
		<cfif local.showAssociation>
			<cfset arrayAppend(local.arrCols,"orgCode #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"memberName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"dateCreated #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qrySWBillingLogs" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;
			CREATE TABLE ##tmpLogs (logID int PRIMARY KEY, logType varchar(50), billAmount decimal(18,2), description varchar(max),
				memberName varchar(200), orgCode varchar(10), dateCreated datetime, row int);

			DECLARE @programID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300), @nowDate datetime = GETDATE();
			SET @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pID',0)#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpLogs (logID, logType, billAmount, description, memberName, orgCode, dateCreated, row)
			SELECT logID, logType, billAmount, description, memberName, orgCode, dateCreated,
				ROW_NUMBER() OVER (ORDER BY #local.orderBy#) as row
			FROM (
				SELECT l.logID, l.logType, l.billAmount,
					l.billDesc +
					CASE WHEN l.logType IN ('setup','editing','syncing') THEN + ': ' + CAST(l.[minutes] as varchar(10)) + ' minutes logged.'
						ELSE ''
					END AS description,
					mActive.firstName + ' ' + mActive.lastName AS memberName, p.orgCode, l.dateCreated
				FROM dbo.tblSWBillingLogs AS l
				INNER JOIN dbo.tblParticipants as p on p.participantID = l.participantID
				INNER JOIN membercentral.dbo.ams_members as m on m.memberID = l.memberID
				INNER JOIN membercentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				WHERE 1=1
				<cfif local.fParticipantID gt 0>
					AND l.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fParticipantID#">
				</cfif>
				<cfif local.programType eq "SWB">
					AND l.bundleID = @programID
				<cfelseif listFindNoCase("SWL,SWOD", local.programType)>
					AND l.seminarID = @programID
				</cfif>
				<cfif len(local.logType)>
					AND l.logType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.logType#">
				</cfif>
				<cfif local.showAssociation>
					AND l.bundleID IS NULL
					AND l.seminarID IS NULL
					AND l.titleID IS NULL
				</cfif>
			) e
			<cfif len(local.searchValue)>
				WHERE description LIKE @searchValue
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT logID, logType, billAmount, description, memberName, orgCode, dateCreated, 
				CASE WHEN MONTH(dateCreated) = MONTH(@nowDate) AND YEAR(dateCreated) = YEAR(@nowDate) THEN 1 ELSE 0 END AS candelete,
				@totalCount AS totalCount
			FROM ##tmpLogs
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpLogs') IS NOT NULL
				DROP TABLE ##tmpLogs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qrySWBillingLogs.recordCount>
			<cfloop query="local.qrySWBillingLogs">
				<cfset local.tmpStr = {
					"logid": local.qrySWBillingLogs.logID,
					"logtype": local.qrySWBillingLogs.logType,
					"billamount": DollarFormat(local.qrySWBillingLogs.billAmount),
					"description": local.qrySWBillingLogs.description,
					"membername": local.qrySWBillingLogs.memberName,
					"sitecode": local.qrySWBillingLogs.orgCode,
					"datecreated": DateTimeFormat(local.qrySWBillingLogs.dateCreated,"m/d/yy h:nn tt") & " CT",
					"candelete": local.qrySWBillingLogs.candelete,
					"DT_RowId": "log_#local.qrySWBillingLogs.logID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySWBillingLogs.totalCount),
			"recordsFiltered": val(local.qrySWBillingLogs.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWAuthorsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			
			local.qryAuthorsList = CreateObject("component","seminarWebAuthors").getAuthorsFromFilters(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'),
										mode="grid", firstName=arguments.event.getValue('fFirstName',''), lastName=arguments.event.getValue('fLastName',''), 
										firm=arguments.event.getTrimValue('fFirm',''), authorType=arguments.event.getValue('fAuthorType',0),
										orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'),
										posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'), 
										showAllAssociations=arguments.event.getValue('showAllAssociations',0),
										participant=arguments.event.getValue('fParticipant',0));
		</cfscript>
		
		<cfset local.arrData = []>
		<cfloop query="local.qryAuthorsList">
			<cfset local.arrData.append({
				"authorid": local.qryAuthorsList.authorID,
				"displayname": local.qryAuthorsList.displayName,
				"firm": local.qryAuthorsList.firm,
				"orgcode": local.qryAuthorsList.orgcode,
				"authortype": local.qryAuthorsList.authorType,
				"linkedswlcount": local.qryAuthorsList.linkedSWLCount,
				"linkedswodcount": local.qryAuthorsList.linkedSWODCount,
				"candelete": NOT val(arguments.event.getValue('showAllAssociations',0)) AND val(local.qryAuthorsList.linkedSWLCount) EQ 0 AND val(local.qryAuthorsList.linkedSWODCount) EQ 0,
				"DT_RowId": "authorrow_#local.qryAuthorsList.authorID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryAuthorsList.totalCount),
			"recordsFiltered":  val(local.qryAuthorsList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWProgramAuthors" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.objSWAuthors = createObject("component","model.seminarweb.SWAuthors")>
		<cfset local.programType = arguments.event.getValue('ft','')>
		<cfset local.programID = arguments.event.getValue('pid',0)>
		<cfset local.authorType = arguments.event.getValue('atype','Speaker')>
		<cfset local.authorLabel = ucFirst(local.authorType)>

		<cfset local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isSeminarLocked(seminarID=local.programID)>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE saa
					SET authorOrder =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.tblSeminarsAndAuthors AS saa
					INNER JOIN dbo.tblAuthors AS a ON a.authorID = saa.authorID
					WHERE saa.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#"> 
					AND saa.authorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
					
				</cfquery>
			</cfloop>
		</cfif>
		<cfset local.qryAuthors = local.objSWAuthors.getAuthorsBySeminarID(seminarID=local.programID, authorType=local.authorType)>

		<cfif local.programType eq "SWL">
			<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.programID)>
			<cfquery name="local.qrySWLProvider" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select isOpen, providerID
				from dbo.tblSeminarsSWLive 
				where seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
			</cfquery>
		</cfif>
		
		<cfset local.data = []>
		<cfloop query="local.qryAuthors">
			<cfset local.tmpStr = {
				"speakerName": "#local.qryAuthors.lastname#, #local.qryAuthors.firstname##len(local.qryAuthors.suffix) GT 0 ? ' (#local.qryAuthors.suffix#)' : ''#",
				"authorID": local.qryAuthors.authorID,
				"programType": local.programType,
				"authorTypeID": local.qryAuthors.authorTypeID,
				"authorType": local.qryAuthors.authorType,
				"isSWProgramLocked": local.isSWProgramLocked,
				"authorLabel": local.authorLabel,
				"displayOnWebsite": YesNoFormat(local.qryAuthors.displayOnWebsite)
			}>

			<cfif local.programType eq "SWL">
				<cfset local.tmpStr['emailCount'] = local.qryAuthors.emailCount>
				<cfset local.tmpStr['isOpen'] = local.qrySWLProvider.isOpen>
				<cfif local.qrySWLProvider.isOpen>
					<cfif len(local.qryAuthors.SWLCode) and local.qrySWLProvider.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and len(local.qryAuthors.ZoomWebinarPanelistID)>
						<cfset local.tmpStr['authorIsSetup'] = true>
					<cfelse>
						<cfset local.tmpStr['authorIsSetup'] = false>
					</cfif>
					<cfset local.tmpStr['SWLCode'] = local.qryAuthors.SWLCode>
					<cfif local.qrySWLProvider.providerID is 3 and len(local.qrySeminar.ZoomWebinarID) and NOT len(local.qryAuthors.ZoomWebinarPanelistID)>
						<cfset local.tmpStr['needsSetup'] = true>
					<cfelse>
						<cfset local.tmpStr['needsSetup'] = false>
					</cfif>
				</cfif>
			</cfif>
			
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryAuthors.recordcount),
			"recordsFiltered": val(local.qryAuthors.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWBList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objSWB = CreateObject("component","model.admin.seminarweb.seminarwebSWB");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.keyWord = arguments.event.getTrimValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType','PO');
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.featuredOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.syndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);
			local.fIsSWOD = arguments.event.getValue('fIsSWOD','');
			local.fbundleID = arguments.event.getValue('fbundleID','');
			local.activatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.activatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.gridMode = arguments.event.getTrimValue('gridMode','grid');

			local.objSWB.saveSWBFilter(arguments.event);
			local.qryGetSWBList = local.objSWB.getPrograms(sitecode=this.siteCode, mode=local.gridMode,
				keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType, fIsSWOD=local.fIsSWOD, fbundleID=local.fbundleID, 
				activatedDateFrom=local.activatedDateFrom, activatedDateTo=local.activatedDateTo, hideInactive=local.hideInactive,
				featuredOnly=local.featuredOnly, fPublisher=arguments.event.getValue('fPublisher',''), syndicatedOnly=local.syndicatedOnly,
				orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'),
				posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'));
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetSWBList">
			<cfif structKeyExists(application.objSiteInfo.mc_siteInfo,local.qryGetSWBList.publisherOrgCode)>
				<cfset local.siteHostName = application.objSiteInfo.getSiteInfo(local.qryGetSWBList.publisherOrgCode).mainHostName>
			<cfelse>
				<cfset local.siteHostName = "">
			</cfif>
			<cfset local.arrData.append({
				"bundleID": local.qryGetSWBList.bundleID,
				"isActive": local.qryGetSWBList.isActive,
				"bundleName": encodeForHTML(local.qryGetSWBList.bundleName),
				"siteHostName": local.siteHostName,
				"isFeatured": local.qryGetSWBList.isFeatured,
				"bundleSubTitle": local.qryGetSWBList.bundleSubTitle,
				"dateActivated": DateFormat(local.qryGetSWBList.dateActivated,"m/d/yyyy"),
				"publisherOrgCode": local.qryGetSWBList.publisherOrgCode,
				"bundleType": local.qryGetSWBList.bundleType,
				"isSyndicated": local.qryGetSWBList.allowSyndication,
				"enrolledCount": val(local.qryGetSWBList.enrolledCount),
				"copyBundleRights": local.tmpRights.copyBundle,
				"hasManageSWBRegistrantsRights": (local.tmpRights.manageSWBRegistrantsSignUp is 1 OR local.tmpRights.manageSWBRegistrantsAll is 1),
				"lockSettings": local.qryGetSWBList.lockSettings,
				"gridMode": local.gridMode,
				"DT_RowId": "bundlerow_#local.qryGetSWBList.bundleID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryGetSWBList.totalCount),
			"recordsFiltered":  val(local.qryGetSWBList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getSWODList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objSWOD = CreateObject("component","model.admin.seminarweb.seminarwebSWOD");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.keyWord = arguments.event.getTrimValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType','PO');
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.featuredOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.syndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);
			local.gridMode = arguments.event.getTrimValue('gridMode','grid');
			local.activatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.activatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.origPublishDateFrom = arguments.event.getValue('fOrigPublishDateFrom','');
			local.origPublishDateTo = arguments.event.getValue('fOrigPublishDateTo','');
			local.createdDateFrom = arguments.event.getTrimValue('fCreatedDateFrom','');
			local.createdDateTo = arguments.event.getTrimValue('fCreatedDateTo','');

			local.objSWOD.saveSWODFilter(arguments.event);
			local.qryGetSWODList = local.objSWOD.getPrograms(sitecode=this.siteCode,
				mode=local.gridMode, keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType,
				hideInactive=local.hideInactive, featuredOnly=local.featuredOnly, createdDateFrom=local.createdDateFrom, createdDateTo=local.createdDateTo, 
				activatedDateFrom=local.activatedDateFrom, activatedDateTo=local.activatedDateTo, origPublishDateFrom=local.origPublishDateFrom, origPublishDateTo=local.origPublishDateTo, 
				fPublisher=arguments.event.getValue('fPublisher',''), syndicatedOnly=local.syndicatedOnly, fSeminarID=arguments.event.getValue('fSeminarID',''),
				orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'),
				posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'));
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetSWODList">
			<cfif structKeyExists(application.objSiteInfo.mc_siteInfo,local.qryGetSWODList.publisherOrgCode)>
				<cfset local.siteHostName = application.objSiteInfo.getSiteInfo(local.qryGetSWODList.publisherOrgCode).mainHostName>
			<cfelse>
				<cfset local.siteHostName = "">
			</cfif>

			<cfset local.arrData.append({
				"seminarID": local.qryGetSWODList.seminarID,
				"isPublished": local.qryGetSWODList.isPublished,
				"seminarName": encodeForHTML(local.qryGetSWODList.seminarName),
				"siteHostName": local.siteHostName,
				"dateActivated": dateFormat(local.qryGetSWODList.dateActivated,"m/d/yyyy"),
				"dateOrigPublished": dateFormat(local.qryGetSWODList.dateOrigPublished,"m/d/yyyy"),
				"isFeatured": local.qryGetSWODList.isFeatured,
				"seminarSubTitle": HTMLEditFormat(local.qryGetSWODList.seminarSubTitle),
				"publisherOrgCode": local.qryGetSWODList.publisherOrgCode,
				"enrolledCount": local.qryGetSWODList.enrolledCount,
				"isSyndicated": local.qryGetSWODList.allowSyndication,
				"manageSWODRegistrantsSignUp": local.tmpRights.manageSWODRegistrantsSignUp,
				"manageSWODRegistrantsAll": local.tmpRights.manageSWODRegistrantsAll,
				"copyProgramRights": local.tmpRights.copyProgram IS 1,
				"deleteProgramRights": local.tmpRights.deleteProgram IS 1,
				"lockSettings": local.qryGetSWODList.lockSettings,
				"gridMode": local.gridMode,
				"DT_RowId": "seminarrow_#local.qryGetSWODList.seminarID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryGetSWODList.totalCount),
			"recordsFiltered":  val(local.qryGetSWODList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWLList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objSWL = CreateObject("component","model.admin.seminarweb.seminarwebSWL");
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.objSWLiveSeminars = CreateObject("component","model.seminarweb.SWLiveSeminars");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.dateFrom = arguments.event.getValue('fDateFrom','');
			local.dateTo = arguments.event.getValue('fDateTo','');
			local.keyWord = arguments.event.getTrimValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType','PO');
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.featuredOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.syndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);

			local.objSWL.saveSWLFilter(arguments.event);
			local.qryGetSWLList = local.objSWL.getPrograms(sitecode=this.siteCode, mode="grid",
				dateFrom=local.dateFrom, dateTo=local.dateTo, keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType,
				hideInactive=local.hideInactive, featuredOnly=local.featuredOnly, orderby=arguments.event.getValue('orderBy'), syndicatedOnly=local.syndicatedOnly,
				direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'));
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>
		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(this.siteCode).qryAssociation>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetSWLList">
			<cfset local.parsedTime = local.objSWLiveSeminars.parseTimesFromWDDX(local.qryGetSWLList.wddxTimeZones,local.qryAssociation.wddxTimeZones,local.qryGetSWLList.dateStart,local.qryGetSWLList.dateEnd)>
			<cfset local.formattedDateStart = left(dayOfWeekAsString(dayOfWeek(local.parsedTime.startDate)), 3) & " " & dateFormat(local.parsedTime.startDate, "M/d/yy")>
			<cfset local.formattedTimeStart = timeFormat(local.parsedTime.startDate, "h:mm") & "-" & timeFormat(local.parsedTime.endDate, "h:mm tt")>
			
			<cfset local.arrData.append({
				"seminarID": local.qryGetSWLList.seminarID,
				"isPublished": local.qryGetSWLList.isPublished,
				"seminarName": encodeForHTML(local.qryGetSWLList.seminarName),
				"dateStart": dateFormat(local.qryGetSWLList.dateStart,"m/d/yyyy"),
				"formattedDateStart": local.formattedDateStart,
				"formattedTimeStart": local.formattedTimeStart,
				"isFeatured": local.qryGetSWLList.isFeatured,
				"seminarSubTitle": HTMLEditFormat(local.qryGetSWLList.seminarSubTitle),
				"publisherOrgCode": local.qryGetSWLList.publisherOrgCode,
				"enrolledCount": local.qryGetSWLList.enrolledCount,
				"isSyndicated": local.qryGetSWLList.allowSyndication,
				"manageSWLRegistrantsSignUp": local.tmpRights.manageSWLRegistrantsSignUp,
				"manageSWLRegistrantsAll": local.tmpRights.manageSWLRegistrantsAll,
				"copyProgramRights": local.tmpRights.copyProgram IS 1,
				"deleteProgramRights": local.tmpRights.deleteProgram IS 1,
				"lockSettings": local.qryGetSWLList.lockSettings,
				"DT_RowId": "swlProgramRow_#local.qryGetSWLList.seminarID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryGetSWLList.totalCount),
			"recordsFiltered":  val(local.qryGetSWLList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWLSchedulePrograms" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.dateFrom = dateformat(arguments.event.getTrimValue('fDateFrom',Now()),"m/d/yyyy")>
		<cfset local.dateTo = dateformat(arguments.event.getTrimValue('fDateTo',dateAdd("yyyy",1,Now())),"m/d/yyyy")>
		
		<cfstoredproc procedure="swl_getSeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.dateFrom#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.dateTo# 23:59:59">
			<cfprocresult name="local.qrySWLSchedulePrograms" resultset="1">
		</cfstoredproc>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateStart #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"orgcode #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"provider #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"seminarName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>
		<cfset local.posStartAndCount = arguments.event.getValue('posStart') + arguments.event.getValue('count')>

		<cfquery name="local.qryFilteredSWLSchedulePrograms" dbtype="query">
			select seminarID, orgcode, isPublished, seminarName, seminarSubTitle, dateStart, dateEnd, isNATLE, providerID, ACLicenseID, 
				enrollcount, provider, ZoomWebinarHostID
			from [local].qrySWLSchedulePrograms
			where 1 = 1
			<cfif len(arguments.event.getTrimValue('fPublisher',''))>
				and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fPublisher')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('fKeyword',''))>
				and (seminarName like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fKeyword')#%">
					or seminarSubTitle like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fKeyword')#%">)
			</cfif>
			<cfif len(arguments.event.getTrimValue('fSeminarID',''))>
				and seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getTrimValue('fSeminarID')))#">
			</cfif>
			<cfif len(local.searchValue)>
				and seminarName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('fZoomHostID',''))>
				and ZoomWebinarHostID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fZoomHostID')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('fIsNATLE',''))>
				and isNATLE = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('fIsNATLE')#">
			</cfif>
			ORDER BY #local.orderBy#
		</cfquery>
		
		<cfset local.arrZoomWebinarLicenses = createObject("component","model.seminarWeb.SWZoomWebinar").getWebinarLicenses()>
		<cfset local.strZoomLicenses = structNew()>
		<cfloop array="#local.arrZoomWebinarLicenses#" index="local.thisLicense">
			<cfset structInsert(local.strZoomLicenses, local.thisLicense.id, local.thisLicense.firstname)>
		</cfloop>

		<cfset local.arrData = []>
	
		<cfloop query="local.qryFilteredSWLSchedulePrograms" startRow="#(arguments.event.getValue('posStart') + 1)#" endRow="#local.posStartAndCount#" index="local.idx">
			<cfset local.dateStart = left(DayofWeekAsString(DayOfWeek(local.qryFilteredSWLSchedulePrograms.dateStart)),3) & ' ' & DateFormat(local.qryFilteredSWLSchedulePrograms.dateStart,"m/d/yy")>
			<cfset local.timeStart = TimeFormat(local.qryFilteredSWLSchedulePrograms.dateStart,"h:mm") & '-' & TimeFormat(local.qryFilteredSWLSchedulePrograms.dateEnd,"h:mm tt")>

			<cfset local.ZoomWebinarHostID = ''>
			<cfif val(local.qryFilteredSWLSchedulePrograms.providerID) gt 0>
				<cfswitch expression="#local.qryFilteredSWLSchedulePrograms.providerID#">
					<cfcase value="3">
						<cfif structKeyExists(local.strZoomLicenses,local.qryFilteredSWLSchedulePrograms.ZoomWebinarHostID)>
							<cfset local.ZoomWebinarHostID = local.strZoomLicenses[local.qryFilteredSWLSchedulePrograms.ZoomWebinarHostID]>
						<cfelse>
							<cfset local.ZoomWebinarHostID = 'former'>
						</cfif>
					</cfcase>
				</cfswitch>
			</cfif>
			
			<cfset local.arrData.append({
				"seminarID": local.qryFilteredSWLSchedulePrograms.seminarID,
				"dateStart": local.dateStart,
				"timeStart": local.timeStart,
				"orgcode" : local.qryFilteredSWLSchedulePrograms.orgcode,
				"isNATLE" : local.qryFilteredSWLSchedulePrograms.isNATLE,
				"providerID" : local.qryFilteredSWLSchedulePrograms.providerID,
				"provider" : local.qryFilteredSWLSchedulePrograms.provider,
				"ZoomWebinarHostID" : local.ZoomWebinarHostID,
				"seminarName": encodeForHTML(local.qryFilteredSWLSchedulePrograms.seminarName),
				"seminarSubTitle" : local.qryFilteredSWLSchedulePrograms.seminarSubTitle,
				"enrollcount": local.qryFilteredSWLSchedulePrograms.enrollcount,
				"isPublished": local.qryFilteredSWLSchedulePrograms.isPublished,
				"DT_RowId": "swlProgramRow_#local.qryFilteredSWLSchedulePrograms.seminarID#"
			})>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryFilteredSWLSchedulePrograms.recordCount),
			"recordsFiltered":  val(local.qryFilteredSWLSchedulePrograms.recordCount),
			"data": local.arrData
		}>
		
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWSubjectAreasList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			
			local.qryOrgCategories = CreateObject("component","model.seminarweb.SWCategories").getCategoriesByOrgCode(orgCode=this.siteCode);
		</cfscript>

		<cfquery name="local.qryFilteredCategories" dbtype="query">
			SELECT categoryID, categoryName, SWLCount, SWODCount
			FROM [local].qryOrgCategories 
			<cfif len(trim(local.searchValue))>
				WHERE categoryName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#trim(local.searchValue)#%">
			</cfif>
			ORDER BY categoryName #arguments.event.getValue('orderDir')#
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryFilteredCategories" startrow="#arguments.event.getValue('posStart')+1#" endrow="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			<cfset local.arrData.append({
				"categoryID": local.qryFilteredCategories.categoryID,
				"categoryName": htmleditformat(local.qryFilteredCategories.categoryName),
				"SWLCount": val(local.qryFilteredCategories.SWLCount),
				"SWODCount": val(local.qryFilteredCategories.SWODCount),
				"DT_RowId": "swSubjectArea_#local.qryFilteredCategories.categoryID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryFilteredCategories.recordCount,
			"recordsFiltered": local.qryFilteredCategories.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getParticipants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"p.orgcode #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"tla.Description #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryParticipantList" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpParticipantList') IS NOT NULL
					DROP TABLE ##tmpParticipantList;
				CREATE TABLE ##tmpParticipantList (participantID int PRIMARY KEY, tlaorgcode varchar(10), tladescription varchar(255), handlesOwnPayment bit,
					isSWL bit, isSWOD bit, row int);

				DECLARE @totalCount INT, @posStart INT, @posStartAndCount INT;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			
				INSERT INTO ##tmpParticipantList (participantID, tlaorgcode, tladescription, handlesOwnPayment, isSWL, isSWOD, row)
				SELECT p.participantID, p.orgcode, tla.Description, p.handlesOwnPayment, p.isSWL, p.isSWOD, ROW_NUMBER() OVER (ORDER BY #local.orderby#)
				FROM dbo.tblParticipants AS p 
				INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode
				INNER JOIN membercentral.dbo.sites as s on s.siteCode = p.orgcode
				WHERE p.isActive = 1
				<cfif len(arguments.event.getTrimValue('fSiteCode',''))>
					AND p.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fSiteCode')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('fHandlesOwnPayment',''))>
					AND p.handlesOwnPayment IN (<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('fHandlesOwnPayment')#" list="true">)
				</cfif>
				<cfif len(arguments.event.getTrimValue('fAssociation',''))>
					AND tla.Description LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fAssociation')#%">
				</cfif>
				<cfif len(arguments.event.getTrimValue('fSWType',''))>
					AND ( 1 = 0
					<cfif listFindNoCase(arguments.event.getTrimValue('fSWType'),'SWL')>
						OR p.isSWL = 1
					</cfif>
					<cfif listFindNoCase(arguments.event.getTrimValue('fSWType'),'SWOD')>
						OR p.isSWOD = 1
					</cfif>
					)
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT participantID, tlaorgcode, tladescription, handlesOwnPayment, isSWL, isSWOD, @totalCount AS totalCount
				FROM ##tmpParticipantList
				WHERE row > @posStart 
				AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpParticipantList') IS NOT NULL
					DROP TABLE ##tmpParticipantList;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryParticipantList">
			<cfset local.arrData.append({
				"tlaorgcode": UCASE(local.qryParticipantList.tlaorgcode),
				"tladescription": local.qryParticipantList.tladescription,
				"handlesOwnPayment": local.qryParticipantList.handlesOwnPayment,
				"isSWL": local.qryParticipantList.isSWL,
				"isSWOD": local.qryParticipantList.isSWOD,
				"activeParticipant": structKeyExists(application.objSiteInfo.mc_siteInfo,local.qryParticipantList.tlaorgcode)
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryParticipantList.totalcount),
			"recordsFiltered":  val(local.qryParticipantList.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getlistNATLEOptIns" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols," swl.dateStart #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"p.orgcode #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"s.seminarName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryOptInList" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpOptInList') IS NOT NULL
					DROP TABLE ##tmpOptInList;
				CREATE TABLE ##tmpOptInList(seminarID int PRIMARY KEY, seminarName varchar(255), seminarSubTitle varchar(255), dateStart datetime, 
					publisher varchar(10), participants varchar(max), row int);

				DECLARE @totalCount INT, @posStart INT, @posStartAndCount INT, @searchValue varchar(300);
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>

				INSERT INTO ##tmpOptInList(seminarID, seminarName, seminarSubTitle, dateStart, publisher, participants,row)			
				SELECT s.seminarID, s.seminarName, s.seminarSubTitle, swl.dateStart, p.orgcode AS publisher, participants = REPLACE( 
					(
					SELECT DISTINCT p2.orgcode AS [data()] 
					FROM dbo.tblSeminarsOptIn AS o
					INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = o.participantID AND o.IsActive = 1
					WHERE o.seminarID = s.seminarID
					ORDER BY p2.orgcode
					FOR XML PATH ('') 
					), ' ', ', ') ,ROW_NUMBER() OVER (ORDER BY #local.orderby#)
				FROM dbo.tblSeminarsSWLive AS swl
				INNER JOIN dbo.tblSeminars AS s ON s.seminarID = swl.seminarID
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				WHERE swl.dateStart between <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('rptStart')#"> 
				AND <cfqueryparam cfsqltype="CF_SQL_timestamp" value="#arguments.event.getValue('rptEnd')# 23:59">
				AND swl.isNATLE = 1
				AND s.isPublished = 1
				<cfif len(local.searchValue)>
					AND (s.seminarName LIKE @searchValue OR p.orgcode LIKE @searchValue OR swl.dateStart LIKE @searchValue)
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.dateStart, tmp.publisher, tmp.participants,
					COUNT(p.participantID) AS participantCount, tmp.row, @totalCount AS totalCount
				FROM ##tmpOptInList tmp
				INNER JOIN dbo.tblSeminarsOptIn AS o ON o.seminarID = tmp.seminarID AND o.IsActive = 1
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = o.participantID
				WHERE tmp.row > @posStart 
				AND tmp.row <= @posStartAndCount
				GROUP BY tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.dateStart, tmp.publisher, tmp.participants, tmp.row
				ORDER BY tmp.row;

				IF OBJECT_ID('tempdb..##tmpOptInList') IS NOT NULL
					DROP TABLE ##tmpOptInList;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>

		<cfloop query="local.qryOptInList">
			<cfset local.arrData.append({
				"strDate": replace(DateTimeFormat(local.qryOptInList.dateStart,"m/d/yy h:nn tt"),":00",""),
				"seminarid": local.qryOptInList.seminarID,
				"seminarname": local.qryOptInList.seminarName,
				"seminarsubtitle": local.qryOptInList.seminarSubTitle,
				"publisher": UCASE(local.qryOptInList.publisher),
				"participantCount": local.qryOptInList.participantCount,
				"participants": local.qryOptInList.participants
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryOptInList.totalcount),
			"recordsFiltered": val(local.qryOptInList.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getCreditAuthorities" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			arguments.event.setValue('searchValue',local.searchValue);
		</cfscript>

		<cfset local.qryCreditAuthorities = createObject("component","seminarWebSWCredits").getCreditAuthoritiesForGrid(event=arguments.event)>

		<cfset local.arrData = []>
		<cfloop query="local.qryCreditAuthorities">
			<cfset local.arrData.append({
				"authorityName": local.qryCreditAuthorities.authorityName,
				"code": local.qryCreditAuthorities.code,
				"authorityID": local.qryCreditAuthorities.authorityID,
				"jurisdiction": local.qryCreditAuthorities.jurisdiction
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryCreditAuthorities.totalcount),
			"recordsFiltered":  val(local.qryCreditAuthorities.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCreditSponsors" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols," sponsorName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"orgcode #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryCreditSponsors" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpCreditSponsors') IS NOT NULL
					DROP TABLE ##tmpCreditSponsors;
				CREATE TABLE ##tmpCreditSponsors (sponsorID INT, sponsorName VARCHAR(200), linkedOrgCode VARCHAR(20), row INT);

				DECLARE @totalCount INT, @posStart INT, @posStartPlusCount INT, @searchValue varchar(300);
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>
				
				INSERT INTO ##tmpCreditSponsors (sponsorID, sponsorName, linkedOrgCode, row)
				SELECT sponsorID, sponsorName, orgcode, ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
				FROM dbo.tblCreditSponsors
				<cfif len(local.searchValue)>
					WHERE (sponsorName LIKE @searchValue OR orgcode LIKE @searchValue)
				</cfif>;

				SET @totalCount = @@ROWCOUNT;

				SELECT sponsorID, sponsorName, linkedOrgCode, @totalCount AS totalCount
				FROM ##tmpCreditSponsors
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpCreditSponsors') IS NOT NULL
					DROP TABLE ##tmpCreditSponsors;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryCreditSponsors">
			<cfset local.arrData.append({
				"sponsorName": local.qryCreditSponsors.sponsorName,
				"linkedOrgCode": local.qryCreditSponsors.linkedOrgCode,
				"sponsorID": local.qryCreditSponsors.sponsorID
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryCreditSponsors.totalcount),
			"recordsFiltered":  val(local.qryCreditSponsors.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWLReminders" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfstoredproc procedure="sw_getReminders" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qrySWLReminders" resultset="1">
		</cfstoredproc>	

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateStart #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"SeminarName #arguments.event.getValue('orderDir')#, seminarSubTitle #arguments.event.getValue('orderDir')#, typeDesc #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>
		<cfset local.posStartAndCount = arguments.event.getValue('posStart') + arguments.event.getValue('count')>
		
		<cfquery name="local.qryFilteredSWLReminders" dbtype="query">
			SELECT scheduledTaskID, seminarID, orgcode, dateStart, seminarName, seminarSubTitle, typeDesc, 
			isRegistrantInstructionsEnabled, registrantSelectedTimeframes, isSpeakerInstructionsEnabled,
			speakerSelectedTimeframes, isWebinarMaterialEnabled, webinarMaterialSelectedTimeframes
			FROM [local].qrySWLReminders
			<cfif len(local.searchValue)>
				WHERE (seminarName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">
						OR seminarSubTitle LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">
						OR typeDesc LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">)
			</cfif>
			ORDER BY #local.orderBy#
		</cfquery>

		<cfset local.arrData = []>

		<cfloop query="local.qryFilteredSWLReminders"  startRow="#(arguments.event.getValue('posStart') + 1)#" endRow="#local.posStartAndCount#">
			
			<cfset local.arrData.append({
				"seminarID":local.qryFilteredSWLReminders.seminarID,
				"orgcode":local.qryFilteredSWLReminders.orgcode,
				"reminderID": local.qryFilteredSWLReminders.scheduledTaskID,
				"dateStart": DateTimeFormat(local.qryFilteredSWLReminders.dateStart,"m/d/yy h:nn tt"),
				"SeminarName": encodeForHTML(local.qryFilteredSWLReminders.seminarName),
				"seminarSubTitle" : local.qryFilteredSWLReminders.seminarSubTitle,
				"typeDesc" : local.qryFilteredSWLReminders.typeDesc,
				"isRegistrantInstructionsEnabled" : local.qryFilteredSWLReminders.isRegistrantInstructionsEnabled,
				"registrantSelectedTimeframes" : local.qryFilteredSWLReminders.registrantSelectedTimeframes,
				"isSpeakerInstructionsEnabled" : local.qryFilteredSWLReminders.isSpeakerInstructionsEnabled,
				"speakerSelectedTimeframes" : local.qryFilteredSWLReminders.speakerSelectedTimeframes,
				"isWebinarMaterialEnabled" : local.qryFilteredSWLReminders.isWebinarMaterialEnabled,
				"webinarMaterialSelectedTimeframes" : local.qryFilteredSWLReminders.webinarMaterialSelectedTimeframes,
				"DT_RowId": "swlReminderRow_#local.qryFilteredSWLReminders.seminarID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qrySWLReminders.recordCount),
			"recordsFiltered":  val(local.qrySWLReminders.recordCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWCPList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.gridMode = arguments.event.getTrimValue('gridMode','grid');
			local.keyWord = arguments.event.getTrimValue('fKeyword','');
			local.publisher = arguments.event.getValue('fPublisher','');
			local.certProgramID = arguments.event.getValue('fCertProgramID','');

			local.qryGetSWCPList = CreateObject("component","model.admin.seminarWeb.seminarwebSWCP").getPrograms(sitecode=this.siteCode, mode=local.gridMode, 
				keyword=local.keyWord, orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'),
				count=arguments.event.getValue('count'), fCertProgramID=local.certProgramID, fPublisher=local.publisher);
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetSWCPList">
			<cfif structKeyExists(application.objSiteInfo.mc_siteInfo,local.qryGetSWCPList.publisherOrgCode)>
				<cfset local.siteHostName = application.objSiteInfo.getSiteInfo(local.qryGetSWCPList.publisherOrgCode).mainHostName>
			<cfelse>
				<cfset local.siteHostName = "">
			</cfif>

			<cfset local.arrData.append({
				"programID": local.qryGetSWCPList.programID,
				"programName": local.qryGetSWCPList.programName,
				"publisherOrgCode": local.qryGetSWCPList.publisherOrgCode,
				"siteHostName": local.siteHostName,
				"enrolledCount": val(local.qryGetSWCPList.enrolledCount),
				"manageSWCPRegistrantsPublish": local.tmpRights.manageSWCPRegistrantsPublish,
				"manageSWCPRegistrantsAll": local.tmpRights.manageSWCPRegistrantsAll,
				"copyProgramRights": local.tmpRights.copySWCPProgram IS 1,
				"gridMode": local.gridMode,
				"DT_RowId": "titleRow_#local.qryGetSWCPList.programID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryGetSWCPList.totalCount),
			"recordsFiltered":  val(local.qryGetSWCPList.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWLinks" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.swType = arguments.event.getValue('swType','');
			local.programID = arguments.event.getValue('pid',0);
		</cfscript>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE sal
					SET sal.linkOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.tblSeminarsAndLinks sal
					INNER JOIN dbo.tblSeminars AS s ON sal.seminarID = s.seminarID
						AND s.isDeleted = 0
					WHERE sal.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
					AND linkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfstoredproc procedure="sw_getSeminarLinks" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
			<cfprocresult name="local.qryLinks" resultset="1">
		</cfstoredproc>
		
		<cfset local.arrData = []>

		<cfloop query="local.qryLinks">
			<cfset local.arrData.append({
				"linkID": local.qryLinks.linkID,
				"linkName": encodeForHTML(local.qryLinks.linkName),
				"linkDesc": encodeForHTML(local.qryLinks.linkDesc),
				"linkURL": local.qryLinks.linkURL,
				"purchaseURL": local.qryLinks.purchaseURL,
				"linkOrder" : local.qryLinks.linkOrder,
				"DT_RowId": "swLinksRow_#local.qryLinks.linkID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryLinks.recordCount),
			"recordsFiltered":  val(local.qryLinks.recordCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="listSWCategories" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"categoryName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>
		<cfset local.posStartAndCount = arguments.event.getValue('posStart') + arguments.event.getValue('count')>

		<cfset local.objSWCategories = createObject("component","model.seminarweb.SWCategories")>
		<cfset local.programType = arguments.event.getValue('ft','')>
		<cfset local.programID = arguments.event.getValue('pid',0)>

		<cfif listFindNoCase("SWL,SWOD",local.programType)>
			<cfset local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isSeminarLocked(seminarID=local.programID)>
		</cfif>

		<cfif listFindNoCase("SWL,SWOD", local.programType)>
			<cfset local.qryLinkedCategories = local.objSWCategories.getCategoriesBySeminarID(seminarID=local.programID)>
		</cfif>

		<cfquery name="local.qryFilteredLinkedCategories" dbtype="query">
			SELECT categoryID, categoryName
			FROM [local].qryLinkedCategories
			<cfif len(local.searchValue)>
				WHERE categoryName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">
			</cfif>
			ORDER BY #local.orderBy#
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryFilteredLinkedCategories" startRow="#(arguments.event.getValue('posStart') + 1)#" endRow="#local.posStartAndCount#" index="local.idx">
			<cfset arrayAppend(local.data, {
				"categoryID": local.qryFilteredLinkedCategories.categoryID,
				"categoryName": local.qryFilteredLinkedCategories.categoryName,
				"isSWProgramLocked": local.isSWProgramLocked
			})>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryLinkedCategories.recordcount),
			"recordsFiltered": val(local.qryFilteredLinkedCategories.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWCPRegistrants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			local.gridmode = arguments.event.getTrimValue('gridmode','grid');
		
			if (local.gridmode eq "reggrid") {
				local.memberID = int(val(arguments.event.getTrimValue('mid',0)));
				local.qryMember = application.objMember.getMemberInfo(memberID=local.memberID, orgID=this.orgID);

				local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWCP").getRegistrants(sitecode=this.siteCode, 
					mode=local.gridmode, programID=0, memberID=local.qryMember.memberID, orderby=arguments.event.getValue('orderBy'), 
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'), 
					count=arguments.event.getValue('count'), searchValue=local.searchValue);

			} else {
				local.programID = int(val(arguments.event.getTrimValue('pid',0)));

				local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWCP").getRegistrants(sitecode=this.siteCode, 
					mode=local.gridmode, programID=local.programID, memberID=0, orderby=arguments.event.getValue('orderBy'), 
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'), 
					count=arguments.event.getValue('count'), searchValue=local.searchValue);
			}
		</cfscript>
		
		<cfset local.data = []>
		<cfloop query="local.qryEnrollments">
			<cfif listValueCount(local.qryEnrollments.semStatusString,1) gt 0>
				<cfset local.statusText = 'In Progress'>
			<cfelseif listValueCount(local.qryEnrollments.semStatusString,3) gt 0>
				<cfset local.statusText = 'Failure'>
			<cfelse>
				<cfset local.statusText = 'Success'>
			</cfif>
			<cfset local.progressVal = "(#listValueCount(local.qryEnrollments.semStatusString,2)#/#listLen(local.qryEnrollments.semStatusString)#)">
			<cfif local.gridmode eq "reggrid" >
				<cfset arrayAppend(local.data, {
					"programID": local.qryEnrollments.programID,
					"depomemberdataid": local.qryEnrollments.depomemberdataid,
					"programName": local.qryEnrollments.programName,
					"statusText": local.statusText,
					"progressVal": local.progressVal,
					"progressBar": '',
					"canView":listValueCount(local.qryEnrollments.semStatusString,2) is listLen(local.qryEnrollments.semStatusString),
					"semStatusString": local.qryEnrollments.semStatusString,
					"gridmode": local.gridmode
				})>
			<cfelse>
				<cfif len(local.qryEnrollments.membernumber)>
					<cfset local.memberID = application.objMember.getMemberIDByMemberNumber(memberNumber=local.qryEnrollments.membernumber, orgID=this.orgID)>
				</cfif>
				<cfif len(local.qryEnrollments.membernumber)>
					<cfset local.memberName = "#local.qryEnrollments.lastname#, #local.qryEnrollments.firstname# (#local.qryEnrollments.membernumber#)">
				<cfelse>
					<cfset local.memberName = "#local.qryEnrollments.lastname#, #local.qryEnrollments.firstname#">
				</cfif>
				<cfset local.progressBar = ''>
				<cfif listLen(local.qryEnrollments.semStatusString) lte 10> 
					<cfloop list="#local.qryEnrollments.semStatusString#" index="local.stat">
						<cfif local.stat is 3>
							<cfset local.className = "bg_danger">
						<cfelseif local.stat is 2>
							<cfset local.className = "bg-success">
						<cfelse>
							<cfset local.className = "bg-white">
						</cfif>
						<cfset local.progressBar &= '<span class="swcpstat #local.className#">.</span>'>
					</cfloop>
				</cfif>
				<cfset arrayAppend(local.data, {
					"programID": local.qryEnrollments.programID,
					"programName": '',
					"depomemberdataid": local.qryEnrollments.depomemberdataid,
					"membernumber": local.qryEnrollments.membernumber,
					"memberName": local.memberName,
					"company": local.qryEnrollments.company,
					"statusText": local.statusText,
					"memberID": local.memberID,
					"semStatusString": local.qryEnrollments.semStatusString,
					"progressVal": local.progressVal,
					"progressBar": local.progressBar,
					"canView":listValueCount(local.qryEnrollments.semStatusString,2) is listLen(local.qryEnrollments.semStatusString),
					"gridmode": local.gridmode
				})>
			</cfif>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEnrollments.totalCount),
			"recordsFiltered": val(local.qryEnrollments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWCPIncludedSeminars" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		
			local.programID = int(val(arguments.event.getTrimValue('pid',0)));
			local.qryItems = createObject("component","model.seminarweb.SWCertPrograms").getItems(programID=local.programID);
		</cfscript>
		
		<cfset local.data = []>
		<cfloop query="local.qryItems">
			<cfset arrayAppend(local.data, {
				"itemID":local.qryItems.itemID,
				"contentID":local.qryItems.contentID,
				"contentType":local.qryItems.contentType,
				"contentName":local.qryItems.contentName,
				"contentPublished": local.qryItems.contentPublished,
				"reqAwardCredit":len(local.qryItems.seminarCreditID) GT 0 ? 'Yes - #local.qryItems.authcode#' : 'No',
				"canmoveup":local.qryItems.itemOrder neq 1,
				"canmovedown": local.qryItems.itemOrder neq local.qryItems.recordCount,
				"itemOrder":local.qryItems.itemOrder
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryItems.recordCount),
			"recordsFiltered": val(local.qryItems.recordCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWBRegistrants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.bundleID = int(val(arguments.event.getTrimValue('pid',0)));
			local.rDateFrom = arguments.event.getValue('rDateFrom','');
			local.rDateTo = arguments.event.getValue('rDateTo','');	
		</cfscript>

		<cfset local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=local.bundleID, orgcode=this.siteCode)>
		<cfset local.assocHandlesOwnPayment = CreateObject("component","seminarWebSWCommon").doesAssociationHandlesPayment(orgcode=this.siteCode)>
		<cfset local.qryEnrollments = CreateObject("component","seminarwebSWB").getRegistrants(sitecode=this.siteCode, 
			bundleID=local.bundleID, rdateFrom=local.rDateFrom, rdateTo=local.rDateTo, orderby=arguments.event.getValue('orderBy'), 
			direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'))>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>

		<cfset local.data = []>
		<cfloop query="local.qryEnrollments">
			<cfif local.qryBundle.isSWOD>
				<cfset local.hasDeleteRegistrantRights = (local.tmpRights.deleteSWODRegistrantSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.deleteSWODRegistrantAll is 1>
			<cfelse>
				<cfset local.hasDeleteRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.deleteSWLRegistrantAll is 1>
			</cfif>

			<cfset arrayAppend(local.data, {
				"orderid": local.qryEnrollments.orderID,
				"memberid": local.qryEnrollments.memberid,
				"membernumber": local.qryEnrollments.memberNumber,
				"lastname": local.qryEnrollments.lastname,
				"firstname": local.qryEnrollments.firstname,
				"company": local.qryEnrollments.company,
				"dateoforder": dateFormat(local.qryEnrollments.dateOfOrder,"m/d/yyyy"),
				"seminarcount": local.qryEnrollments.seminarCount,
				"amountbilled": dollarFormat(local.qryEnrollments.amountBilled),
				"amountdue": dollarFormat(local.qryEnrollments.amountDue),
				"canchangeprice": NOT local.assocHandlesOwnPayment AND NOT local.qryEnrollments.handlesOwnPayment AND local.hasDeleteRegistrantRights,
				"candelete": local.hasDeleteRegistrantRights,
				"assocHandlesOwnPayment": local.assocHandlesOwnPayment,
				"DT_RowId": "row_#local.qryEnrollments.orderID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEnrollments.totalCount),
			"recordsFiltered": val(local.qryEnrollments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getSeminarForms" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE saf
					SET saf.orderBy = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.tblSeminarsAndForms saf
					INNER JOIN dbo.tblSeminars AS s ON saf.seminarID = s.seminarID
						AND s.isDeleted = 0
					WHERE saf.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
					AND seminarFormID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfquery name="local.qryForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT saf.seminarFormID, saf.formid, saf.orderby, saf.isRequired, saf.loadpoint, f.formtitle, 
				f.passingPct, count(sfr.sfrID) as NumResponses
			FROM dbo.tblSeminarsAndForms as saf
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = saf.formid and f.isDeleted = 0
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses sfr
				INNER JOIN formbuilder.dbo.tblResponses as fbr on fbr.responseID = sfr.responseID
					and fbr.isActive = 1
				on sfr.seminarFormID = saf.seminarFormID
			WHERE saf.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
			AND saf.loadpoint = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('loadPoint','')#">
			GROUP BY saf.seminarFormID, saf.formid, saf.orderby, saf.isRequired, saf.loadpoint, f.formtitle, f.passingPct
			ORDER BY saf.orderBy;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isSeminarLocked(seminarID=arguments.event.getValue('sid',0))>

		<cfset local.arrData = []>
		<cfloop query="local.qryForms">
			<cfset local.arrData.append({
				"seminarFormID": local.qryForms.seminarFormID,
				"formTitle": encodeForHTML(local.qryForms.formTitle),
				"loadpoint" : ucFirst(local.qryForms.loadpoint,false,false),
				"numResponses" : local.qryForms.NumResponses,
				"orderby" : local.qryForms.orderby,
				"canmoveup" : local.qryForms.orderBy NEQ 1,
				"canmovedown" : local.qryForms.orderby NEQ local.qryForms.recordCount,
				"isSWProgramLocked" : local.isSWProgramLocked,
				"DT_RowId": "seminarFormRow_#local.qryForms.seminarFormID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryForms.recordCount,
			"recordsFiltered": local.qryForms.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWBIncludedItems" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE bi
					SET bi.itemOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.tblBundledItems bi
					INNER JOIN dbo.tblSeminars AS s ON bi.seminarID = s.seminarID
						AND s.isDeleted = 0
					WHERE bi.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('bundleID',0)#">
					AND itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfset local.isBundleProgramLocked = CreateObject("seminarWebSWCommon").isBundleProgramLocked(bundleID=arguments.event.getValue('bundleID',0))>
		<cfset local.qryItems = createObject("component","seminarWebSWB").getBundledItems(bundleID=arguments.event.getValue('bundleID',0))>
		<cfset local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=arguments.event.getValue('bundleID',0), orgcode=this.siteCode)>

		<cfset local.data = []>
		<cfloop query="local.qryItems">
			<!-- Initialize the base object -->
			<cfset local.item = {
				"itemID": local.qryItems.itemID,
				"contentPublished": local.qryItems.contentPublished,
				"contentType": local.qryItems.contentType,
				"contentID": local.qryItems.contentID,
				"contentName": local.qryItems.contentName,
				"contentSubTitle": local.qryItems.contentSubTitle,
				"isBundleProgramLocked": local.isBundleProgramLocked,
				"lockSettings": local.qryItems.lockSettings
			}>
		
			<!-- Add SWOD-specific details if applicable -->
			<cfif local.qryBundle.isSWOD>
				<cfset local.item["catalogSaleExpired"] = local.qryItems.catalogSaleExpired ? 'Yes' : 'No'>
				<cfset local.item["catalogSaleDetails"] = NOT LEN(local.qryItems.dateCatalogEnd) 
					? 'Not Sold in Catalog' 
					: (
						local.qryItems.catalogSaleExpired
						? 'Expired on: #DateFormat(local.qryItems.dateCatalogEnd, "m/d/yy")#' 
						: 'Will Expire on: #DateFormat(local.qryItems.dateCatalogEnd, "m/d/yy")#'
					)>
				<cfset local.item["creditExpired"] = local.qryItems.creditExpired ? 'Yes' : 'No'>
				<cfset local.item["creditDetails"] = NOT LEN(local.qryItems.MaxCreditCompleteByDate) 
					? 'No Credit Offered' 
					: (
						local.qryItems.creditExpired 
						? 'Expired on: #DateFormat(local.qryItems.MaxCreditCompleteByDate, "m/d/yy")#'
						: 'Will Expire on: #DateFormat(local.qryItems.MaxCreditCompleteByDate, "m/d/yy")#'
					)>
			<cfelse>
				<cfset local.item["formattedDateStart"] = local.qryItems.formattedDateStart>
				<cfset local.item["formattedTimeStart"] = local.qryItems.formattedTimeStart>
			</cfif>
		
			<!-- Append the item to the data array -->
			<cfset arrayAppend(local.data, local.item)>
		</cfloop>		

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryItems.recordcount,
			"recordsFiltered": local.qryItems.recordcount,
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWODSubmissionsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qrySWODSubmissions = CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getSWODSubmissions(keyword=arguments.event.getTrimValue('fKeyword',''),
				origPublishDateFrom=arguments.event.getValue('fOrigPublishDateFrom',''), origPublishDateTo=arguments.event.getValue('fOrigPublishDateTo',''), 
				fPublisher=arguments.event.getValue('fPublisher',''), orderby=arguments.event.getValue('orderBy'),
				direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'));
		</cfscript>

		<cfset local.arrData = []>
		<cfloop query="local.qrySWODSubmissions">
			<cfset local.arrData.append({
				"submissionID": local.qrySWODSubmissions.submissionID,
				"participantID": local.qrySWODSubmissions.participantID,
				"seminarName": encodeForHTML(local.qrySWODSubmissions.seminarName),
				"seminarSubTitle": HTMLEditFormat(local.qrySWODSubmissions.seminarSubTitle),
				"dateOrigPublished": dateFormat(local.qrySWODSubmissions.dateOrigPublished,"m/d/yyyy"),
				"dateCatalogStart": dateFormat(local.qrySWODSubmissions.dateCatalogStart,"m/d/yyyy"),
				"dateCatalogEnd": dateFormat(local.qrySWODSubmissions.dateCatalogEnd,"m/d/yyyy"),
				"publisherOrgCode": local.qrySWODSubmissions.publisherOrgCode,
				"dateSubmitted": dateFormat(local.qrySWODSubmissions.dateSubmitted,"m/d/yyyy"),
				"submittedBy": local.qrySWODSubmissions.submittedByMemberName,
				"status": local.qrySWODSubmissions.status,
				"convertedSeminarID": val(local.qrySWODSubmissions.convertedSeminarID),
				"DT_RowId": "seminarrow_#local.qrySWODSubmissions.submissionID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qrySWODSubmissions.totalCount),
			"recordsFiltered":  val(local.qrySWODSubmissions.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="listSWODTitles" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objSWCommon = createObject("component","seminarWebSWCommon");
			local.titleSetupIssues = {};
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=this.siteCode)>
		<cfset local.isSeminarLocked = local.objSWCommon.isSeminarLocked(seminarID=arguments.event.getValue('sid',0))>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE sat
					SET sat.titleOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.tblSeminarsAndTitles sat
					INNER JOIN dbo.tblSeminars AS s ON sat.seminarID = s.seminarID
						AND s.isDeleted = 0
					WHERE sat.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
					AND titleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfset local.qrySeminarTitles = createObject("component","model.seminarweb.SWODSeminars").getSeminarTitlesBySeminarID(seminarID=arguments.event.getValue('sid',0))>
		<cfset local.filesForTitles = createObject("component","model.admin.seminarweb.seminarWebFiles").getFilesFromSeminarTitles(participantID=local.participantID, seminarID=arguments.event.getValue('sid',0))>
		<cfset local.titlesWithNoMediaFiles = local.filesForTitles.titlesWithNoMediaFiles>
		<cfset local.titlesWithNoDownloadFiles = local.filesForTitles.titlesWithNoDownloadFiles>
		<cfset local.qrySetupIssues = local.filesForTitles.qrySetupIssues>
		
		<!--- Convert qrySetupIssues to a struct of titleID => 1 if checkID is 7, 16, or 17 --->
		<cfset local.titleSetupIssues = queryReduce(local.qrySetupIssues, function(acc, row) {
			if (listFind("7,16,17", row.checkID)) {
				acc[row.titleID] = 1;
			}
			return acc;
		}, {} )>
		<cfset local.titleDownloadSetupIssues = queryReduce(local.qrySetupIssues, function(acc, row) {
			if (listFind("5", row.checkID)) {
				acc[row.titleID] = 1;
			}
			return acc;
		}, {} )>
		<cfset local.data = []>
		<cfloop query="local.qrySeminarTitles">
			<cfset local.hasMediaFiles = not listContains(local.titlesWithNoMediaFiles, local.qrySeminarTitles.titleID, ",")>
			<cfset local.hasDownloadFiles = not listContains(local.titlesWithNoDownloadFiles, local.qrySeminarTitles.titleID, ",")>
			
			<cfset arrayAppend(local.data, {
				"titleID": local.qrySeminarTitles.titleID,
				"titleName": local.qrySeminarTitles.titleName,
				"titleNameEncoded": encodeForJavascript(local.qrySeminarTitles.titleName),
				"isSeminarLocked": local.isSeminarLocked,
				"hasMediaFiles": local.hasMediaFiles,
				"hasDownloadFiles": local.hasDownloadFiles,
				"setupIssues": structKeyExists(local.titleSetupIssues, local.qrySeminarTitles.titleID) ? 1 : 0,
				"downloadSetupIssues": structKeyExists(local.titleDownloadSetupIssues, local.qrySeminarTitles.titleID) ? 1 : 0,
				"numOfTitles": local.qrySeminarTitles.recordcount,
				"DT_RowId": "swodTitleProgramRow_#local.qrySeminarTitles.titleID#"
			})>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySeminarTitles.recordcount),
			"recordsFiltered": val(local.qrySeminarTitles.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWLRegistrants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars");
		local.objSWLAdmin = CreateObject("component","seminarWebSWL");
		local.gridmode = arguments.event.getTrimValue('gridmode','grid');

		local.assocHandlesOwnPayment = CreateObject("component","seminarWebSWCommon").doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
		if (local.assocHandlesOwnPayment)
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');

		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

		local.posStart = arguments.event.getValue('posStart');
		local.count = arguments.event.getValue('count');
		local.seminarID = 0;
		local.memberID = 0;
		
		// GRIDMODES:
		// regsearchgrid = registrant search
		// reggrid = seminarweb tab on the members record
		// grid = registrants tab on a program

		if (local.gridmode eq "regsearchgrid") {
			local.orderby = arguments.event.getValue('orderby');
			local.direct = arguments.event.getValue('orderDir');
			
			local.pdateFrom = arguments.event.getValue('frpDateFrom','');
			local.pdateTo = arguments.event.getValue('frpDateTo','');
			local.pkeyword = arguments.event.getValue('frpKeyword','');
			local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');
			local.ppublisherType = arguments.event.getValue('frpPubType','');
			local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
			local.rdateFrom = arguments.event.getValue('frrDateFrom','');
			local.rdateTo = arguments.event.getValue('frrDateTo','');
			local.rAttended = arguments.event.getValue('frrAttended','');
			local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
			local.rCredits = arguments.event.getValue('frrCredits','');

			local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getRegistrants(sitecode=this.siteCode, 
				mode=local.gridmode, seminarId=0, rAttended=local.rAttended, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, 
				rCredits=local.rCredits, memberID=0, pDateFrom=local.pdateFrom, pDateTo=local.pdateTo, pKeyword=local.pkeyword, pProgramCode=local.pProgramCode,
				pPublisherType=local.ppublisherType, pHideInactive=local.pHideInactive, orderby=local.orderby, direct=local.direct, posstart=local.posStart, count=local.count);

			local.showCreditCol = true;
			local.showAttendanceCol = true;
			local.showMaterialsCol = true;
			local.showInstructionsCol = true;
			local.showCertificateCol = true;
			local.showReplayCol = true;
			local.showManageProgressCol = true;
		} else if (local.gridmode eq "reggrid") {
			local.rdateFrom = arguments.event.getValue('rSWLDateFrom','');
			local.rdateTo = arguments.event.getValue('rSWLDateTo','');
			local.rAttended = arguments.event.getValue('rSWLAttended','');
			local.rHideDeleted = arguments.event.getValue('rSWLHideDeleted',1);
			local.rCredits = arguments.event.getValue('rSWLCredits','');
			local.orderby = arguments.event.getValue('orderby');
			local.direct = arguments.event.getValue('orderDir');
			
			local.memberID = int(val(arguments.event.getTrimValue('mid',0)));
			local.qryMember = application.objMember.getMemberInfo(memberID=local.memberID, orgID=this.orgID);

			local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getRegistrants(sitecode=this.siteCode, 
				mode=local.gridmode, seminarId=0, rAttended=local.rAttended, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, 
				rCredits=local.rCredits, memberID=local.qryMember.memberID, orderby=local.orderby, direct=local.direct, posstart=local.posStart, count=local.count);

			local.showCreditCol = true;
			local.showAttendanceCol = true;
			local.showMaterialsCol = true;
			local.showInstructionsCol = true;
			local.showCertificateCol = true;
			local.showReplayCol = false;
			if (local.qryEnrollments.showReplayCol is 1) local.showReplayCol = true;
			local.showManageProgressCol = false;
			if (local.qryEnrollments.showManageProgressCol is 1) local.showManageProgressCol = true;

		} else {
			local.orderby = arguments.event.getValue('orderby');
			local.direct = arguments.event.getValue('orderDir');
			
			local.seminarId = int(val(arguments.event.getTrimValue('pid',0)));
			local.rdateFrom = arguments.event.getValue('rDateFrom','');
			local.rdateTo = arguments.event.getValue('rDateTo','');
			local.rAttended = arguments.event.getValue('rAttended','');
			local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
			local.rCredits = arguments.event.getValue('rCredits','');

			local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWL").getRegistrants(sitecode=this.siteCode, 
				mode=local.gridmode, seminarId=local.seminarId, rAttended=local.rAttended, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted,
				rCredits=local.rCredits, memberID=0, orderby=local.orderby, direct=local.direct, posstart=local.posStart, count=local.count);

			local.strSeminar = local.objSWL.getSeminarBySeminarID(seminarID=local.seminarId);
			local.qrySeminar = local.objSWL.addParsedTimeZoneToSeminars(local.strSeminar,this.sitecode);
			local.qrySeminarForms = local.objSWLAdmin.getSeminarForms(seminarID=local.seminarId);
			local.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.seminarId, sitecode=this.sitecode);
			local.showCreditCol = false;
			local.showAttendanceCol = false;
			local.showMaterialsCol = false;
			local.showInstructionsCol = false;
			local.showCertificateCol = false;
			if (local.qrySeminar.offerCredit) local.showCreditCol = true;
			if (NOT local.qrySeminar.isOpen) {
				local.showAttendanceCol = true;
				if (local.materialsDoc.recordcount gt 0) local.showMaterialsCol = true;
				if (local.qrySeminar.offerCertificate is 1) local.showCertificateCol = true;
			} else {
				local.showInstructionsCol = true;
			}
			local.showReplayCol = false;
			if (local.qryEnrollments.showReplayCol is 1) local.showReplayCol = true;
			local.showManageProgressCol = false;
			if (local.qryEnrollments.showManageProgressCol is 1) local.showManageProgressCol = true;
		}
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>
		<cfset local.hasIsFeeExemptRights = local.tmpRights.isFeeExempt is 1>

		<cfset local.data = []>
		<cfloop query="local.qryEnrollments">			
			<cfset local.hasViewSWLCertificate = false>
			<cfif local.showCertificateCol>
				<cfset local.hasViewSWLCertificateRights = (local.tmpRights.sendSWLCertificatesSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.sendSWLCertificatesAll is 1>
				<cfset local.progress = local.objSWL.getSeminarProgress(local.qryEnrollments.enrollmentID)>	
				
				<cfif local.hasViewSWLCertificateRights 
					AND local.qryEnrollments.passed is 1 
					and local.qryEnrollments.isActive is 1
					and (
						local.qryEnrollments.creditCount eq 0 
							OR 
						((local.qryEnrollments.creditCount gt 0 and local.progress.qrySeminarProgress.hasCertificate is 1)
							OR
						(local.qryEnrollments.creditCount gt 0 AND local.progress.qrySeminarProgress.allPostTestCompleted gt 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted gt 0))
					)>
					<cfset local.hasViewSWLCertificate = true>
				</cfif>
			</cfif>
			<cfset local.hasManageSWLRegProgressRights = (local.tmpRights.manageSWLRegProgressSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWLRegProgressAll is 1>
			<cfif local.showManageProgressCol>				
				<cfif local.hasManageSWLRegProgressRights is 1 AND local.qryEnrollments.showManageProgress is 1>
					<cfset local.qrySeminarForms = local.objSWLAdmin.getSeminarForms(seminarID=local.qryEnrollments.seminarID)>
				</cfif>
			</cfif>
			<cfset local.hasDeleteSWLRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.deleteSWLRegistrantAll is 1>
			<cfset local.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.qryEnrollments.seminarId, enrollmentID=val(local.qryEnrollments.enrollmentID), userType='user', sitecode=this.siteCode)>
			<cfif local.showMaterialsCol>
				<cfset local.showMaterialsLink = false>
				<cfif ListContainsNoCase("reggrid,regsearchgrid",local.gridmode)>
					<cfset local.strSeminar = local.objSWL.getSeminarBySeminarID(seminarID=local.qryEnrollments.seminarId)>
					<cfset local.qrySeminar = local.objSWL.addParsedTimeZoneToSeminars(local.strSeminar,this.sitecode)>
					<cfif NOT local.qrySeminar.isOpen AND (local.materialsDoc.recordcount gt 0)>
						<cfset local.showMaterialsLink = true>
					</cfif>
				</cfif>
			</cfif>
			<cfset local.isSWLProviderWarning = false>
			<cfif local.gridmode eq "grid" and local.seminarId and local.qrySeminar.isOpen and (
				(local.qryEnrollments.providerID is 2 and len(local.qryEnrollments.ACSeminarSCOID) and NOT len(local.qryEnrollments.ACUserID)) 
				OR
				(local.qryEnrollments.providerID is 3 and len(local.qryEnrollments.ZoomWebinarID) and NOT len(local.qryEnrollments.ZoomWebinarRegistrantID))
			)>
				<cfset local.isSWLProviderWarning = true>
			</cfif>			
			<cfset local.hasManageSWLRegistrantsRights = (local.tmpRights.manageSWLRegistrantsSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWLRegistrantsAll is 1>
			<cfset local.hasViewSWLInstructions = false>
			<cfif local.showInstructionsCol>
				<cfif local.hasManageSWLRegistrantsRights and local.qryEnrollments.isActive is 1 and 
					(
						(local.qryEnrollments.providerID is 2 and len(local.qryEnrollments.ACSeminarSCOID) and len(local.qryEnrollments.ACUserID))
						or 
						(local.qryEnrollments.providerID is 3 and len(local.qryEnrollments.ZoomWebinarID) and len(local.qryEnrollments.ZoomWebinarRegistrantID))
					)>
					<cfset local.hasViewSWLInstructions = true>
				</cfif>
			</cfif>
			<cfset local.addPaymentEncString = "">
			<cfif local.assocHandlesOwnPayment>
				<cfif local.qryEnrollments.handlesOwnPayment is 1 and local.qryEnrollments.amountDue gt 0 and (local.memberID or val(local.qryEnrollments.memberID) gt 0)>
					<cfif local.gridmode eq "reggrid">
						<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.memberID, t="Registration of #local.qryMember.lastname#, #local.qryMember.firstname# (#local.qryMember.memberNumber#)", ta=local.qryEnrollments.amountDue, tmid=local.memberID, ad="v|#local.qryEnrollments.invoicesDue#")>
					<cfelse>
						<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryEnrollments.memberID, t="Registration of #local.qryEnrollments.lastname#, #local.qryEnrollments.firstname# (#local.qryEnrollments.memberNumber#)", ta=local.qryEnrollments.amountDue, tmid=local.qryEnrollments.memberID, ad="v|#local.qryEnrollments.invoicesDue#")>
					</cfif>
				</cfif>
			</cfif>
			<cfset local.canViewReplay = false>
			<cfif local.qrySeminar.replayAvailability EQ "registrants">
				<cfset local.canViewReplay = true>
			<cfelseif local.qrySeminar.replayAvailability EQ "attendees" AND local.qryEnrollments.attended EQ 1>
				<cfset local.canViewReplay = true>
			<cfelseif local.qrySeminar.replayAvailability EQ "nonattendees" AND  local.qryEnrollments.attended EQ 0>
				<cfset local.canViewReplay = true>
			</cfif>

			<cfset arrayAppend(local.data, {
				"seminarid": local.qryEnrollments.seminarId,
				"memberid": (local.memberID)?local.memberID:local.qryEnrollments.memberid,
				"membernumber": (local.memberID)?local.qryMember.memberNumber:local.qryEnrollments.memberNumber,
				"lastname": (local.memberID)?local.qryMember.lastname:local.qryEnrollments.lastname,
				"firstname": (local.memberID)?local.qryMember.firstname:local.qryEnrollments.firstname,
				"company": (local.memberID)?local.qryMember.company:local.qryEnrollments.company,
				"seminarname": local.qryEnrollments.seminarName,
				"seminarsubtitle": local.qryEnrollments.seminarSubTitle,
				"signuporgname": local.qryEnrollments.signUpOrgName,
				"isActive": local.qryEnrollments.isActive,
				"enrollmentid": local.qryEnrollments.enrollmentID,
				"isswlproviderwarning": local.isSWLProviderWarning,
				"signuporgcode": local.qryEnrollments.signUpOrgCode,
				"dateenrolled": dateFormat(local.qryEnrollments.dateenrolled,"m/d/yyyy"),
				"attended": YesNoFormat(local.qryEnrollments.attended),
				"duration": val(local.qryEnrollments.duration),
				"creditcount": "#local.qryEnrollments.creditCount# credit" & (local.qryEnrollments.creditcount neq 1 ? 's' : ''),
				"depomemberdataid": local.qryEnrollments.depomemberdataid,
				"amountbilled": dollarFormat(local.qryEnrollments.amountBilled),
				"amountdue": dollarFormat(local.qryEnrollments.amountDue),
				"isfeeexempt": local.qryEnrollments.isFeeExempt,
				"candelete": local.hasDeleteSWLRegistrantRights and local.qryEnrollments.isActive is 1,
				"assochandlesownpayment": local.assocHandlesOwnPayment,
				"addpaymentencstring": local.addPaymentEncString,
				"showInstructionsCol":local.showInstructionsCol,
				"hasviewswlinstructions": local.hasViewSWLInstructions,
				"showCreditCol":local.showCreditCol,
				"hasmanageswlregcreditrights": local.showCreditCol and local.qryEnrollments.isActive is 1 and  (local.tmpRights.manageSWLRegCreditSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWLRegCreditAll is 1,
				"showCertificateCol":local.showCertificateCol,
				"hasviewswlcertificate": local.hasViewSWLCertificate,
				"showManageProgressCol":local.showManageProgressCol,
				"hasviewswlprogress": local.showManageProgressCol and local.hasManageSWLRegProgressRights is 1 AND local.qryEnrollments.showManageProgress is 1 AND local.qrySeminarForms.recordCount gt 0 and local.qryEnrollments.isActive is 1,
				"hasviewswlcommunication": local.hasManageSWLRegistrantsRights and local.qryEnrollments.emailCount gt 0 and local.qryEnrollments.isActive is 1,
				"showMaterialsCol":local.showMaterialsCol,
				"hasviewswlmaterials": local.showMaterialsCol and local.hasManageSWLRegistrantsRights and local.qryEnrollments.isActive is 1 and local.showMaterialsLink,
				"showReplayCol":local.showReplayCol,
				"hassendswlreplay": local.showReplayCol and local.hasManageSWLRegistrantsRights and local.qryEnrollments.showReplay is 1 and local.qryEnrollments.isActive is 1,
				"hasIsFeeExemptRights":local.hasIsFeeExemptRights,
				"showfeeexemptcol": local.hasIsFeeExemptRights and val(local.qryEnrollments.amountBilled) EQ 0,
				"haschangeswregistrantprice": local.hasDeleteSWLRegistrantRights AND local.qryEnrollments.isActive IS 1 AND NOT local.qryEnrollments.handlesOwnPayment AND NOT val(local.qryEnrollments.bundleOrderID),
				"canviewreplay":local.canViewReplay,
				"DT_RowId": "swlRegRow_#local.qryEnrollments.enrollmentID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEnrollments.totalCount),
			"recordsFiltered": val(local.qryEnrollments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWODRegistrants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.gridmode = arguments.event.getTrimValue('gridmode','grid');

			local.assocHandlesOwnPayment = CreateObject("component","seminarWebSWCommon").doesAssociationHandlesPayment(orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
			if (local.assocHandlesOwnPayment)
				local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',25))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.seminarID = 0;
			local.memberID = 0;
			
			// GRIDMODES:
			// regsearchgrid = registrant search
			// reggrid = seminarweb tab on the members record
			// grid = registrants tab on a program

			if (local.gridmode eq "regsearchgrid") {		
				local.pkeyword = arguments.event.getValue('frpKeyword','');
				local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');
				local.ppublisherType = arguments.event.getValue('frpPubType','');
				local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
				local.rdateFrom = arguments.event.getValue('frrDateFrom','');
				local.rdateTo = arguments.event.getValue('frrDateTo','');
				local.rdateCompletedFrom = arguments.event.getValue('frrDateCompletedFrom','');
				local.rdateCompletedTo = arguments.event.getValue('frrDateCompletedTo','');
				local.rCompleted = arguments.event.getValue('frrCompleted','');
				local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
		
				local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getRegistrants(sitecode=this.siteCode, 
					mode=local.gridmode, seminarId=0, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, completed=local.rCompleted, rHideDeleted=local.rHideDeleted, 
					cdateFrom=local.rdateCompletedFrom, cdateTo=local.rdateCompletedTo, memberID=0, pKeyword=local.pkeyword, pProgramCode=local.pProgramCode, 
					pPublisherType=local.ppublisherType, pHideInactive=local.pHideInactive, orderby=arguments.event.getValue('orderBy'), 
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'));
				
			} else if (local.gridmode eq "reggrid") {
				local.rdateFrom = arguments.event.getValue('rSWODDateFrom','');
				local.rdateTo = arguments.event.getValue('rSWODDateTo','');
				local.completed = arguments.event.getTrimValue('fSWODCompleted','');
				local.rHideDeleted = arguments.event.getValue('rSWODHideDeleted',1);
				local.cdateFrom = arguments.event.getValue('cSWODDateFrom','');
				local.cdateTo = arguments.event.getValue('cSWODDateTo','');				
				local.memberID = int(val(arguments.event.getTrimValue('mid',0)));
				local.qryMember = application.objMember.getMemberInfo(memberID=local.memberID, orgID=this.orgID);
		
				local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getRegistrants(sitecode=this.siteCode, 
					mode=local.gridmode, seminarId=0, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, completed=local.completed, rHideDeleted=local.rHideDeleted, 
					cdateFrom=local.cdateFrom, cdateTo=local.cdateTo, memberID=local.qryMember.memberID, orderby=arguments.event.getValue('orderBy'), 
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'));
		
			} else {
				local.seminarId = int(val(arguments.event.getTrimValue('pid',0)));
				local.rdateFrom = arguments.event.getValue('rDateFrom','');
				local.rdateTo = arguments.event.getValue('rDateTo','');
				local.completed = arguments.event.getTrimValue('fCompleted','');
				local.rHideDeleted = arguments.event.getValue('rHideDeleted',1);
				local.cdateFrom = arguments.event.getValue('cDateFrom','');
				local.cdateTo = arguments.event.getValue('cDateTo','');
		
				local.qryEnrollments = CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getRegistrants(sitecode=this.siteCode, 
					mode=local.gridmode, seminarId=local.seminarId, rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, completed=local.completed, 
					cdateFrom=local.cdateFrom, cdateTo=local.cdateTo, memberID=0, orderby=arguments.event.getValue('orderBy'), 
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'));
			}
		</cfscript>

		<cfset local.semWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=this.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.semWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=this.siteID)>
		<cfset local.hasIsFeeExemptRights = local.tmpRights.isFeeExempt is 1>

		<cfset local.data = []>
		<cfloop query="local.qryEnrollments">			
			<cfset local.hasManageSWODRegistrantsRights = (local.tmpRights.manageSWODRegistrantsSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWODRegistrantsAll is 1>
			<cfset local.hasViewSWODCertificateRights = (local.tmpRights.sendSWODCertificatesSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.sendSWODCertificatesAll is 1>
			<cfset local.hasManageSWODRegCreditRights = (local.tmpRights.manageSWODRegCreditSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWODRegCreditAll is 1>
			<cfset local.hasManageSWODRegProgressRights = (local.tmpRights.manageSWODRegProgressSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.manageSWODRegProgressAll is 1>
			<cfset local.hasDeleteSWODRegistrantRights = (local.tmpRights.deleteSWODRegistrantSignUp is 1 AND local.qryEnrollments.signUpOrgCode EQ this.siteCode) OR local.tmpRights.deleteSWODRegistrantAll is 1>
			
			<cfset local.addPaymentEncString = "">
			<cfif local.assocHandlesOwnPayment>
				<cfif local.qryEnrollments.handlesOwnPayment is 1 and local.qryEnrollments.amountDue gt 0 and (local.memberID or val(local.qryEnrollments.memberID) gt 0)>
					<cfif local.memberID>
						<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.memberID, t="Registration of #local.qryMember.lastname#, #local.qryMember.firstname# (#local.qryMember.memberNumber#)", ta=local.qryEnrollments.amountDue, tmid=local.memberID, ad="v|#local.qryEnrollments.invoicesDue#")>
					<cfelse>
						<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryEnrollments.memberID, t="Registration of #local.qryEnrollments.lastname#, #local.qryEnrollments.firstname# (#local.qryEnrollments.memberNumber#)", ta=local.qryEnrollments.amountDue, tmid=local.qryEnrollments.memberID, ad="v|#local.qryEnrollments.invoicesDue#")>
					</cfif>
				</cfif>
			</cfif>

			<cfset arrayAppend(local.data, {
				"seminarid": local.qryEnrollments.seminarId,
				"memberid": (local.memberID)?local.memberID:local.qryEnrollments.memberid,
				"membernumber": (local.memberID)?local.qryMember.memberNumber:local.qryEnrollments.memberNumber,
				"lastname": (local.memberID)?local.qryMember.lastname:local.qryEnrollments.lastname,
				"firstname": (local.memberID)?local.qryMember.firstname:local.qryEnrollments.firstname,
				"company": (local.memberID)?local.qryMember.company:local.qryEnrollments.company,
				"seminarname": local.qryEnrollments.seminarName,
				"seminarsubtitle": local.qryEnrollments.seminarSubTitle,
				"signuporgname": local.qryEnrollments.signUpOrgName,
				"isActive": local.qryEnrollments.isActive,
				"enrollmentid": local.qryEnrollments.enrollmentID,
				"signuporgcode": local.qryEnrollments.signUpOrgCode,
				"dateenrolled": dateFormat(local.qryEnrollments.dateenrolled,"m/d/yyyy"),
				"creditcount": "#local.qryEnrollments.creditCount# credit" & (local.qryEnrollments.creditcount neq 1 ? 's' : ''),
				"depomemberdataid": local.qryEnrollments.depomemberdataid,
				"amountbilled": dollarFormat(local.qryEnrollments.amountBilled),
				"amountdue": dollarFormat(local.qryEnrollments.amountDue),
				"isfeeexempt": local.qryEnrollments.isFeeExempt,
				"candelete": local.hasDeleteSWODRegistrantRights and local.qryEnrollments.isActive is 1,
				"assochandlesownpayment": local.assocHandlesOwnPayment,
				"addpaymentencstring": local.addPaymentEncString,
				"hasmanageswodregcreditrights": local.hasManageSWODRegCreditRights and local.qryEnrollments.isActive is 1,
				"hasviewswodcertificate": local.hasViewSWODCertificateRights AND local.qryEnrollments.progress NEQ 3 AND local.qryEnrollments.showCertButton is 1 and local.qryEnrollments.isActive is 1,
				"hasviewswodprogress": local.hasManageSWODRegProgressRights and local.qryEnrollments.isActive is 1,
				"hasviewswodcommunication": local.hasManageSWODRegistrantsRights AND local.qryEnrollments.emailCount and local.qryEnrollments.isActive is 1,
				"hasviewswodinstructions": local.hasManageSWODRegistrantsRights and local.qryEnrollments.isActive is 1,
				"hasisfeeexemptrights": local.hasIsFeeExemptRights,
				"showfeeexemptcol": local.hasIsFeeExemptRights and val(local.qryEnrollments.amountBilled) EQ 0,
				"haschangeswregistrantprice": local.hasDeleteSWODRegistrantRights AND local.qryEnrollments.isActive IS 1 AND NOT local.qryEnrollments.handlesOwnPayment AND NOT val(local.qryEnrollments.bundleOrderID),
				"DT_RowId": "swodRegRow_#local.qryEnrollments.enrollmentID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEnrollments.totalCount),
			"recordsFiltered": val(local.qryEnrollments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAuthorSeminars" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			local.programType = arguments.event.getValue('programType', '');
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateStart #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"seminarName #arguments.event.getValue('orderDir')#, seminarSubTitle #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryGetAuthorSeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpAuthorSeminars') IS NOT NULL
					DROP TABLE ##tmpAuthorSeminars;
				CREATE TABLE ##tmpAuthorSeminars (seminarID INT, seminarName VARCHAR(250), seminarSubTitle VARCHAR(250), isPublished BIT,
					format VARCHAR(10), orgcode VARCHAR(10), dateStart DATETIME, dateEnd DATETIME, wddxTimeZones XML, row INT);

				DECLARE @totalCount INT, @posStart INT, @posStartPlusCount INT, @searchValue varchar(300);
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>

				INSERT INTO ##tmpAuthorSeminars (seminarID, seminarName, seminarSubTitle, isPublished, format, orgcode, dateStart, dateEnd, wddxTimeZones, row)
				SELECT seminarID, seminarName, seminarSubTitle, isPublished, format, orgcode, dateStart, dateEnd, wddxTimeZones,
					ROW_NUMBER() OVER (ORDER BY isPublished DESC, #preserveSingleQuotes(local.orderby)#)
				FROM (
					SELECT s.seminarID, s.seminarName, s.seminarSubTitle, s.isPublished,
						CASE WHEN swl.liveid IS NOT NULL THEN 'SWL'
							WHEN sod.ondemandID IS NOT NULL THEN 'SWOD'
							ELSE 'unknown' END AS format,
						CASE WHEN sod.ondemandID is not null then (select p.orgcode from dbo.tblParticipants as p where p.participantID = s.participantID)
							ELSE '' END as orgcode,
						swl.dateStart, swl.dateEnd, CAST(swl.wddxTimeZones AS XML) as wddxTimeZones
					FROM dbo.tblSeminars AS s
					INNER JOIN dbo.tblSeminarsAndAuthors AS saa ON s.seminarID = saa.seminarID
					LEFT OUTER JOIN dbo.tblSeminarsSWOD AS sod ON s.seminarID = sod.seminarID
					LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
					WHERE saa.authorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('aid',0)#">
					AND s.isDeleted = 0
					<cfif len(local.programType)>
						<cfif local.programType EQ "SWL">
							AND swl.liveid IS NOT NULL
						<cfelseif local.programType EQ "SWOD">
							AND sod.ondemandID IS NOT NULL
						</cfif>
					</cfif>
					<cfif len(local.searchValue)>
						AND (s.seminarName LIKE @searchValue)
					</cfif>
				) tmp;

				SET @totalCount = @@ROWCOUNT;

				SELECT seminarID, seminarName, seminarSubTitle, isPublished, format, orgcode, dateStart, dateEnd, wddxTimeZones, @totalCount AS totalCount
				FROM ##tmpAuthorSeminars
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpAuthorSeminars') IS NOT NULL
					DROP TABLE ##tmpAuthorSeminars;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetAuthorSeminars">
			<cfset local.dataItem = {
				"seminarID": local.qryGetAuthorSeminars.seminarID,
				"seminarName": local.qryGetAuthorSeminars.seminarName,
				"seminarSubTitle": local.qryGetAuthorSeminars.seminarSubTitle,
				"isPublished": local.qryGetAuthorSeminars.isPublished,
				"format": local.qryGetAuthorSeminars.format,
				"orgcode": local.qryGetAuthorSeminars.orgcode,
				"DT_RowId": "swAuthSemRow_#local.qryGetAuthorSeminars.seminarID#"
			}>

			<cfif local.qryGetAuthorSeminars.format EQ "SWL" AND isDate(local.qryGetAuthorSeminars.dateStart)>
				<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=this.siteCode).qryAssociation>
				<cfset local.parsedTime = CreateObject("component","model.seminarweb.SWLiveSeminars").parseTimesFromWDDX(
					seminarWDDXTimeZones=local.qryGetAuthorSeminars.wddxTimeZones,
					orgWDDXTimeZones=local.qryAssociation.wddxTimeZones,
					ifErrStartTime=local.qryGetAuthorSeminars.dateStart,
					ifErrEndTime=local.qryGetAuthorSeminars.dateEnd
				)>
				<cfset local.formattedDateStart = dateFormat(local.parsedTime.startDate, "mmmm d, yyyy")>
				<cfset local.formattedTimeStart = timeFormat(local.parsedTime.startDate, "h:mm tt") & " - " & timeFormat(local.parsedTime.endDate, "h:mm tt")>

				<cfset local.dataItem["formattedDateStart"] = local.formattedDateStart>
				<cfset local.dataItem["formattedTimeStart"] = local.formattedTimeStart>
			</cfif>

			<cfset local.arrData.append(local.dataItem)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryGetAuthorSeminars.totalcount),
			"recordsFiltered":  val(local.qryGetAuthorSeminars.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="listSWProgramsForCopyRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.keyWord = form['search[value]'] ?: '';

			local.programID = arguments.event.getValue('pid',0);
			local.ft = arguments.event.getValue('ft','');
			if (local.ft EQ "SWL") {
				local.dateFrom = arguments.event.getValue('fDateFrom','');
				local.dateTo = arguments.event.getValue('fDateTo','');
			}
			local.publisherType = arguments.event.getValue('fPubType','PO');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.hideInactive = arguments.event.getValue('fStatus',1);

			if(local.ft EQ "SWL"){
				local.qryPrograms = CreateObject("component","model.admin.seminarweb.seminarwebSWL").getPrograms(sitecode=this.siteCode, 
					mode="copyRatesGrid", dateFrom=local.dateFrom, dateTo=local.dateTo, keyword=local.keyWord, programCode=local.programCode,
					publisherType=local.publisherType, hideInactive=local.hideInactive, orderby=arguments.event.getValue('orderBy'),
					direct=arguments.event.getValue('orderDir'), posstart=arguments.event.getValue('posstart'),
					count=arguments.event.getValue('count'), pid=local.programID);
			}	
			else if(local.ft EQ "SWOD"){
				local.qryPrograms = CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getPrograms(sitecode=this.siteCode, 
					mode="copyRatesGrid", keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType,
					hideInactive=local.hideInactive, orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'), 
					posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'), pid=local.programID);
			}
			else if(local.ft EQ "SWB"){
				local.qryPrograms = CreateObject("component","model.admin.seminarWeb.seminarwebSWB").getPrograms(sitecode=this.siteCode, 
					mode="copyRatesGrid", keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType,
					hideInactive=local.hideInactive, orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'), 
					posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'), pid=local.programID);
			}
		</cfscript>
				
		<cfset local.arrData = []>
		<cfloop query="local.qryPrograms"> 
			<cfif listFindNoCase("SWL,SWOD", local.ft)>
				<cfset local.programID = local.qryPrograms.seminarID>
				<cfset local.programName = local.qryPrograms.seminarName>
				<cfset local.programSubTitle = local.qryPrograms.seminarSubTitle>
				<cfset local.isActive = local.qryPrograms.isPublished>
				<cfset local.programDisplayText = "Program">
			<cfelseif local.ft EQ "SWB">
				<cfset local.programID = local.qryPrograms.bundleID>
				<cfset local.programName = local.qryPrograms.bundleName>
				<cfset local.programSubTitle = local.qryPrograms.bundleSubTitle>
				<cfset local.isActive = local.qryPrograms.isActive>
				<cfset local.programDisplayText = "Bundle">
			</cfif>

			<cfset local.arrData.append({
				"programID": local.programID,
				"isActive": local.isActive,
				"programName": htmleditformat(local.programName),
				"programDisplayText": local.programDisplayText,
				"dateStart": local.ft EQ "SWL" ? dateFormat(local.qryPrograms.dateStart,"m/d/yyyy") : "",
				"programSubTitle": local.programSubTitle,
				"publisherOrgCode": local.qryPrograms.publisherOrgCode,
				"siteCode": this.siteCode,
				"DT_RowId": "SWProgramRow_#local.programID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryPrograms.totalCount),
			"recordsFiltered":  val(local.qryPrograms.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWProgramRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrRates = []>
		<cfset local.objSWCommon = createObject("component","seminarWebSWCommon")>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.programID = arguments.event.getValue('programID',0)>
		<cfset local.programType = arguments.event.getValue('ft')>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfset local.participantID = local.objSWCommon.getParticipantIDFromSiteCode(sitecode=local.siteCode)>
		<cfset local.hasRateChangeRights = local.objSWCommon.hasSWProgramRateChangeRights(sitecode=local.siteCode, programID=local.programID, programType=local.programType)>

		<cfif listFindNoCase("SWL,SWOD",local.programType)>
			<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
			<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
				<cfloop array="#local.reorderData#" index="local.thisData">
					<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
						UPDATE r
						SET r.rateOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder+1#">
						FROM dbo.tblSeminarsAndRates AS r
						INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
						WHERE r.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
						AND ISNULL(r.rateGroupingID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.rateGroupingID#">
						AND r.rateID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
						AND participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
					</cfquery>
				</cfloop>
			</cfif>
			<cfset local.isProgramLocked = local.objSWCommon.isSeminarLocked(seminarID=local.programID)>
			<cfset local.qryRates = local.objSWCommon.getSeminarRatesBySeminarID(participantID=local.participantID, seminarID=local.programID)>
		<cfelseif local.programType eq "SWB">
			<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
			<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
				<cfloop array="#local.reorderData#" index="local.thisData">
					<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
						UPDATE r
						SET r.rateOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder+1#">
						FROM dbo.tblBundlesAndRates AS r
						INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
							and sr.siteResourceStatusID = 1
						WHERE r.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
						AND r.rateID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
						AND ISNULL(r.rateGroupingID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.rateGroupingID#">
						AND r.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">

					</cfquery>
				</cfloop>
			</cfif>
			<cfset local.isProgramLocked = local.objSWCommon.isBundleProgramLocked(bundleID=local.programID)>
			<cfset local.qryRates = createObject("component","seminarWebSWB").getBundleRatesByBundleID(participantID=local.participantID, bundleID=local.programID)>
		</cfif>

		<cfquery name="local.qryGroups" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT groupID, groupPathExpanded AS thePathexpanded
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
			ORDER BY groupPathSortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.totalRateGroupingsNotZero" dbtype="query">
			SELECT DISTINCT rateGroupingID
			FROM [local].qryRates
			where rateGroupingID > 0
		</cfquery>
		
		<cfset local.RGIndex = 0>

		<cfoutput query="local.qryRates" group="rateGroupingID">
			<cfset local.rateGroupRowID = "rg#local.qryRates.rateGroupingID#">

			<cfif local.totalRateGroupingsNotZero.recordcount>
				<cfif local.qryRates.rateGroupingID gt 0>
					<cfset local.RGIndex++>
				</cfif>

				<cfset local.objRateGrouping = {
					"level": 1,
					"rowType": "rateGroup",
					"displayName": local.qryRates.rateGrouping,
					"rateGroupingID": local.qryRates.rateGroupingID,
					"hasChildren": 0,
					"programID": local.programID,
					"hasChangePerms": local.qryRates.rateGroupingID gt 0 AND NOT local.isProgramLocked AND local.hasRateChangeRights,
					"canMoveUp": local.RGIndex gt 1,
					"canMoveDown": local.RGIndex lt local.totalRateGroupingsNotZero.recordCount,
					"parentRowID": "gridRoot",
					"DT_RowId": local.rateGroupRowID,
					"DT_RowClass": "child-of-gridRoot"
				}>
				<cfset local.objRateGrouping["DT_RowClass"] &= local.qryRates.rateGroupingID eq 0 ? " default-nogrouping" : "">
				<cfset local.arrRates.append(local.objRateGrouping)>
			</cfif>

			<cfset local.rateIndex = 0>
			<cfset local.rateParentRowID = local.totalRateGroupingsNotZero.recordcount ? local.rateGroupRowID : "gridRoot">
				
			<cfoutput group="rateID">
				<cfif val(local.qryRates.rateID) gt 0>
					<cfset local.rateIndex++>

					<cfquery name="local.totalRateCount" dbtype="query">
						SELECT DISTINCT rateID
						FROM [local].qryRates
						WHERE rateGroupingID = #local.qryRates.rateGroupingID#
					</cfquery>
					
					<cfset local.rateRowID = "#local.rateGroupRowID#-#local.qryRates.rateID#">

					<cfset local.objRate = {
						"level": local.totalRateGroupingsNotZero.recordcount ? 2 : 1,
						"rowType": "rate",
						"displayName": local.qryRates.rateName & (local.qryRates.isHidden is 1 ? " (hidden rate)" : ""),
						"displayNameEncoded": encodeForJavascript(local.qryRates.rateName),
						"rateGroupingID": local.qryRates.rateGroupingID,
						"rateID": val(local.qryRates.rateID),
						"rateFormatted": dollarFormat(local.qryRates.rate),
						"rateSRID": local.qryRates.siteResourceID,
						"hasChildren": 0,
						"canAddGroup": NOT local.isProgramLocked,
						"hasChangePerms": NOT local.isProgramLocked AND local.hasRateChangeRights,
						"canMoveUp": local.rateIndex gt 1,
						"canMoveDown": local.rateIndex lt local.totalRateCount.recordCount,
						"parentRowID": local.rateParentRowID,
						"DT_RowId": local.rateRowID,
						"DT_RowClass": "child-of-#local.rateParentRowID#"
					}>
					<cfset local.arrRates.append(local.objRate)>

					<cfset local.groupIndex = 0>
					<cfoutput>
						<cfif local.qryRates.groupID gt 0>
							<cfset local.groupIndex++>

							<cfquery dbtype="query" name="local.qryGroupFullPath">
								SELECT thePathExpanded
								FROM [local].qryGroups
								WHERE groupID = #local.qryRates.groupID#
							</cfquery>

							<cfif len(local.qryGroupFullPath.thePathExpanded)>
								<cfset local.displayGroupAs = local.qryGroupFullPath.thePathExpanded>
							<cfelse>
								<cfset local.displayGroupAs = local.qryRates.groupName>
							</cfif>

							<cfset local.arrRates.append({
								"level": local.totalRateGroupingsNotZero.recordcount ? 3 : 2,
								"rowType": "group",
								"displayName": (local.qryRates.include eq 0 ? "Denied: " : "") & local.displayGroupAs,
								"rateGroupingID": val(local.qryRates.rateGroupingID),
								"rateID": val(local.qryRates.rateID),
								"groupID": local.qryRates.groupID,
								"include": local.qryRates.include,
								"hasChildren": 0,
								"canRemove": NOT local.isProgramLocked,
								"parentRowID": "#local.rateRowID#",
								"DT_RowId": "#local.rateRowID#-#local.qryRates.groupID#-#local.qryRates.include#",
								"DT_RowClass": "child-of-#local.rateRowID#"
							})>
						</cfif>
					</cfoutput>

					<cfset local.objRate.hasChildren = local.groupIndex gt 0>
				</cfif>
			</cfoutput>

			<cfset local.objRateGrouping.hasChildren = local.rateIndex gt 0>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrRates),
			"recordsFiltered": arrayLen(local.arrRates),
			"data": local.arrRates
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSelectedSWODProgramsForDeactivation" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset local = structNew()>
		<cfset local.deactivateSWODSeminarIDs = arguments.event.getValue('deactivatePrgmIDs','')>
		<cfset local.arrData = []>

		<cfif listLen(local.deactivateSWODSeminarIDs)>
			<cfset local.participantID = createObject("component","seminarWebSWCommon").getParticipantIDFromSiteCode(sitecode=this.siteCode)>
			<cfstoredproc procedure="swod_getProgramsForDeactivationBySeminarIDList" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.deactivateSWODSeminarIDs#">
				<cfprocresult name="local.qrySWODPrograms" resultset="1">
			</cfstoredproc>

			<cfloop query="local.qrySWODPrograms">
				<cfset local.arrData.append({
					"seminarID": local.qrySWODPrograms.seminarID,
					"seminarName": encodeForHTML(local.qrySWODPrograms.seminarName),
					"catalogSaleExpired": local.qrySWODPrograms.catalogSaleExpired ? 'Yes' : 'No',
					"catalogSaleDetails": 
						NOT LEN(local.qrySWODPrograms.dateCatalogEnd) 
						? 'Not Sold in Catalog' 
						: (
							local.qrySWODPrograms.catalogSaleExpired
							? 'Expired on: #DateFormat(local.qrySWODPrograms.dateCatalogEnd, "m/d/yy")#' 
							: 'Will Expire on: #DateFormat(local.qrySWODPrograms.dateCatalogEnd, "m/d/yy")#'
						),
					"creditExpired": local.qrySWODPrograms.creditExpired ? 'Yes' : 'No',
					"creditDetails": 
						NOT LEN(local.qrySWODPrograms.MaxCreditCompleteByDate) 
						? 'No Credit Offered' 
						: (
							local.qrySWODPrograms.creditExpired 
							? 'Expired on: #DateFormat(local.qrySWODPrograms.MaxCreditCompleteByDate, "m/d/yy")#'
							: 'Will Expire on: #DateFormat(local.qrySWODPrograms.MaxCreditCompleteByDate, "m/d/yy")#'
						), 
					"DT_RowId": "deactivaterow_#local.qrySWODPrograms.seminarID#"
				})>
			</cfloop>
			<cfset local.recordsTotal = local.qrySWODPrograms.recordCount>
		<cfelse>
			<cfset local.recordsTotal = 0>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.recordsTotal,
			"recordsFiltered": local.recordsTotal,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRegistrantsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',25))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.pKeyword = arguments.event.getTrimValue('frpKeyword','');
			local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');
			local.pformat = arguments.event.getValue('frformat',0);
			local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
			local.rdateFrom = arguments.event.getValue('frrDateFrom','');
			local.rdateTo = arguments.event.getValue('frrDateTo','');
			local.cdateFrom = arguments.event.getValue('frrDateCompletedFrom','');
			local.cdateTo = arguments.event.getValue('frrDateCompletedTo','');
			local.pPublisher = arguments.event.getValue('frpPublisher',0);	
			local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
	
			local.qryEnrollments = CreateObject("component","seminarwebSWCommon").getRegistrants(mode="regsearch",
				rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, pKeyword=local.pkeyword, 
				pProgramCode=local.pProgramCode, pformat=local.pformat, pHideInactive=local.pHideInactive, pPublisher=local.pPublisher, 
				orderby=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('orderDir'), 
				posstart=arguments.event.getValue('posstart'), count=arguments.event.getValue('count'));
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryEnrollments">			
			<cfif structKeyExists(application.objSiteInfo.mc_siteInfo,local.qryEnrollments.signuporgcode)>
				<cfset local.siteHostName = application.objSiteInfo.getSiteInfo(local.qryEnrollments.signuporgcode).mainHostName>
			<cfelse>
				<cfset local.siteHostName = "">
			</cfif>	
			<cfset arrayAppend(local.data, {
				"isactive": local.qryEnrollments.isActive,
				"sitehostname": local.siteHostName,
				"memberid": local.qryEnrollments.memberID,
				"firstname":local.qryEnrollments.firstname,
				"lastname": local.qryEnrollments.lastname,
				"membernumber": local.qryEnrollments.memberNumber,
				"company": local.qryEnrollments.company,
				"programformat": local.qryEnrollments.programFormat,
				"seminarid": local.qryEnrollments.seminarId,
				"seminarname": local.qryEnrollments.seminarName,
				"dateenrolled": dateFormat(local.qryEnrollments.dateenrolled,"m/d/yyyy"),
				"signuporgcode": local.qryEnrollments.signUpOrgCode,
				"publisherorgcode": local.qryEnrollments.publisherOrgCode,
				"isnatle": local.qryEnrollments.isNATLE,
				"DT_RowId": "RegRow_#local.qryEnrollments.enrollmentID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEnrollments.totalCount),
			"recordsFiltered": val(local.qryEnrollments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSWAuditLogs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrActorMemberID = arrayNew(1)>

		<cfif val(arguments.event.getValue('fSiteAdminActionsOnly',0))>
			<cfquery name="local.qryAdminMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				DECLARE @siteAdminGroupID int;			
				SELECT @siteAdminGroupID = groupID FROM dbo.ams_groups WHERE orgID = @orgID AND groupCode = 'SiteAdmins';
	
				SELECT DISTINCT m.activeMemberID
				FROM dbo.ams_members AS m 
				INNER JOIN dbo.cache_members_groups as mg ON mg.orgID = @orgID
					AND mg.groupID = @siteAdminGroupID
					AND mg.memberID = m.memberID
				WHERE m.orgID = @orgID
				AND m.[status] IN ('A','I');

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.arrActorMemberID = valueArray(local.qryAdminMembers.activeMemberID)>
		</cfif>

		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getSeminarWebAuditLogs(
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			keywords=arguments.event.getValue('fDescription',''),
			dateFrom=arguments.event.getValue('fDateFrom',''),
			dateTo=arguments.event.getValue('fDateTo',''),
			arrActorMemberID=local.arrActorMemberID,
			limit=50
		).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>
			
			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getSWLMaterialDocs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<!--- Process reorder data if provided --->
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE dbo.tblSeminarsAndDocuments
					SET documentOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder#">
					WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pid')#">
					AND documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT sd.seminarDocumentID, sd.documentID, sd.documentOrder, dl.docTitle, dv.fileName, dv.fileExt
			FROM seminarWeb.dbo.tblSeminarsAndDocuments as sd
			INNER JOIN dbo.cms_documents as d ON sd.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
			INNER JOIN dbo.cms_siteResources as sr on sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#"> AND sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
			WHERE sd.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pid')#">
			ORDER BY sd.documentOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qryDocuments.recordCount>
			<cfloop query="local.qryDocuments">
				<cfset local.tmpStr = {
					"documentid": local.qryDocuments.documentID,
					"doctitle": local.qryDocuments.docTitle,
					"doctitleenc": encodeForHTMLAttribute(local.qryDocuments.docTitle),
					"filename": local.qryDocuments.fileName,
					"filenameenc": encodeForHTMLAttribute(local.qryDocuments.fileName),
					"fileext": local.qryDocuments.fileExt,
					"columnid": local.qryDocuments.documentOrder,
					"DT_RowId": "SWLMaterialDoc_#local.qryDocuments.seminarDocumentID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": local.qryDocuments.recordCount,
			"recordsFiltered": local.qryDocuments.recordCount,
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getTitleFilesInfo" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>
		
		<cfstoredproc procedure="sw_getTitleFiles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('titleID')#">
			<cfprocresult name="local.qryTitleFiles" resultset="1">
		</cfstoredproc>

		<cfset local.data = []>
		<cfloop query="local.qryTitleFiles">
			<cfset local.streamingExt = ''>
			<cfset local.friendlyTime = "">
			<cfset local.pgDesc = "">
			<cfif ArrayLen(XMLSearch(local.qryTitleFiles.formatsAvailable, "/formats/format[@accesstype='S']")) GT 0>
				<cfset local.streamingExt = XMLSearch(local.qryTitleFiles.formatsAvailable, "/formats/format[@accesstype='S']")[1].xmlAttributes.ext>
				<cfset local.durationInSeconds = val(local.qryTitleFiles.duration)>
				<cfif local.durationInSeconds gt 0>
					<cfset local.hours = int(local.durationInSeconds / 3600)>
					<cfset local.minutes = int((local.durationInSeconds mod 3600) / 60)>
					<cfset local.seconds = local.durationInSeconds mod 60>

					<!--- Build the friendly time string compactly --->
					<cfset local.friendlyTime = trim(
						(local.hours ? numberformat(local.hours,'00') & ":" : "") & 
						(local.minutes ? numberformat(local.minutes,'00') & ":" : "") & 
						numberformat(local.seconds,'00')
					)>
				<cfelse>
					<cfset local.friendlyTime = "N/A">
				</cfif>
			</cfif>

			<!--- Check if there's a result for downloadable formats --->
			<cfset local.downloadableExt = ''>
			<cfif ArrayLen(XMLSearch(local.qryTitleFiles.formatsAvailable, "/formats/format[@accesstype='D']")) GT 0>
				<cfset local.dFormats = XMLSearch(local.qryTitleFiles.formatsAvailable, "/formats/format[@accesstype='D']")>
				<cfset local.downloadableFileName = ''>
				<cfset local.downloadableFileName = local.dFormats[1].xmlAttributes.ext>
				<cfif local.downloadableFileName NEQ ''>
					<!--- Extract file extension from filename --->
					<cfset local.downloadableExt = ListLast(local.downloadableFileName, ".")>
				</cfif>
				
				<cfloop from="1" to="#arraylen(local.dFormats)#" index="local.dEl">
					<cfif local.dFormats[local.dEl].xmlAttributes.ext eq "pvr">
						<cfif arraylen(local.dFormats[local.dEl].page) lte 4>
							<cfset local.numInColumn = arraylen(local.dFormats[local.dEl].page)>
						<cfelse>
							<cfset local.numInColumn = arraylen(local.dFormats[local.dEl].page) \ 5>
						</cfif>
						<cfset local.pgDesc = "<table cellpadding='4' cellspacing='3'><tr valign='top'><td>">
						<cfloop from="1" to="#arraylen(local.dFormats[local.dEl].page)#" index="local.dElpEl">
							<cfset local.pgDesc = local.pgDesc & "#local.dFormats[local.dEl].page[local.dElpEl].xmlAttributes.pg#<br/>">
							<cfif local.numInColumn gt 0 and local.dElpEl mod numInColumn is 0>
								<cfset local.pgDesc = local.pgDesc & "</td><td>">
							</cfif>
						</cfloop>
						<cfset local.pgDesc = local.pgDesc & "</td></tr></table>">
						<cfset local.pgDesc = encodeForHTMLAttribute("<b>Pages in this paper:</b><br/>#trim(local.pgDesc)#")>
					</cfif>
				</cfloop>
			</cfif>
			
			<cfif local.streamingExt NEQ ''>
				<cfset local.extension = local.streamingExt>
			<cfelse>
				<cfset local.extension = local.downloadableExt>
			</cfif>
			<cfset local.filemode = "">
			<cfif ListFindNoCase("mp3,mp4", local.extension)>
				<cfset local.filemode =  "stream">
			<cfelseif ListFindNoCase("pdf,doc,docx,ppt,pptx,xls,xlsx", local.extension)>
				<cfset local.filemode =  "download">
			<cfelseif ListFindNoCase("pvr", local.extension)>
				<cfset local.filemode = "paper">
			</cfif>
			
			<cfset local.tmpStr = {
				"fileID": local.qryTitleFiles.fileID,
				"fileName": local.qryTitleFiles.fileName,
				"titleID": arguments.event.getValue('titleID'),
				"fileTitle": encodeForHTMLAttribute(local.qryTitleFiles.fileTitle),
				"fileType": local.qryTitleFiles.fileType,
				"filemode": local.filemode,
				"pgDesc": local.pgDesc,
				"isDownloadable": local.qryTitleFiles.isDownloadable IS 1,
				"isDefaultStream": local.qryTitleFiles.isDefaultStream IS 1,
				"isDefaultPaper": local.qryTitleFiles.isDefaultPaper IS 1,
				"isSupportingDoc": local.qryTitleFiles.isSupportingDoc IS 1,
				"previewPct": local.qryTitleFiles.previewPct,
				"participantID": local.qryTitleFiles.participantID,
				"hasVideoPreview": local.qryTitleFiles.hasVideoPreview,
				"streamingExt": local.streamingExt,				/*REQUIRED?????*/
				"downloadableExt": local.downloadableExt,
				"duration": local.friendlyTime,
				"DT_RowId": "tf_field_#local.qryTitleFiles.fileID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"fileIDList":valueList(local.qryTitleFiles.fileID),
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryTitleFiles.recordCount),
			"recordsFiltered": val(local.qryTitleFiles.recordCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramObjectivesList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfset local.programID = arguments.event.getValue('programID', 0)>
		<cfset local.programType = arguments.event.getValue('programType', '')>

		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
					UPDATE dbo.tblLearningObjectives
					SET objectiveOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					WHERE objectiveID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
					<cfif listFindNoCase("SWL,SWOD", local.programType)>
						AND seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
					<cfelseif local.programType EQ 'SWB'>
						AND bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
					</cfif>
				</cfquery>
			</cfloop>
		</cfif>

		<cfset local.qryProgramObjectives = CreateObject("component","model.seminarweb.SWCommon").getProgramObjectives(programType=#local.programType#, programID=#local.programID#)>)>
		<cfif listFindNoCase("SWL,SWOD",local.programType)>
			<cfset local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isSeminarLocked(seminarID=local.programID)>
		<cfelseif local.programType eq "SWB">
			<cfset local.isSWProgramLocked = CreateObject("seminarWebSWCommon").isBundleProgramLocked(bundleID=local.programID)>
		</cfif>
	
		<cfset local.arrData = []>
		<cfloop query="local.qryProgramObjectives">
			
			<cfset local.arrData.append({
				"objectiveid": local.qryProgramObjectives.objectiveID,
				"objective": htmleditformat(local.qryProgramObjectives.objective),
				"isreadonly": arguments.event.getValue('isReadOnly', 0),
				"isSWProgramLocked": local.isSWProgramLocked,
				"DT_RowId": "row_#local.qryProgramObjectives.objectiveID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryProgramObjectives.recordCount,
			"recordsFiltered": local.qryProgramObjectives.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>